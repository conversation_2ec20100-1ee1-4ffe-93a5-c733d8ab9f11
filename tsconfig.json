{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "types": ["vite/client", "@testing-library/jest-dom", "node"], "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "baseUrl": "src", "paths": {"@feat-auth/*": ["features/auth/*"], "@feat-availability/*": ["features/availability/*"], "@feat-caregivers/*": ["features/caregivers/*"], "@feat-clients/*": ["features/clients/*"], "@feat-scheduling/*": ["features/scheduling/*"], "@feat-service-requests/*": ["features/serviceRequests/*"], "@feat-shifts/*": ["features/shifts/*"], "@feat-users/*": ["features/users/*"], "@web-push/*": ["features/web-push/*"], "@context/*": ["context/*"], "@lib/*": ["lib/*"], "@hooks/*": ["hooks/*"], "@api/*": ["api/*"], "@pages/*": ["pages/*"], "@app/*": ["*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}