/// <reference types="vitest" />
import { sentryVitePlugin } from '@sentry/vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
// https://vitejs.dev/config/

// const APP_ENV = process.env.APP_ENV;

export default defineConfig(({ mode }) => {
  return {
    plugins: getPlugins(mode),
    base: '/',
    build: {
      chunkSizeWarningLimit: 3000,
      sourcemap: true,
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './setupTests.jsx',

      alias: {
        flatpickr: 'flatpickr',
      },
    },
    server: {
      port: 3101,
    },
    preview: {
      port: 3130,
    },
  };
});

function getPlugins(mode: string) {
  const plugins = [react(), tsconfigPaths(), tailwindcss()];

  if (mode === 'production') {
    const authToken = process.env.SENTRY_AUTH_TOKEN;
    plugins.push(
      sentryVitePlugin({
        authToken,
        org: 'konnecta-systems-ike',
        project: 'homecare-dashboard',
      })
    );
  }
  return plugins;
}
