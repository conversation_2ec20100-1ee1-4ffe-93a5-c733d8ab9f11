/// <reference types="vitest" />
import { sentryVitePlugin } from '@sentry/vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
// https://vitejs.dev/config/

// const APP_ENV = process.env.APP_ENV;

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: getPlugins(mode),
    base: '/',
    build: {
      chunkSizeWarningLimit: 3000,
      sourcemap: true,
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './setupTests.jsx',

      alias: {
        flatpickr: 'flatpickr',
      },
    },
    server: {
      port: Number(env.VITE_DEV_APP_PORT) || 3100,
    },
    preview: {
      port: 3130,
    },
  };
});

function getPlugins(mode) {
  const plugins = [react(), tsconfigPaths(), tailwindcss()];

  if (mode === 'production') {
    const authToken = process.env.SENTRY_AUTH_TOKEN;
    plugins.push(
      sentryVitePlugin({
        authToken,
        org: 'konnecta-systems-ike',
        project: 'homecare-dashboard',
      })
    );
  }
  return plugins;
}
