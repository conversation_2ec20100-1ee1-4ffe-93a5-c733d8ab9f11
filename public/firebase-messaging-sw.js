console.log('SERVICE WOERKER FB STARTED');

importScripts('https://cdn.jsdelivr.net/npm/idb-keyval@6/dist/umd.js');
importScripts('https://www.gstatic.com/firebasejs/9.10.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.10.0/firebase-messaging-compat.js');

const NOTIFICATION_KEY = 'notification';
const img = `${self.location.origin}/media/logos/logo_light.png`;
console.log('img', img);

self.addEventListener('notificationclick', (event) => {
  console.log('Clicknotifyicaiton', event);
  const notificationData = event.notification || {};

  const notificationPayload = {
    title: notificationData.title || 'N/A',
    body: notificationData.body || 'N/A',
  };
  // Open the URL in the default browser.
  event.waitUntil(routing(notificationPayload, event));
});

function routing(notificationData, event) {
  return clients.matchAll({ includeUncontrolled: true, type: 'window' }).then(async (windowClients) => {
    console.log('clients', { clients, windowClients });
    // Check if there is already a window/tab open with the target URL

    const postMessage = {
      type: 'NOTIFICATION_CLICK',
      ...notificationData,
    };

    console.log({ postMessage });
    const lastClient = windowClients.length > 0 && windowClients[windowClients.length - 1];
    console.log({ lastClient });

    if (lastClient) {
      console.log('inside lastClient', { lastClient });
      lastClient.focus();
      lastClient.postMessage({
        ...postMessage,
        msg: 'from background - foreground - tab is there but not focused',
      });

      event.notification.close();
      return Promise.resolve(true);
    }

    //Opening new window
    await saveNotifToDb(postMessage);
    // const newWindow = await clients.openWindow(urlToOpen + '#closed');
    clients.openWindow('/dashboard').then((client) => {
      return Promise.resolve(true);
    });

    event.notification.close();
    return Promise.resolve(true);
  });
}
async function saveNotifToDb(notif) {
  console.log('saving notification to db', notif);
  return await idbKeyval.set(NOTIFICATION_KEY, notif);
}

const firebaseConfig = {
  apiKey: 'AIzaSyClHnd6BzHefbhJg-arg6yqsP9mb39p4b8',
  authDomain: 'homecare-2498a.firebaseapp.com',
  projectId: 'homecare-2498a',
  storageBucket: 'homecare-2498a.firebasestorage.app',
  messagingSenderId: '1063363203978',
  appId: '1:1063363203978:web:ee1a9263e338d61fa6ae73',
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

// messaging.onBackgroundMessage((payload) => {
//   console.log('Received background message: ', payload);

//   const notificationTitle = payload.notification.title;
//   const notificationOptions = {
//     body: payload.notification.body,
//     tag: notificationTitle, // tag is added to ovverride the notification with latest update
//     icon: img,
//     data: { url: '/dashboard/notifications#test-push-background' },
//   };
//   self.registration.showNotification(notificationTitle, notificationOptions);
// });
self.addEventListener('push', (event) => {
  console.log('Received push event', event);
  if (event.data) {
    // console.log('Push data received', event.data.text());
    const payload = event.data.json();
    console.log('Push data received', payload);
    if ((!'data') in payload) {
      console.error('No data in payload. Wrong notification schema', payload);
      return;
    }

    const notificationData = payload.data;
    const title = notificationData.title || 'N/A';
    const body = notificationData.body || 'N/A';

    const notificationTitle = title;
    const notificationOptions = {
      body: body,
      icon: img,
      // renotify: true,
      // data: JSON.parse(payload.data.action),
    };
    console.log('Generating notification', notificationOptions);
    event.waitUntil(self.registration.showNotification(notificationTitle, notificationOptions));
  }
});
