import { createRoot } from 'react-dom/client';
import './styles/styles.css';

// import './Sentry';

import AppRoutes from '@app/routes/AppRoutes';
import { ApiInterceptorProvider } from '@context/api/ApiInterceptorProvider';
import { AuthProvider } from '@context/auth/AuthProvider';
import { AuthProvider as OidcProvider } from 'react-oidc-context';
import { createBrowserRouter, RouteObject, RouterProvider } from 'react-router-dom';

import { ProtectedWrapper } from '@app/features/oidc/components/ProtectedWrapper';
import oidcConfig from '@app/features/oidc/oidc.config';
import envConfig from './enviroment/enviroment';

const container = document.getElementById('root');

const SENTRY_DNS = envConfig.getEnvKey('SENTRY_DNS');
const SENTRY_ENVIROMENT = envConfig.getEnvKey('SENTRY_ENVIROMENT');
//if sentry dns then its not dev so we need to hide all the logs
if (SENTRY_DNS && SENTRY_ENVIROMENT !== 'local-dev') {
  window.console.log = function () {};
  window.console.debug = function () {};
  window.console.warn = function () {};
  window.console.info = function () {};
}

if (container) {
  const routes: RouteObject[] = [
    {
      path: '/*',
      element: (
        <ProtectedWrapper>
          <AuthProvider>
            <ApiInterceptorProvider>
              <AppRoutes />
            </ApiInterceptorProvider>
          </AuthProvider>
        </ProtectedWrapper>
      ),
    },
  ];
  const router = createBrowserRouter(routes);

  if (import.meta.hot) {
    // import.meta.hot.dispose(() => router.dispose());
  }
  createRoot(container).render(
    <OidcProvider {...oidcConfig}>
      <RouterProvider router={router} />
    </OidcProvider>
  );
}
