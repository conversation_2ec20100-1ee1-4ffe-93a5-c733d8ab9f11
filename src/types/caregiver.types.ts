import { Service } from '@app/types/service.types';
import { Address, Gender } from './common.types';

export const CareGiverTypes = ['Doctor', 'Nurse', 'ElderlyCare', 'PhysicalTherapist'] as const;

export type CaregiverType = (typeof CareGiverTypes)[number];

export type CareGiverOption = { type: CaregiverType; label: string };
export const caregiverOptions: CareGiverOption[] = [
  { type: 'Doctor', label: 'Doctor' },
  { type: 'Nurse', label: 'Nurse' },
  { type: 'ElderlyCare', label: 'Elderly Care' },
  { type: 'PhysicalTherapist', label: 'Physical Therapist' },
];

export type AvailabilitySchedule = {
  [day: string]: {
    start: string; // e.g., '09:00'
    end: string; // e.g., '17:00'
    slots?: string[]; // optional array of time slots
  } | null;
};

export type Caregiver = {
  id: string;
  type: CaregiverType;
  firstName: string;
  lastName: string;
  fullName: string;
  dateOfBirth?: Date;
  username: string;
  passwordHash: string;
  gender?: Gender;
  nationalId?: string;
  phone?: string;
  email: string;
  baseAddress?: Address;
  coverageAreas?: string[];
  travelRadius?: number; // in km
  certifications?: string[];
  skills?: string[];
  specialties?: string[]; // relevant for doctors
  availability?: AvailabilitySchedule;
  languagesSpoken?: string[];
  rating?: number;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
  services: Service[];
};
