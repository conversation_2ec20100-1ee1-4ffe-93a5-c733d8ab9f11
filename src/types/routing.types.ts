import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Y_PAGE,
  <PERSON><PERSON><PERSON>VERS_LIST,
  CARE<PERSON>VERS_PREFIX,
  CLIENT_PAGE,
  CLIENTS_LIST,
  DASHBOARD_PAGE,
  DOCTOR_LIST,
  DOCTOR_PAGE,
  EDIT_CAREGIVER,
  EDIT_CLIENT,
  EDIT_DOCTOR,
  EDIT_USER,
  NEW_CAREGIVER,
  NEW_CLIENT,
  NEW_DOCTOR,
  NEW_USER,
  SCHEDULING_PAGE,
  SERVICE_PAGE,
  SERVICE_REQUESTS_MANAGEMENT_PAGE,
  SERVICE_REQUESTS_MANAGEMENT_PAGE_LIST,
  SERVICE_REQUESTS_PAGE,
  SERVICE_TYPE_PAGE,
  SHIFTS_PAGE,
  USERS_LIST,
  USERS_PREFIX,
  VISITS_PAGE,
} from '@app/routes/urls';
import { FC } from 'react';
import { IconType } from 'react-icons/lib';
import { EDIT_SERVICE, NEW_SERVICE, SERVICE_LIST } from './../routes/urls';
export type IconVariant = {
  filled: IconType;
  outline: IconType;
};
export interface MenuItem {
  label: string;
  key: string;
  show?: boolean;
  icon?: IconVariant;
  children?: MenuItem[];
}
// Route can either be a JSX.Element or a React Component that returns JSX.Element
export type RouteDefinition = FC;

// Define the Routes Object

export type Route = SingleRoute | MoreRoutes;
export type RouteBase = {
  show: boolean;
  full_path: string;
};
export type NestedRoutes = { [routeKey: string]: Route };

// Route with a component (without nested routes)
export type SingleRoute = RouteBase & {
  route: RouteDefinition;
  routes?: NestedRoutes;
};
// Route with nested routes (without a component)
export type MoreRoutes = RouteBase & {
  routes: NestedRoutes;
  route?: RouteDefinition;
};

export type RoutesStructure = {
  // [AUTH_PREFIX]: {
  //   full_path: string;
  //   show: boolean;
  //   routes: {
  //     [LOGIN]: Route;
  //     [RESET_PWD]: Route;
  //     [SET_PWD]: Route;
  //     [AUTH_CALLBACK]: Route;
  //   };
  // };
  [DASHBOARD_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [CAREGIVERS_PREFIX]: {
    full_path: string;
    show: boolean;
    routes: {
      [CAREGIVERS_LIST]: Route;
      [NEW_CAREGIVER]: Route;
      [EDIT_CAREGIVER]: Route;
    };
  };
  [CLIENT_PAGE]: {
    full_path: string;
    show: boolean;
    routes: {
      [CLIENTS_LIST]: Route;
      [NEW_CLIENT]: Route;
      [EDIT_CLIENT]: Route;
    };
  };
  [USERS_PREFIX]: {
    full_path: string;
    show: boolean;
    routes: {
      [USERS_LIST]: Route;
      [NEW_USER]: Route;
      [EDIT_USER]: Route;
    };
  };
  [DOCTOR_PAGE]: {
    full_path: string;
    show: boolean;
    routes: {
      [DOCTOR_LIST]: Route;
      [NEW_DOCTOR]: Route;
      [EDIT_DOCTOR]: Route;
    };
  };
  [AVAILABILITY_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [SERVICE_PAGE]: {
    full_path: string;
    show: boolean;
    routes: {
      [SERVICE_LIST]: Route;
      [NEW_SERVICE]: Route;
      [EDIT_SERVICE]: Route;
    };
  };
  [SERVICE_TYPE_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [SERVICE_REQUESTS_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [SHIFTS_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [SCHEDULING_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [VISITS_PAGE]: {
    full_path: string;
    show: boolean;
    route: RouteDefinition;
  };
  [SERVICE_REQUESTS_MANAGEMENT_PAGE]: {
    full_path: string;
    show: boolean;
    routes: {
      [SERVICE_REQUESTS_MANAGEMENT_PAGE_LIST]: Route;
    };
  };
  // [PROFILE_PREFIX]: {
  //   full_path: string;
  //   show: boolean;
  //   routes: {
  //     [EDIT_PROFILE]: Route;
  //   };
  // };
};

export type MenuStructure = MenuItem[];

// Generic `RoutesStructure` that supports any string keys
export type RoutesStructureWithStringKeys = Record<string, Route>;

// Extract all top-level keys (e.g., 'LEAVE_PREFIX', 'TIMESHEET_PREFIX')
export type TopLevelKeys = keyof RoutesStructure;

// Extract first-level routes (e.g., 'LEAVE_CALENDAR', 'GET_LIST' etc.)
export type FirstLevelRoutes<T extends TopLevelKeys> = RoutesStructure[T] extends { routes: infer R } ? keyof R : never;

// Extract second-level routes (if any exist)
export type SecondLevelRoutes<T extends TopLevelKeys, K extends FirstLevelRoutes<T>> = RoutesStructure[T] extends {
  routes: Record<string, Route>;
}
  ? RoutesStructure[T]['routes'][K] extends { routes: infer R }
    ? keyof R
    : never
  : never;

// Flatten all route keys into one type
export type AllRouteKeys =
  | TopLevelKeys
  | { [T in TopLevelKeys]: FirstLevelRoutes<T> }[TopLevelKeys]
  | {
      [T in TopLevelKeys]: {
        [K in FirstLevelRoutes<T>]: SecondLevelRoutes<T, K>;
      }[FirstLevelRoutes<T>];
    }[TopLevelKeys];
