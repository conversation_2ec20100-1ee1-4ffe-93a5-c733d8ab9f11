import type { RegisterOptions } from 'react-hook-form';

type FieldType =
  | 'text'
  | 'email'
  | 'password'
  | 'date'
  | 'select'
  | 'multiple-select'
  | 'checkbox'
  | 'radiogroup'
  | 'number'
  | 'textarea'
  | 'list'
  | 'custom';

type Option = {
  label: string;
  value: string | number;
};

export type FieldConfig<TValue> = {
  name: string;
  label: string;
  type: FieldType;
  rules?: RegisterOptions;
  width?: 'third' | 'half' | 'full';
  options?: Option[]; // For select or checkbox groups
  component?: (props: { value: TValue; onChange: (val: TValue) => void; error?: string }) => React.ReactNode;
};

export type FieldGroup = {
  title?: string; // Optional section header
  description?: string; // Optional tooltip/help
  fields: FieldConfig<unknown>[];
};
