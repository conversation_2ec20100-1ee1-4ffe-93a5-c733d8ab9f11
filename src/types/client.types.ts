export type GeoLocation = {
  lat?: number;
  lng?: number;
};

export type Contact = {
  name?: string;
  relationship?: string;
  phone?: string;
};

export type Client = {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  gender?: 'Male' | 'Female' | 'Other';
  nationalId?: string;
  phone?: string;
  email?: string;
  username: string;
  passwordHash: string;
  address?: {
    street?: string;
    city?: string;
    zip?: string;
    geoLocation?: GeoLocation;
  };
  medicalHistory?: string;
  medications?: string[];
  allergies?: string[];
  mobilityNotes?: string;
  emergencyContact?: Contact;
  secondaryContact?: Contact;
  preferredLanguage?: string;
  favoriteProviders?: string[]; // Caregiver IDs
  notes?: string;
  roles: ['client'];
  active?: boolean; // Default true
  createdAt: Date;
  updatedAt: Date;
};
