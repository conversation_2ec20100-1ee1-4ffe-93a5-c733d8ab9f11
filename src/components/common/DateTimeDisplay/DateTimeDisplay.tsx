import { Typography } from 'antd';
import dayjs from 'dayjs';
import React, { memo } from 'react';

interface DateTimeDisplayProps {
  /** Start date/time */
  startTime?: string | Date;
  /** End date/time (optional) */
  endTime?: string | Date;
  /** Show date on first line, time on second line */
  showDateSeparately?: boolean;
  /** Custom date format */
  dateFormat?: string;
  /** Custom time format */
  timeFormat?: string;
  /** Custom className for styling */
  className?: string;
  /** Custom className for date line */
  dateClassName?: string;
  /** Custom className for time line */
  timeClassName?: string;
}

/**
 * DateTimeDisplay Component
 *
 * A reusable component for displaying date and time information with:
 * - Separate date and time lines
 * - Start and end time ranges
 * - Dayjs integration for consistent formatting
 * - Customizable formats and styling
 * - Memoized for performance
 *
 * @example
 * ```tsx
 * <DateTimeDisplay
 *   startTime={visit.startTime}
 *   endTime={visit.endTime}
 *   showDateSeparately
 * />
 * ```
 */
const DateTimeDisplayComponent: React.FC<DateTimeDisplayProps> = ({
  startTime,
  endTime,
  showDateSeparately = true,
  dateFormat = 'MMM DD, YYYY',
  timeFormat = 'HH:mm',
  className = '',
  dateClassName = '',
  timeClassName = '',
}) => {
  // Return dash if no start time
  if (!startTime) {
    return <Typography.Text className={className}>-</Typography.Text>;
  }

  const startDayjs = dayjs(startTime);
  const endDayjs = endTime ? dayjs(endTime) : null;

  // Format date and time
  const dateText = startDayjs.format(dateFormat);
  const startTimeText = startDayjs.format(timeFormat || '');
  const endTimeText = endDayjs ? endDayjs.format(timeFormat || '') : null;

  // Build time range text
  const timeRangeText = endTimeText ? `${startTimeText} - ${endTimeText}` : startTimeText;

  if (showDateSeparately) {
    return (
      <div className={className}>
        <Typography.Text className={`block ${dateClassName}`}>{dateText}</Typography.Text>
        <Typography.Text className={`block ${timeClassName}`}>{timeRangeText}</Typography.Text>
      </div>
    );
  }

  // Single line format
  return (
    <Typography.Text className={className}>
      {dateText} {timeFormat && timeRangeText}
    </Typography.Text>
  );
};

// Memoized component with display name
DateTimeDisplayComponent.displayName = 'DateTimeDisplay';
export const DateTimeDisplay = memo(DateTimeDisplayComponent);

export default DateTimeDisplay;
