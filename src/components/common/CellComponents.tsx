import { App as Antd<PERSON><PERSON>, Button, Tooltip, Typography } from 'antd';
import React from 'react';
import { FiCopy } from 'react-icons/fi';

export interface CellComponentProps {
  value: unknown;
  content?: React.ReactNode;
  copy?: boolean;
  title?: string;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
}

export interface CellComponentConfig {
  component: React.ComponentType<CellComponentProps>;
  getHref?: (value: string) => string;
  getLabel?: (value: string) => string;
}

export const PhoneCell: React.FC<CellComponentProps> = ({ value, content, copy = true, title }) => {
  const { message } = AntdApp.useApp();
  const stringValue = value == null ? '' : String(value);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(stringValue);
      message.success('Copied');
    } catch {
      message.error('Copy failed');
    }
  };

  if (!stringValue) {
    return <span className="text-gray-400">-</span>;
  }

  const cleanedPhone = stringValue.replace(/[^\d+]/g, '');
  const href = `tel:${cleanedPhone}`;

  return (
    <span className="group relative inline-flex items-center gap-1 max-w-full">
      <a
        href={href}
        onClick={(e) => e.stopPropagation()}
        title={title ?? stringValue}
        aria-label={`Call ${stringValue}`}
        className="hover:!underline"
      >
        <Typography.Text>{content ?? stringValue}</Typography.Text>
      </a>
      {copy && <CopyButton value={stringValue} onCopy={handleCopy} />}
    </span>
  );
};

export const EmailCell: React.FC<CellComponentProps> = ({ value, content, copy = true, title }) => {
  const { message } = AntdApp.useApp();
  const stringValue = value == null ? '' : String(value);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(stringValue);
      message.success('Copied');
    } catch {
      message.error('Copy failed');
    }
  };

  if (!stringValue) {
    return <span className="text-gray-400">-</span>;
  }

  const href = `mailto:${stringValue}`;

  return (
    <span className="group relative inline-flex items-center gap-1 max-w-full">
      <a
        href={href}
        onClick={(e) => e.stopPropagation()}
        title={title ?? stringValue}
        aria-label={`Email ${stringValue}`}
        className="hover:!underline"
      >
        <Typography.Text>{content ?? stringValue}</Typography.Text>
      </a>
      {copy && <CopyButton value={stringValue} onCopy={handleCopy} />}
    </span>
  );
};

export const AddressCell: React.FC<CellComponentProps> = ({ value, content, copy = true, title }) => {
  const { message } = AntdApp.useApp();
  const stringValue = value == null ? '' : String(value);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(stringValue);
      message.success('Copied');
    } catch {
      message.error('Copy failed');
    }
  };

  if (!stringValue) {
    return <span className="text-gray-400">-</span>;
  }

  const encodedAddress = encodeURIComponent(stringValue);
  const href = `https://maps.google.com/maps?q=${encodedAddress}`;

  return (
    <span className="group relative inline-flex items-center gap-1 max-w-full">
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        onClick={(e) => e.stopPropagation()}
        className="hover:!underline"
        title={title ?? stringValue}
        aria-label={`Open ${stringValue} in Google Maps`}
      >
        <Typography.Text>{content ?? stringValue}</Typography.Text>
      </a>
      {copy && <CopyButton value={stringValue} onCopy={handleCopy} />}
    </span>
  );
};

export const TextCell: React.FC<CellComponentProps> = ({ value, content, copy = true, title, className }) => {
  const { message } = AntdApp.useApp();
  const stringValue = value == null ? '' : String(value);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(stringValue);
      message.success('Copied');
    } catch {
      message.error('Copy failed');
    }
  };

  return (
    <span className="group relative inline-flex items-center gap-1 max-w-full">
      <span className={`truncate ${className || ''}`} title={title ?? stringValue}>
        {content ?? (stringValue || '-')}
      </span>
      {copy && stringValue && <CopyButton value={stringValue} onCopy={handleCopy} />}
    </span>
  );
};

const CopyButton: React.FC<{ value: string; onCopy: (e: React.MouseEvent) => void }> = ({ onCopy }) => {
  return (
    <Tooltip title="Copy">
      <Button
        type="text"
        size="small"
        onClick={onCopy}
        onMouseDown={(e) => e.stopPropagation()}
        className="opacity-0 group-hover:opacity-100 transition-opacity p-0 h-auto ml-1"
        aria-label="Copy"
      >
        <FiCopy />
      </Button>
    </Tooltip>
  );
};
