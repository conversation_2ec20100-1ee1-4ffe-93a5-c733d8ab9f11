import { Ad<PERSON><PERSON><PERSON>, CellComponentConfig, <PERSON>ail<PERSON>ell, Phone<PERSON><PERSON>, TextCell } from './CellComponents';

export type CellType = 'phone' | 'email' | 'address' | 'text';

export const CELL_TYPE_REGISTRY: Record<CellType, CellComponentConfig> = {
  phone: {
    component: PhoneCell,
    getHref: (value: string) => {
      const cleanedPhone = value.replace(/[^\d+]/g, '');
      return `tel:${cleanedPhone}`;
    },
    getLabel: (value: string) => `Call ${value}`,
  },
  email: {
    component: EmailCell,
    getHref: (value: string) => `mailto:${value}`,
    getLabel: (value: string) => `Email ${value}`,
  },
  address: {
    component: AddressCell,
    getHref: (value: string) => `https://maps.google.com/maps?q=${encodeURIComponent(value)}`,
    getLabel: (value: string) => `Open ${value} in Google Maps`,
  },
  text: {
    component: TextCell,
  },
};

export const getCellComponent = (type: CellType) => {
  return CELL_TYPE_REGISTRY[type];
};

export const isValidCellType = (type: string): type is CellType => {
  return Object.keys(CELL_TYPE_REGISTRY).includes(type);
};
