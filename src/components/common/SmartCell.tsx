import { Typography } from 'antd';
import React, { memo } from 'react';
import { CELL_TYPE_REGISTRY, CellType } from './CellTypeRegistry';

type SmartCellType = CellType;

export interface SmartCellProps {
  value: unknown;
  // Optional pre-rendered content from column-specific renderer
  content?: React.ReactNode;
  // Optional explicit type override
  typeOverride?: SmartCellType;
  // Hide copy affordance
  copy?: boolean;
  // Title/tooltip fallback
  title?: string;
}

function _SmartCell({ value, content, typeOverride, copy = true, title }: SmartCellProps) {
  const cellConfig = CELL_TYPE_REGISTRY[typeOverride || 'text'];
  const CellComponent = cellConfig.component;

  return (
    <Typography.Text>
      <CellComponent value={value} content={content} copy={copy} title={title} />
    </Typography.Text>
  );
}

export const SmartCell = memo(_SmartCell);
