import { Button, Space, Tooltip } from 'antd';
import { MdAdd, Md<PERSON><PERSON>ove } from 'react-icons/md';

type Props = {
  onNudge: (deltaMinutes: number) => void;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
};

export default function NudgeButtons({ onNudge, disabled, size = 'small' }: Props) {
  return (
    <Space size={4} align="center">
      <Tooltip title="-1 hour">
        <Button
          size={size}
          type="text"
          onClick={() => onNudge(-60)}
          disabled={disabled}
          aria-label="Nudge minus one hour"
        >
          <MdRemove />
        </Button>
      </Tooltip>
      <Tooltip title="+1 hour">
        <Button
          size={size}
          type="text"
          onClick={() => onNudge(60)}
          disabled={disabled}
          aria-label="Nudge plus one hour"
        >
          <MdAdd />
        </Button>
      </Tooltip>
    </Space>
  );
}
