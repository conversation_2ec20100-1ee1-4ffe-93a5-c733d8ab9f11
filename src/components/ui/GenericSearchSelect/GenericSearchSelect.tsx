import { SearchSelectV2 } from '@app/components/ui/SearchSelectV2';
import { InputProps, SelectProps } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// Generic types for the API response structure
export interface ApiResponse<T> {
  data: T[];
  total: number;
  offset: number;
}

export interface ApiQueryParams {
  limit: number;
  offset: number;
  query?: string;
}

// Generic API function type
export type ApiFetchFunction<T> = (params: ApiQueryParams) => Promise<{ data: ApiResponse<T> }>;

// Generic item can be any object - we'll extract ID and label via functions
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SelectableItem = Record<string, any>;

type GenericSearchSelectProps<T extends SelectableItem> = {
  // Required props
  apiFetch: ApiFetchFunction<T>;
  getItemId: (item: T) => number | string;
  getItemLabel: (item: T) => string;
  onSelectionChange?: (selection: T | T[] | undefined) => void;

  // Optional customization
  placeholder?: string;
  pageSize?: number;
  minSearchLength?: number;
  searchDebounceMs?: number;
  searchPlaceholder?: string;
  // If true, skip initial load and fetch on first dropdown open only
  lazyLoadOnOpen?: boolean;
  // Custom option rendering
  renderOption?: (item: T) => React.ReactNode;
  // External selected items provider (avoids syncing via useEffect)
  selectedItemsProvider?: () => T | T[] | undefined;
  inputSearchProps?: InputProps;
} & Omit<SelectProps, 'options' | 'onChange' | 'loading' | 'onSearch'>;

export function GenericSearchSelect<T extends SelectableItem>({
  apiFetch,
  getItemId,
  getItemLabel,
  onSelectionChange,
  placeholder = 'Select item',
  pageSize = 10,
  minSearchLength = 3,
  searchDebounceMs = 300,
  searchPlaceholder,
  inputSearchProps,
  renderOption,
  selectedItemsProvider,
  lazyLoadOnOpen = false,
  ...selectProps
}: GenericSearchSelectProps<T>) {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentSearch, setCurrentSearch] = useState('');
  const [currentOffset, setCurrentOffset] = useState(0);
  const [selectedItems, setSelectedItems] = useState<T[]>([]);
  const loadingRef = useRef(false);
  const currentSearchRef = useRef('');
  const didMountReloadRef = useRef(false);

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const didLazyLoadRef = useRef(false);

  useEffect(() => {
    loadingRef.current = loading;
  }, [loading]);
  useEffect(() => {
    currentSearchRef.current = currentSearch;
  }, [currentSearch]);

  const loadItems = useCallback(
    async (search: string = '', offset: number = 0, append: boolean = false) => {
      if (loadingRef.current) return;

      setLoading(true);

      const query: ApiQueryParams = {
        limit: pageSize,
        offset,
      };

      if (search) {
        query.query = search.trim();
      }

      try {
        const response = await apiFetch(query);
        const resp = response.data;

        const newItems = resp.data;
        setItems((prev) => (append ? [...prev, ...newItems] : newItems));
        setTotalCount(resp.total);
        setHasMore(resp.offset + resp.data.length < resp.total);
        setCurrentOffset(resp.offset);
      } catch (error) {
        console.error('Failed to fetch items:', error);
        setHasMore(false);
      } finally {
        setLoading(false);
      }
    },
    [pageSize, apiFetch]
  );

  // Initial load (skip if lazy)
  useEffect(() => {
    if (!lazyLoadOnOpen) {
      loadItems();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lazyLoadOnOpen]);

  useEffect(() => {
    if (!didMountReloadRef.current) {
      didMountReloadRef.current = true;
      return;
    }
    loadItems(currentSearchRef.current, 0, false);
  }, [loadItems]);

  const handleSearch = useCallback(
    (searchTerm: string) => {
      // Clear existing debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // If search term is too short, only update local state but don't search
      if (searchTerm.length > 0 && searchTerm.length < minSearchLength) {
        setCurrentSearch(searchTerm);
        // Don't make API call, just return
        return;
      }

      // Set up debounced search
      debounceTimerRef.current = setTimeout(() => {
        setCurrentSearch(searchTerm);
        setCurrentOffset(0);
        loadItems(searchTerm, 0, false);
      }, searchDebounceMs);
    },
    [loadItems, minSearchLength, searchDebounceMs]
  );

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const handleLoadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextOffset = currentOffset + pageSize;
      loadItems(currentSearch, nextOffset, true);
    }
  }, [loading, hasMore, currentOffset, pageSize, currentSearch, loadItems]);

  // Trigger first load when dropdown opens (lazy mode only)
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (!open) return;
      if (!lazyLoadOnOpen) return;
      if (didLazyLoadRef.current) return;
      didLazyLoadRef.current = true;
      loadItems(currentSearch, 0, false);
    },
    [lazyLoadOnOpen, loadItems, currentSearch]
  );

  const handleChange = useCallback(
    (value: (number | string) | (number | string)[] | undefined) => {
      console.log('handleChange', value);

      // Create combined items list for lookups (avoid duplicates)
      const allItemsMap = new Map<number | string, T>();
      items.forEach((item) => allItemsMap.set(getItemId(item), item));
      selectedItems.forEach((item) => allItemsMap.set(getItemId(item), item));
      const allItems = Array.from(allItemsMap.values());

      if (Array.isArray(value)) {
        // Multiple selection
        const newSelectedItems = allItems.filter((item) => value.includes(getItemId(item)));
        setSelectedItems(newSelectedItems);
        onSelectionChange?.(newSelectedItems);
      } else if (value !== undefined) {
        // Single selection
        const selectedItem = allItems.find((item) => getItemId(item) === value);
        if (selectedItem) {
          setSelectedItems([selectedItem]);
          onSelectionChange?.(selectedItem);
        }
      } else {
        // Clear selection
        setSelectedItems([]);
        onSelectionChange?.(undefined);
      }
    },
    [items, selectedItems, onSelectionChange, getItemId]
  );

  // Transform items to Select options format, ensuring no duplicate keys
  const options = useMemo(() => {
    // Create a Map to track unique items by their ID
    const uniqueItemsMap = new Map<number | string, T>();

    // First add all current API items
    items.forEach((item) => {
      uniqueItemsMap.set(getItemId(item), item);
    });

    // Then add selected items (this ensures selected items are always available)
    // If a selected item is already in the API results, it won't create duplicates
    selectedItems.forEach((selectedItem) => {
      uniqueItemsMap.set(getItemId(selectedItem), selectedItem);
    });
    // Include any externally provided selected items without syncing state
    const ext = selectedItemsProvider?.();
    if (ext) {
      const arr = Array.isArray(ext) ? ext : [ext];
      arr.forEach((it) => uniqueItemsMap.set(getItemId(it), it));
    }

    // Convert Map values back to array and transform to options
    return Array.from(uniqueItemsMap.values()).map((item) => ({
      value: getItemId(item),
      label: getItemLabel(item),
      data: item,
    }));
  }, [items, selectedItems, selectedItemsProvider, getItemId, getItemLabel]);

  const defaultSearchPlaceholder = searchPlaceholder || `Search items... (min ${minSearchLength} characters)`;

  return (
    <SearchSelectV2
      {...selectProps}
      options={options}
      loading={loading}
      hasMore={hasMore}
      totalCount={totalCount}
      currentCount={items.length}
      placeholder={placeholder}
      onSearch={handleSearch}
      onLoadMore={handleLoadMore}
      onOpenChange={handleOpenChange}
      onChange={(val) => {
        console.log('ONCHANGE GENERIC', val);
        handleChange(val);
      }}
      optionRender={(option) => {
        const item = option.data.data as T;
        return renderOption ? renderOption(item) : <span>{getItemLabel(item)}</span>;
      }}
      inputSearchProps={{
        ...inputSearchProps,
        placeholder: inputSearchProps?.placeholder ?? defaultSearchPlaceholder,
      }}
    />
  );
}

export default GenericSearchSelect;
