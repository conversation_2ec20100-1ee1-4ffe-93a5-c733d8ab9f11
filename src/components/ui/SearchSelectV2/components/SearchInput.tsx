import { SearchOutlined } from '@ant-design/icons';
import { Input, InputProps, theme } from 'antd';
import { memo } from 'react';

interface SearchInputProps {
  inputSearchProps?: InputProps;
  onSearch: (value: string) => void;
}

const SearchInput = memo(({ inputSearchProps, onSearch }: SearchInputProps) => {
  const { token } = theme.useToken();

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Prevent backspace and other keys from bubbling up to the Select
    e.stopPropagation();
  };

  return (
    <Input
      {...inputSearchProps}
      onChange={(e) => onSearch(e.target.value)}
      onKeyDown={handleKeyDown}
      allowClear
      onClear={() => onSearch('')}
      prefix={
        <SearchOutlined
          style={{
            color: token.colorIcon,
            fontSize: token.fontSizeSM,
          }}
        />
      }
    />
  );
});

SearchInput.displayName = 'SearchInput';

export default SearchInput;
