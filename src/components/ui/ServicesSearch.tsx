import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import FetchSearchSelect from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { CommonListResponse } from '@app/types/common.types';
import { DefaultOptionType } from 'antd/es/select';
import qs from 'qs';
import { ReactNode, useState } from 'react';

export function ServicessSearch({
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
  query,
}: {
  disabled?: boolean;
  setValue: (v: Service | Service[] | string | undefined) => void;
  value: Service | Service[] | undefined;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: Service) => boolean;
  query?: {
    offset?: number;
    limit?: number;
  };
}) {
  const [data, setData] = useState<CommonListResponse<Service>>({
    Results: [],
    TotalResults: 0,
  });

  const fetchData = async () => {
    const res = await ApiClient.serviceApi.services.getServicesServicesGet(query, {
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
    });
    setData({
      Results: res.data,
      TotalResults: res.data.length,
    });
    return {} as CommonListResponse<Service>;
  };

  return (
    <FetchSearchSelect<Service>
      data={data}
      disabled={disabled}
      value={
        Array.isArray(value)
          ? value.map((v) => `${v.name} ${v.estimatedTimeMinutes} minutes`).join(', ')
          : value
            ? `${value.name} ${value.estimatedTimeMinutes} minutes`
            : undefined
      }
      onValueChange={(item) => setValue(item)}
      getId={(item) => item.serviceId}
      getLabel={(item) => `${item.name} ${item.estimatedTimeMinutes} minutes`}
      fetchData={fetchData}
      placeholder="Select Service"
      optionRender={optionRender}
      optionDisable={optionDisable}
    />
  );
}
