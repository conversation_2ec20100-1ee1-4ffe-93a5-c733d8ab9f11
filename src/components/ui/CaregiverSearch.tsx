import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { CommonListResponse } from '@app/types/common.types';
import { DefaultOptionType } from 'antd/es/select';
import { ReactNode, useEffect, useState } from 'react';
import FetchSearchSelect from './FetchSelect/ApiFetchSelect';
import qs from 'qs';

export function CaregiverSearch({
  // debounce = 500,
  // values,
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
  query,
}: {
  searchTerm?: string;
  debounce?: number;
  disabled: boolean;
  setValue: (e: Caregiver | Caregiver[] | undefined | string) => void;
  value: Caregiver | undefined;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: Caregiver) => boolean;
  query?: {
    offset?: number;
    limit?: number;
    username?: string | null;
    services?: number[] | null;
    active?: boolean | null;
    city?: string | null;
    skills?: string[] | null;
    languages_spoken?: string[] | null;
    rating_min?: number | null;
    rating_max?: number | null;
  };
}) {
  const [data, setData] = useState<CommonListResponse<Caregiver>>({
    Results: [],
    TotalResults: 0,
  });

  const fetchData = async () =>
    // searchValue_?: string, pageSize?: number, pageIndex?: number
    {
      if (disabled) return;

      const res = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet(query, {
        paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
      });
      console.log('HERE', res);
      setData({
        Results: res.data,
        TotalResults: res.data.length,
      });
      return {} as CommonListResponse<Caregiver>;
    };

  useEffect(() => {
    fetchData();
    setValue(undefined);
  }, [query]);

  return (
    <FetchSearchSelect<Caregiver>
      data={data}
      disabled={disabled}
      value={value ? `${value.firstName} ${value.lastName}` : undefined}
      onValueChange={(item) => setValue(item)}
      getId={(item) => item.caregiverId}
      getLabel={(item) => `${item.firstName} ${item.lastName}`}
      fetchData={fetchData}
      placeholder="Select Caregiver"
      optionRender={optionRender}
      optionDisable={optionDisable}
    />
  );
}
