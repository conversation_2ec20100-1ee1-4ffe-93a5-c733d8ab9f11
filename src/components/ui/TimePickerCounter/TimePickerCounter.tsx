import { But<PERSON>, TimePicker } from 'antd';
import { Dayjs } from 'dayjs';
import { MdAdd, MdRemove } from 'react-icons/md';

type DisabledTime = () => {
  disabledHours?: () => number[];
  disabledMinutes?: (hour: number) => number[];
};

type Props = {
  value: Dayjs | null;
  onChange: (time: Dayjs | null) => void;
  onNudge: (deltaMinutes: number) => void;
  disabledTime?: DisabledTime;
  disabled?: boolean;
  className?: string;
};

export default function TimePickerCounter({ value, onChange, onNudge, disabledTime, disabled, className }: Props) {
  return (
    <div className={`relative flex items-center w-[100px] group ${className ?? ''}`}>
      <Button
        className="!absolute -left-8 opacity-0 group-hover:opacity-90 transition-opacity z-10 text-gray-700 hover:text-black w-4 p-0"
        onClick={() => onNudge(-60)}
        aria-label="Decrease by 1 hour"
        disabled={disabled}
        icon={<MdRemove className="text-xs" />}
      />
      <TimePicker
        format="HH:mm"
        minuteStep={15}
        needConfirm={false}
        className="w-full"
        value={value}
        onChange={onChange}
        disabledTime={disabledTime}
      />
      <Button
        className="!absolute -right-8 opacity-0 group-hover:opacity-90 transition-opacity z-10 text-gray-700 hover:text-black w-4 p-0"
        onClick={() => onNudge(60)}
        aria-label="Increase by 1 hour"
        disabled={disabled}
        icon={<MdAdd className="text-xs" />}
      />
    </div>
  );
}
