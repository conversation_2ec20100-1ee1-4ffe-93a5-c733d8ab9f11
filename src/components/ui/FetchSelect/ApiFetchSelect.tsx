import { CommonListResponse } from '@app/types/common.types';
import { DefaultOptionType } from 'antd/es/select';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import { ReactNode, useEffect, useRef, useState } from 'react';
import SearchSelect, { TSearchSelectNewValuesProps } from './SearchSelect';

export type FetchDataFn<T> = (
  searchValue?: string,
  pageSize?: number,
  pageIndex?: number
) => Promise<CommonListResponse<T> | undefined>;

type Props<T> = {
  debounce?: number;
  value: { label: string; value: number | string }[] | string | undefined;
  disabled?: boolean;
  fetchData: FetchDataFn<T>;
  getId: (item: T) => string | number;
  getLabel: (item: T) => string;
  onValueChange?: (item?: T | T[] | string) => void;
  data: CommonListResponse<T>;
  placeholder?: string;
  OptionComponent?: (option: T) => JSX.Element;
  mode?: 'tags' | 'multiple';
  readOnly?: boolean;
  onValueSelect?: (item: T) => void;
  onValueDeselect?: (item: string | number) => void;
  onClear?: () => void;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: T) => boolean;
  canAdd?: boolean;
  style?: React.CSSProperties;
  tagRender?: (props: CustomTagProps) => React.ReactElement; // <-- Add this line
} & TSearchSelectNewValuesProps;

const FetchSearchSelect = <T,>({
  debounce = 500,
  value,
  disabled,
  fetchData,
  tagRender,
  getId,
  getLabel,
  onValueChange,
  data,
  placeholder = 'Select',
  OptionComponent,
  mode,
  readOnly = false,
  onValueSelect,
  onValueDeselect,
  onClear,
  optionRender,
  optionDisable,
  allowNewValue,
  newValueText,
  newValueCb = () => {},
  style = {},
}: Props<T>) => {
  const [response, setResponse] = useState<CommonListResponse<T>>({
    Results: [],
    TotalResults: 0,
  });

  const ajaxSize = 10;

  const handleScrollSetData = async (searchValue: string, ajaxSize: number, ajaxIndex: number) => {
    const res = await fetchData(searchValue, ajaxSize, ajaxIndex);
    if (res) {
      setResponse((d) => ({
        TotalResults: res?.TotalResults ?? 0,
        Results: [...d.Results, ...(res?.Results ?? [])],
      }));
    }
  };

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const onSearchChange = (e: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(async () => {
      const res = await fetchData(e, ajaxSize, 0);
      if (res) {
        setResponse(res);
      }
    }, debounce);
  };

  useEffect(() => {
    setResponse(data);
  }, [data]);

  return (
    <SearchSelect<T>
      tagRender={tagRender}
      disabled={disabled}
      ajaxSize={ajaxSize}
      data={response}
      value={value}
      onValueChange={onValueChange}
      onSearchChange={onSearchChange}
      handleScrollSetData={handleScrollSetData}
      getId={getId}
      getLabel={getLabel}
      placeholder={placeholder}
      OptionComponent={OptionComponent}
      mode={mode}
      readOnly={readOnly}
      onValueSelect={onValueSelect}
      onValueDeselect={onValueDeselect}
      onClear={onClear}
      allowNewValue={allowNewValue}
      newValueCb={newValueCb}
      newValueText={newValueText}
      optionRender={optionRender}
      optionDisable={optionDisable}
      style={style}
    />
  );
};

export default FetchSearchSelect;
