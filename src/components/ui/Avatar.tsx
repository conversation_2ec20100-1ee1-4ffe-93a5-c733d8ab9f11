import { isLightColour } from '@app/utils/colourFunctions';
import { hashCode, intToRGB } from '@app/utils/generateColors';
import { Avatar, Tooltip } from 'antd';
import { AvatarSize } from 'antd/es/avatar/AvatarContext';
import { useEffect, useState } from 'react';

type Props = {
  fullName: string;
  doubleLetters: string[];
  size?: AvatarSize;
  isSelected?: boolean;
  imageUrl?: string;
  personId: number;
};

const AppAvatar = ({ fullName, doubleLetters, size = 'default', isSelected = false, personId }: Props) => {
  const [imageUrl, setImageUrl] = useState('');
  useEffect(() => {
    //   const loadImages = async () => {
    //     const blob = await getEmpPhoto(personId as number);
    //     if (blob) setImageUrl(URL.createObjectURL(blob));
    //   };
    //   loadImages();

    console.log(personId);
    setImageUrl('');
  }, []);
  return (
    <Tooltip title={fullName}>
      {isSelected ? (
        imageUrl ? (
          <img src={imageUrl} className="h-full w-full rounded-full" />
        ) : (
          <Avatar
            className="border-solid border-primary border-2 "
            size={size}
            style={{
              backgroundColor: fullName ? '#' + intToRGB(hashCode(fullName)) : '',
              color: fullName && isLightColour('#' + intToRGB(hashCode(fullName))) ? 'black' : 'white',
            }}
            src={imageUrl}
            shape="circle"
          >
            {!imageUrl && (
              <>
                {doubleLetters[0]}
                {doubleLetters[1]}
              </>
            )}
          </Avatar>
        )
      ) : (
        'me'
      )}
    </Tooltip>
  );
};

export default AppAvatar;
