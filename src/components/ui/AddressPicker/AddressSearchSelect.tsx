import {
  getSuggestions,
  ParsedAddress,
  resetPlacesSession,
  resolveSuggestionToAddress,
} from '@app/components/ui/AddressPicker/google.api';
import { SearchSelectV2 } from '@app/components/ui/SearchSelectV2';
import { useCallback, useEffect, useState } from 'react';
import { AddressValue } from './types';

interface AddressSearchSelectProps {
  disabled?: boolean;
  setValue: (address: AddressValue | undefined) => void;
  value: AddressValue | undefined;
  style?: React.CSSProperties;
  placeholder?: string;
  noDataText?: string;
  onAddressSelect?: (address: ParsedAddress) => void; // New callback for address selection
}

interface GoogleSuggestion {
  placePrediction: google.maps.places.PlacePrediction | null;
}

/**
 * Address search component using Google Maps JavaScript SDK
 * Follows the same pattern as PatientSearch but for addresses
 */
export function AddressSearchSelect({
  disabled = false,
  setValue,
  value,
  style,
  placeholder = 'Search address...',
  noDataText = 'No addresses found',
  onAddressSelect,
}: AddressSearchSelectProps) {
  const [suggestions, setSuggestions] = useState<GoogleSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  /**
   * Convert ParsedAddress to AddressValue format
   * Includes lat/lng extraction as requested
   */
  const convertToAddressValue = (parsed: ParsedAddress): AddressValue => {
    return {
      address: parsed.formattedAddress,
      city: parsed.city || '',
      country: parsed.country || '',
      zip: parsed.postalCode || '',
      lat: parsed.location.lat, // ✅ Requirement 0: Extract lat
      lng: parsed.location.lng, // ✅ Requirement 0: Extract lng
    };
  };

  /**
   * Fetch address suggestions using Google Maps JavaScript SDK
   */
  const handleSearch = useCallback(async (searchValue: string) => {
    try {
      if (!searchValue || searchValue.length < 3) {
        setSuggestions([]);
        setTotalCount(0);
        return;
      }

      setLoading(true);
      console.log('🔍 Fetching address suggestions for:', searchValue);

      const results = await getSuggestions(searchValue.trim());
      console.log('📍 Address suggestions response:', results);

      if (results && results.length > 0) {
        setSuggestions(results);
        setTotalCount(results.length);
      } else {
        setSuggestions([]);
        setTotalCount(0);
      }
    } catch (err) {
      console.error('Error fetching addresses:', err);
      setSuggestions([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Handle address selection
   * ✅ Requirement 3: Fill out client props (city, zip, lat, lng)
   */
  const handleChange = async (value?: string | string[]) => {
    if (typeof value === 'string' && value) {
      // Find the selected suggestion
      const selectedSuggestion = suggestions.find((suggestion) => suggestion.placePrediction?.placeId === value);

      if (selectedSuggestion) {
        try {
          console.log('🏠 Resolving selected suggestion:', selectedSuggestion);

          const parsedAddress = await resolveSuggestionToAddress(selectedSuggestion);
          console.log({ parsedAddress });

          const addressValue = convertToAddressValue(parsedAddress);

          console.log('✅ Created address value:', addressValue);
          console.log('📊 Address breakdown:');
          console.log('  - Street:', parsedAddress.street);
          console.log('  - City:', parsedAddress.city);
          console.log('  - State:', parsedAddress.state);
          console.log('  - Country:', parsedAddress.country);
          console.log('  - ZIP:', parsedAddress.postalCode);
          console.log('  - Lat:', parsedAddress.location.lat);
          console.log('  - Lng:', parsedAddress.location.lng);
          console.log('  - Has street address:', parsedAddress.hasStreetAddress);
          console.log('  - Is complete address:', parsedAddress.isCompleteAddress);

          setValue(addressValue);

          // ✅ Requirement 3: Call callback with parsed address for client props
          if (onAddressSelect) {
            onAddressSelect(parsedAddress);
          }

          // Reset session after selection for billing best practices
          resetPlacesSession();
        } catch (error) {
          console.error('Error resolving address:', error);
          setValue(undefined);
        }
      }
    } else {
      setValue(undefined);
    }
  };

  // Convert suggestions to options format
  const options = suggestions.map((suggestion) => {
    const prediction = suggestion.placePrediction;
    const placeId = prediction?.placeId || '';
    const mainText = prediction?.text?.text || '';

    return {
      value: placeId,
      label: mainText,
      data: suggestion,
    };
  });

  // Test function to verify SDK is working
  useEffect(() => {
    console.log('AddressSearchSelect mounted, testing Google Maps SDK...');
    (async () => {
      try {
        const resp = await getSuggestions('Κορυτ');
        console.log('✅ Google Maps SDK working, predictions:', resp);
      } catch (error) {
        console.error('❌ Google Maps SDK error:', error);
      }
    })();
  }, []);

  return (
    <SearchSelectV2
      style={style}
      disabled={disabled}
      value={value ? value.address : undefined}
      onChange={handleChange}
      options={options}
      placeholder={placeholder}
      loading={loading}
      hasMore={false} // Google Places doesn't support pagination
      totalCount={totalCount}
      currentCount={suggestions.length}
      onSearch={handleSearch}
      showSearch={false}
      filterOption={false}
      notFoundContent={suggestions.length === 0 && !loading ? noDataText : undefined}
      inputSearchProps={{
        placeholder: 'Type to search addresses...',
      }}
      optionRender={(option) => {
        const suggestion = option.data;
        const prediction = suggestion.placePrediction;
        const mainText = prediction?.text?.text || '';
        const types = prediction?.types || [];
        console.log({ suggestion, prediction, mainText, types });
        return (
          <div className="flex flex-col text-sm">
            <span className="font-medium">{suggestion.label}</span>
          </div>
        );
      }}
    />
  );
}
