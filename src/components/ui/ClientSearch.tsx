import { ApiClient } from '@api/api-configuration';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { CommonListResponse } from '@app/types/common.types';
import { DefaultOptionType } from 'antd/es/select';
import { ReactNode, useState } from 'react';
import FetchSearchSelect from './FetchSelect/ApiFetchSelect';

export function ClientSearch({
  // debounce = 500,
  // values,
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
}: {
  searchTerm?: string;
  debounce?: number;
  // values: Timesheet;
  disabled: boolean;
  setValue: (e: Client | Client[] | undefined | string) => void;
  value: string | undefined;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: Client) => boolean;
}) {
  const [data, setData] = useState<CommonListResponse<Client>>({
    Results: [],
    TotalResults: 0,
  });

  // const ajaxSize = 10;

  // const fetchCondition = values.Status === 0 || values.Status === 3;
  // && !values.TimesheetEntries[index].ProjectAcronym

  // useEffect(() => {
  //   if (fetchCondition) {
  //     const timeout = setTimeout(async () => {
  //       const res = await fetchData('', ajaxSize, 0);
  //       if (res) {
  //         setData(res);
  //       }
  //     }, debounce);
  //     return () => clearTimeout(timeout);
  //   }
  // }, [values.Month, values.Employee.EmployeeId]);

  // const fetchData = async (searchValue_?: string, pageSize?: number, pageIndex?: number) => {
  const fetchData = async () =>
    // searchValue_?: string, pageSize?: number, pageIndex?: number
    {
      if (disabled) return;

      const res = await ApiClient.clientApi.clients.getClientsClientsGet();

      setData({
        Results: res.data,
        TotalResults: res.data.length,
      });
      return {} as CommonListResponse<Client>;
    };

  return (
    <FetchSearchSelect<Client>
      data={data}
      disabled={disabled}
      value={value}
      onValueChange={(item) => setValue(item)}
      getId={(item) => item.clientId}
      getLabel={(item) => `${item.firstName} ${item.lastName}`}
      fetchData={fetchData}
      placeholder="Select Client"
      optionRender={optionRender}
      optionDisable={optionDisable}
    />
  );
}
