import useUiContext from '@context/ui/useUiContext';
import { Dropdown, Menu } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { FiChevronDown, FiChevronRight } from 'react-icons/fi';
import { useLocation, useNavigate } from 'react-router-dom';
import './menu.scss';

type IconComponent = React.ComponentType<React.SVGProps<SVGSVGElement>>;

type MenuItem = {
  key: string | number;
  icon?: { filled: IconComponent; outline: IconComponent }; // <-- CHANGED  label: string | React.ReactElement;
  label: string;
  children?: MenuItem[];
};
const getPaddingLeftClass = (depth: number) => {
  const paddingMap: Record<number, string> = {
    0: 'pl-0',
    1: 'pl-8',
    2: 'pl-16',
    3: 'pl-24',
    4: 'pl-28',
    5: 'pl-32',
  };
  return paddingMap[depth] || 'pl-32';
};

const getLeftClass = (depth: number) => {
  const paddingMap: Record<number, string> = {
    0: 'left-2',
    1: 'left-2',
    2: 'left-[60px]',
  };
  return paddingMap[depth] || 'left-[60px]';
};
const RecursiveMenu: React.FC<{ items: MenuItem[]; depth?: number }> = ({ items, depth = 0 }) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { isSidebarCollapsed } = useUiContext();
  const [openItems, setOpenItems] = useState<Record<string | number, boolean>>({});

  const openParentsOfActive = (items: MenuItem[], path: string): boolean => {
    let found = false;
    for (const item of items) {
      if (item.key === path) return true;
      if (item.children?.length) {
        const childHasActive = openParentsOfActive(item.children, path);
        if (childHasActive) {
          setOpenItems((prev) => ({ ...prev, [item.key]: true }));
          found = true;
        }
      }
    }
    return found;
  };

  useEffect(() => {
    openParentsOfActive(items, pathname);
  }, [pathname]);

  const toggleItem = (key: string | number) => {
    setOpenItems((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const isChildActive = (children: MenuItem[]): boolean => {
    return children.some((child) => {
      if (child.key === pathname) return true;
      if (child.children) return isChildActive(child.children);
      return false;
    });
  };

  const renderDropdownMenu = (children: MenuItem[]) => (
    <Menu className="rounded-md shadow-lg">
      {children.map((child) => {
        const isActive = pathname === child.key;
        return (
          <Menu.Item
            key={child.key}
            onClick={() => navigate(String(child.key))}
            className={`!px-4 !py-2 ${
              isActive ? 'bg-app-primary/50 !text-app-primary font-medium' : ' hover:!text-app-primary'
            }`}
          >
            {child.label}
          </Menu.Item>
        );
      })}
    </Menu>
  );

  return (
    <div
      className={`flex flex-col w-full items-center relative ${
        depth !== 0 && 'pl-' + depth * 100
      } ${depth === 0 ? 'gap-2' : 'gap-2'}`}
    >
      {items.map((item, index) => {
        const isOpen = !!openItems[item.key];
        const hasChildren = item.children && item.children.length > 0;
        const isActive = pathname === item.key;

        const itemContent = (
          <div
            className={`relative flex items-center ${
              isSidebarCollapsed ? 'w-5' : 'w-full gap-x-2'
            } cursor-pointer rounded-md transition-all duration-200 ${
              depth === 0 ? 'text-sm' : 'text-sm'
            } ${isActive ? '' : 'text-text-app-text-light hover:text-app-primary hover:bg-app-gray-light'} ${hasChildren && 'pr-6'}`}
            onClick={() => {
              if (hasChildren && !isSidebarCollapsed) {
                toggleItem(item.key);
              } else if (!hasChildren) {
                navigate(String(item.key));
              }
            }}
          >
            {item.icon && (
              <span className="flex items-center z-10">
                {React.createElement(
                  isActive || (hasChildren && isChildActive(item.children ?? []))
                    ? item.icon.filled
                    : item.icon.outline,
                  {
                    className: 'text-app-primary text-[18px]',
                  }
                )}
              </span>
            )}
            {!isSidebarCollapsed && (
              <span
                className={`relative inline-block z-10 text-ellipsis w-full whitespace-nowrap px-5 py-3 rounded-4xl ${
                  isActive ? 'text-app-primary font-normal' : 'text-app-text-light hover:text-app-primary'
                }`}
              >
                <div className="relative z-10">{item.label}</div>
                {isActive && (
                  <div
                    className={`menu-pill absolute top-0 left-0 w-full h-full z-0 rounded-tl-2xl rounded-bl-2xl bg-white`}
                  />
                )}
              </span>
            )}
            {/* {!isSidebarCollapsed && (
              <span
                className={`text-ellipsis w-full whitespace-nowrap px-5 py-2 rounded-4xl ${
                  isActive
                    ? 'bg-app-primary text-app-gray-light font-normal'
                    : 'text-app-text-light hover:text-app-primary'
                }`}
              >
                {item.label}
              </span>
            )} */}
            {!isSidebarCollapsed &&
              hasChildren &&
              (isOpen ? <FiChevronDown className="ml-auto" /> : <FiChevronRight className="ml-auto" />)}
            {/* {!isSidebarCollapsed && (
              <div className={`absolute top-0 left-0 w-full h-full z-0 rounded-md ${isActive && 'bg-app-primary'}`} />
            )} */}
          </div>
        );

        return (
          <div key={item.key} className={`w-full justify-center ${index === 0 && depth !== 0 ? 'mt-2' : ''}`}>
            <div
              className={`w-full mx-auto relative flex items-center justify-center ${
                isSidebarCollapsed ? 'px-0 ' : `${getPaddingLeftClass(depth)}`
              }`}
            >
              {/* {isActive && !isSidebarCollapsed && (
                <div className="absolute right-0 top-0 bottom-0 w-2 bg-app-primary rounded-l-md" />
              )} */}

              {isSidebarCollapsed && hasChildren ? (
                <Dropdown
                  trigger={['hover']}
                  placement="topRight"
                  overlay={renderDropdownMenu(item.children ? item.children : [])}
                  overlayClassName="z-50"
                />
              ) : (
                itemContent
              )}
            </div>

            {!isSidebarCollapsed && hasChildren && (
              <div
                className={`transition-all duration-300 ${isOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'}`}
              >
                <div className="relative flex justify-center">
                  <div className={`absolute top-0 bottom-0 ${getLeftClass(depth)} w-px bg-app-primary z-0`} />
                  <RecursiveMenu items={item.children ? item.children : []} depth={depth + 1} />
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default memo(RecursiveMenu);
