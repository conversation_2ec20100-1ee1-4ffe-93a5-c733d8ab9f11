import { MenuStructure } from '@app/types/routing.types';
import UseRoutingContext from '@context/routing/useRoutingContext';
import useUiContext from '@context/ui/useUiContext';
import WebPushPermissions from '@web-push/notifications-permissions';
import { Grid, Layout, MenuProps } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { IoArrowBack } from 'react-icons/io5';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar/Components/Sidebar';

const { Content } = Layout;
const { useBreakpoint } = Grid;

const MasterLayout: React.FC = () => {
  const breakpoints = useBreakpoint();
  const { isSidebarOpen, toggleSidebar } = useUiContext();
  const { xs } = breakpoints;
  const { filteredMenuStructure } = UseRoutingContext();

  useEffect(() => {
    if (xs !== undefined && isSidebarOpen && xs) {
      toggleSidebar();
    }
  }, [xs, isSidebarOpen, toggleSidebar]);

  const menuItems: MenuProps['items'] = useMemo(() => {
    const mapMenuStructure = (menu: MenuStructure): MenuProps['items'] => {
      return menu
        .filter((item) => item.show)
        .map((item) => {
          const hasChildren = Array.isArray(item.children);
          const children = hasChildren ? mapMenuStructure(item.children as MenuStructure) : undefined;

          const hasValidChildren = Array.isArray(children) && children.length > 0;

          if (hasChildren && !hasValidChildren) return null;

          return {
            key: item.key,
            icon: item.icon,
            label: item.label,
            children: hasValidChildren ? children : undefined,
          };
        })
        .filter(Boolean) as MenuProps['items'];
    };

    return mapMenuStructure(filteredMenuStructure);
  }, [filteredMenuStructure]);

  return (
    <Layout className="flex h-full !flex-row p-2 gap-2 !bg-white overflow-hidden">
      <Sidebar collapseIcon={<IoArrowBack size={'large'} />} menuItems={menuItems as undefined} />
      <Layout className="flex-1 h-full flex flex-col min-w-0 w-full">
        <div className="absolute w-full top-0 left-0 z-[999]">
          <WebPushPermissions />
        </div>
        <div className="flex flex-col bg-white h-full min-w-0">
          <Content className="flex flex-col gap-4 max-h-screen overflow-auto min-w-0 ">
            <Outlet />
          </Content>
        </div>
      </Layout>
    </Layout>
  );
};

export { MasterLayout };
