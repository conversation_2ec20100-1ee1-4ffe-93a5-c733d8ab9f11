import useUiContext from '@context/ui/useUiContext';
import { Button } from 'antd';
import React, { memo, ReactNode } from 'react';
import { Helmet } from 'react-helmet';
import { FiSidebar } from 'react-icons/fi';

type Props = {
  children: ReactNode;
  title: ReactNode;
  heightDifference?: string;
};
const isString = (node: ReactNode): node is string => {
  return typeof node === 'string';
};

const isReactElement = (node: ReactNode): node is React.ReactElement => {
  return React.isValidElement(node);
};

// I was expecting the HeaderLayout to handle just the header for the app layout. But it accepts the children as well for the page. Is that the main Layout?

// The whole layout Rerenders when i change route. I can see it in the browser cause it flashes most likely every single page component gets wrapper with the HeaderLayout again and again.

const HeaderLayout = ({ children, title, heightDifference }: Props) => {
  const { toggleSidebarCollapse } = useUiContext();

  const customStyle: React.CSSProperties = {};

  if (heightDifference) {
    customStyle['height'] = `calc(100vh - ${heightDifference || '4rem'})`;
  }

  return (
    <>
      {/* Do we really need to set the title? */}
      <Helmet>
        <title> {isString(title) ? title : isReactElement(title) ? 'LOOM' : null}</title>
      </Helmet>

      <div className="flex justify-between items-center px-0 py-2 bg-white border-b border-slate-100">
        <div className="flex gap-4 items-center">
          <div className="xs:hidden block" onClick={() => toggleSidebarCollapse()}>
            <Button
              className="text-xl bg-white leading-none"
              type="link"
              icon={<FiSidebar className="text-lg" />}
            ></Button>
          </div>
          <div className="text-lg text-slate-600">{title}</div>
        </div>

        {/* <div className="flex gap-4">
          <HeaderDropdown />
          <UserDropdown />
        </div> */}
      </div>

      <div className={'mx-4 pb-4 flex flex-col h-full'} style={customStyle}>
        {children}
      </div>
    </>
  );
};

export default memo(HeaderLayout);
