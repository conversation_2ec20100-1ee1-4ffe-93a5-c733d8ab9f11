import NotificationsWidget from '@app/features/notifications/notificationsWidget/NotificationsWidget';
import useUiContext from '@context/ui/useUiContext';
import { Button } from 'antd';
import React, { memo, ReactNode } from 'react';
import { Helmet } from 'react-helmet';
import { FiSidebar } from 'react-icons/fi';

type Props = {
  children: ReactNode;
  title: ReactNode;
};

const isString = (node: ReactNode): node is string => typeof node === 'string';
const isReactElement = (node: ReactNode): node is React.ReactElement => React.isValidElement(node);

const HeaderLayout = ({ children, title }: Props) => {
  const { toggleSidebarCollapse } = useUiContext();

  return (
    <>
      <Helmet>
        <title>{isString(title) ? title : isReactElement(title) ? 'LOOM' : null}</title>
      </Helmet>

      {/* Entire layout column, bounded, no page scroll */}
      <div className="flex h-full min-h-0 flex-col overflow-hidden">
        {/* Header row (auto height, never grows) */}
        <div className="shrink-0 flex justify-between items-center px-0 py-2 bg-white border-b border-slate-100 pr-8">
          <div className="flex gap-4 items-center">
            <div className="xs:hidden block" onClick={() => toggleSidebarCollapse()}>
              <Button className="text-xl bg-white leading-none" type="link" icon={<FiSidebar className="text-lg" />} />
            </div>

            <div className="text-lg text-slate-600">{title}</div>
          </div>

          <NotificationsWidget />
        </div>

        {/* Content area fills the rest; children can scroll inside */}
        <div className="flex-1 min-h-0 overflow-hidden px-4 pt-2 pb-2 flex flex-col">{children}</div>
      </div>
    </>
  );
};

export default memo(HeaderLayout);
