/// <reference lib="webworker" />
type TVisistData = {
  address: string;
  caregiver_first_name: string;
  caregiver_id: string;
  caregiver_last_name: string;
  client_first_name: string;
  client_id: string;
  client_last_name: string;
  notificationId: string;
  visit_end_time: string;
  visit_id: string;
  visit_start_time: string;
};
export type TMessage = {
  data: TVisistData;
  isFirebaseMessaging: boolean;
  messageType: 'push-received';
  notification: { title: string; body: string };
  type: 'NOTIFICATION_CLICK' | undefined;
};

interface IDBKeyval {
  set: (key: string, value: TMessage) => Promise<void>;
  get: (key: string) => Promise<TMessage>;
}

interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
}

interface Firebase {
  initializeApp: (config: FirebaseConfig) => void;
  messaging: () => unknown;
}

declare const self: ServiceWorkerGlobalScope & typeof globalThis;

declare const idbKeyval: IDBKeyval;
declare const firebase: Firebase;

console.log('SERVICE WORKER FB STARTED');

// Import external scripts
importScripts('https://cdn.jsdelivr.net/npm/idb-keyval@6/dist/umd.js');
importScripts('https://www.gstatic.com/firebasejs/9.10.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.10.0/firebase-messaging-compat.js');

// Make updates apply without a manual reload
self.skipWaiting();
self.clients.claim();

const NOTIFICATION_KEY = 'notification';
const img = `${self.location.origin}/media/logos/homecarelogo_sm.png`;
console.log('img', img);

self.addEventListener('notificationclick', (event: NotificationEvent) => {
  console.log('Click notification', event);
  const notificationData = event.notification || {};

  if (!('data' in notificationData)) {
    console.error('No data in notification. Wrong notification schema', notificationData);
    return;
  }

  const notificationPayload: TMessage = {
    ...(notificationData.data as TMessage),
  };
  // Open the URL in the default browser.
  event.waitUntil(routing(notificationPayload, event));
});

async function routing(notificationData: TMessage, event: NotificationEvent): Promise<boolean> {
  const windowClients = await self.clients.matchAll({ includeUncontrolled: true, type: 'window' });

  const postMessage: TMessage = {
    ...notificationData,
    type: 'NOTIFICATION_CLICK',
  };

  const lastClient = windowClients.length > 0 && windowClients[windowClients.length - 1];

  if (lastClient) {
    lastClient.focus();
    lastClient.postMessage({
      ...postMessage,
      msg: 'from background - foreground - tab is there but not focused',
    });

    event.notification.close();
    return Promise.resolve(true);
  }

  // Opening new window
  await saveNotifToDb(postMessage);

  await self.clients.openWindow('/dashboard');
  event.notification.close();
  return Promise.resolve(true);
}

async function saveNotifToDb(notif: TMessage): Promise<void> {
  console.log('saving notification to db', notif);
  return await idbKeyval.set(NOTIFICATION_KEY, notif);
}

const firebaseConfig = {
  apiKey: 'AIzaSyClHnd6BzHefbhJg-arg6yqsP9mb39p4b8',
  authDomain: 'homecare-2498a.firebaseapp.com',
  projectId: 'homecare-2498a',
  storageBucket: 'homecare-2498a.firebasestorage.app',
  messagingSenderId: '1063363203978',
  appId: '1:1063363203978:web:ee1a9263e338d61fa6ae73',
};

firebase.initializeApp(firebaseConfig);

// Initialize messaging (kept for Firebase setup)
firebase.messaging();

self.addEventListener('push', (event: PushEvent) => {
  console.log('Received push event', event);
  if (event.data) {
    const payload: TMessage = event.data.json();
    console.log('Push data received', payload);

    if (!('data' in payload)) {
      console.error('No data in payload. Wrong notification schema', payload);
      return;
    }

    const notificationData = payload.notification;
    const title = notificationData?.title || 'N/A';
    const body = notificationData?.body || 'N/A';

    const notificationTitle = title;
    const notificationOptions = {
      body: body,
      icon: img,
      data: payload,
    };

    console.log('Generating notification', notificationOptions);
    // This is the main notification display in OS system pane.
    event.waitUntil(self.registration.showNotification(notificationTitle, notificationOptions));
  }
});

export {};
