import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import ServiceTypesPage from '@pages/serviceTypes/ServiceTypesPage';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ServiceTypeTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Service Types Management">
      <ServiceTypesPage />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  ServiceTypeTablePage,
};
