import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { ServiceTypesList } from '@pages/serviceTypes/ServiceTypeList';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ServiceTypeTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Service Table">
      <ServiceTypesList />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  ServiceTypeTablePage,
};
