import NotificationsModalPage from '@app/features/notifications/NotificationsModalPage/NotificationsModalPage';
import { MasterLayout } from '@app/layouts/MasterLayout';
import UseRoutingContext from '@context/routing/useRoutingContext';
import { isRouteExist } from '@context/routing/utils/route-checker';
import { useWebPushApiCallback } from '@context/web-push';
import useTrackLocation from '@hooks/useTrackLocation';
import RunTimeError from '@pages/RunTimeError';
import { useEffect } from 'react';
import { Navigate, Outlet, useLocation, useNavigate, useRoutes } from 'react-router-dom';
import { WebPushSetup } from '../features/web-push';
import RequireAuth from './RequireAuth';

// const appRoutes = [
//   {
//     path: 'users',
//     children: [
//       { path: '', element: <UsersList /> },
//       { path: 'new', element: <CreateUser /> },
//       { path: ':id/edit', element: <EditUser /> },
//     ],
//   },
//   {
//     path: 'clients',
//     children: [
//       { path: '', element: <ClientsList /> },
//       { path: 'new', element: <CreateClient /> },
//       { path: ':id/edit', element: <EditClient /> },
//     ],
//   },
//   {
//     path: 'caregivers',
//     children: [
//       { path: '', element: <CaregiversList /> },
//       { path: 'new', element: <CreateCaregiver /> },
//       { path: ':id/edit', element: <EditCaregiver /> },
//     ],
//   },

//   {
//     path: 'availability',
//     element: <AvailabilityRoutes.AvailabilityPage />,
//   },
//   {
//     path: 'scheduling',
//     element: <SchedulingRoutes.SchedulingPage />,
//   },
//   {
//     path: 'shifts',
//     element: <ShiftsRoutes.ShiftsPage />,
//   },

//   {
//     path: '*',
//     element: <NotFound />,
//   },
// ];

const PrivateRoutes = () => {
  const navigate = useNavigate();
  const onToken = useWebPushApiCallback();

  // We need the current location to implement the background-location modal pattern
  const location = useLocation();
  const state = location.state as { backgroundLocation?: Location };

  const { getIndexRouteKey, getAllPaths, toReactRouter } = UseRoutingContext();

  useTrackLocation();
  // const routes = useRoutes([
  //   {
  //     path: '',
  //     element: (
  //       <RequireAuth>
  //         <WebPushSetup onToken={onToken}>
  //           <MasterLayout />
  //         </WebPushSetup>
  //       </RequireAuth>
  //     ),
  //     errorElement: <RunTimeError />,
  //     children: [
  //       {
  //         path: 'dashboard/*',
  //         element: <Outlet />,
  //         children: toReactRouter, // ✅ Insert transformed dynamic routes
  //       },
  //       {
  //         path: '*',
  //         element: <Navigate to="/404" />,
  //       },
  //     ],
  //   },
  // ]);

  const routes = useRoutes(
    [
      {
        path: '',
        element: (
          <RequireAuth>
            <WebPushSetup onToken={onToken}>
              <MasterLayout />
            </WebPushSetup>
          </RequireAuth>
        ),
        errorElement: <RunTimeError />,
        children: [
          {
            path: 'dashboard/*',
            element: <Outlet />,
            children: toReactRouter, // dynamic routes under /dashboard
          },
          { path: '*', element: <Navigate to="/404" /> },
        ],
      },
    ],
    state?.backgroundLocation || location
  );

  useEffect(() => {
    const prevRoute = localStorage.getItem('redirectAfterLogin');
    const indexRoute = getIndexRouteKey as string;

    if (!prevRoute) {
      navigate(indexRoute);
      return;
    }
    const allPaths = getAllPaths();
    console.log({ allPaths, prevRoute, indexRoute });
    const exist = isRouteExist(prevRoute, allPaths);

    if (exist) navigate(prevRoute);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {routes}

      {/* Global modal trigger: show overlay if the current URL ends with /notifications */}
      {/* TODO : will need to update it to ignore queryParams */}
      <NotificationsModalPage isOpen={location.pathname.endsWith('/notifications')} onClose={() => navigate(-1)} />
    </>
  );
};

export default PrivateRoutes;
