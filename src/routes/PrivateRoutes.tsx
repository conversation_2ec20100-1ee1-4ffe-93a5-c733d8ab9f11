import { MasterLayout } from '@app/layouts/MasterLayout';
import UseRoutingContext from '@context/routing/useRoutingContext';
import { isRouteExist } from '@context/routing/utils/route-checker';
import useTrackLocation from '@hooks/useTrackLocation';
import RunTimeError from '@pages/RunTimeError';
import { useEffect } from 'react';
import { Navigate, Outlet, useNavigate, useRoutes } from 'react-router-dom';
import RequireAuth from './RequireAuth';

// const appRoutes = [
//   {
//     path: 'users',
//     children: [
//       { path: '', element: <UsersList /> },
//       { path: 'new', element: <CreateUser /> },
//       { path: ':id/edit', element: <EditUser /> },
//     ],
//   },
//   {
//     path: 'clients',
//     children: [
//       { path: '', element: <ClientsList /> },
//       { path: 'new', element: <CreateClient /> },
//       { path: ':id/edit', element: <EditClient /> },
//     ],
//   },
//   {
//     path: 'caregivers',
//     children: [
//       { path: '', element: <CaregiversList /> },
//       { path: 'new', element: <CreateCaregiver /> },
//       { path: ':id/edit', element: <EditCaregiver /> },
//     ],
//   },

//   {
//     path: 'availability',
//     element: <AvailabilityRoutes.AvailabilityPage />,
//   },
//   {
//     path: 'scheduling',
//     element: <SchedulingRoutes.SchedulingPage />,
//   },
//   {
//     path: 'shifts',
//     element: <ShiftsRoutes.ShiftsPage />,
//   },

//   {
//     path: '*',
//     element: <NotFound />,
//   },
// ];

const PrivateRoutes = () => {
  const navigate = useNavigate();

  const { getIndexRouteKey, getAllPaths, toReactRouter } = UseRoutingContext();

  useTrackLocation();
  const routes = useRoutes([
    {
      path: '',
      element: (
        <RequireAuth>
          <MasterLayout />
        </RequireAuth>
      ),
      errorElement: <RunTimeError />,
      children: [
        {
          path: 'dashboard/*',
          element: <Outlet />,
          children: toReactRouter, // ✅ Insert transformed dynamic routes
        },
        {
          path: '*',
          element: <Navigate to="/404" />,
        },
      ],
    },
  ]);
  useEffect(() => {
    const prevRoute = localStorage.getItem('redirectAfterLogin');
    const indexRoute = getIndexRouteKey as string;

    if (!prevRoute) {
      navigate(indexRoute);
      return;
    }
    const allPaths = getAllPaths();
    console.log({ allPaths, prevRoute, indexRoute });
    const exist = isRouteExist(prevRoute, allPaths);

    if (exist) navigate(prevRoute);
  }, []);

  return <>{routes}</>;
};

export default PrivateRoutes;
