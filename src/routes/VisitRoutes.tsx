import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import VisitsPage from '@pages/visits/VisitsPage';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const VisitTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Visits Management">
      <VisitsPage />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  VisitTablePage,
};
