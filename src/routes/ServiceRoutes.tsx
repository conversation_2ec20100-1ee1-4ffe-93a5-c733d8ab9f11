import FullLoader from '@app/components/ui/FullLoader';
import { ServiceForm } from '@app/features/services/components/ServiceForm/ServiceForm';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { EditService } from '@pages/services/EditService';
import { ServiceList } from '@pages/services/ServicesList';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ServiceTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Service Table">
      <ServiceList />
    </HeaderLayout>
  </Suspense>
);

const ServiceFormPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create Service">
      <ServiceForm />
    </HeaderLayout>
  </Suspense>
);

const ServiceEditPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Edit Service">
      <EditService />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  ServiceFormPage,
  ServiceTablePage,
  ServiceEditPage,
};
