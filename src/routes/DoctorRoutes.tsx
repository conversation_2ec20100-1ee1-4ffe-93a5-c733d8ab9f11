// import FullLoader from '@app/components/ui/FullLoader';
// import HeaderLayout from '@app/layouts/HeaderLayout';
// import DoctorForm from '@pages/doctors/CreateDoctor';
// import { DoctorsList } from '@pages/doctors/DoctorsList';
// import { EditDoctor } from '@pages/doctors/EditDoctor';
// import { Suspense } from 'react';

// // Lazy Load DashboardPage Component

// // Route Component
// const DoctorTablePage = () => (
//   <Suspense fallback={<FullLoader />}>
//     <HeaderLayout title="Doctor Table">
//       <DoctorsList />
//     </HeaderLayout>
//   </Suspense>
// );
// const DoctorFormPage = () => (
//   <Suspense fallback={<FullLoader />}>
//     <HeaderLayout title="Create Doctor">
//       <DoctorForm />
//     </HeaderLayout>
//   </Suspense>
// );
// const DoctorEditPage = () => (
//   <Suspense fallback={<FullLoader />}>
//     <HeaderLayout title="Edit Doctor">
//       <EditDoctor />
//     </HeaderLayout>
//   </Suspense>
// );
// // Export Lazy-Loaded Route
// export default {
//   DoctorFormPage,
//   DoctorTablePage,
//   DoctorEditPage,
// };
