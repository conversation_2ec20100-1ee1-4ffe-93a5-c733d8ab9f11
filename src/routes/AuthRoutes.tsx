import { AuthLayout } from '@pages/auth/AuthLayout';
import { Login } from '@pages/auth/Login';
import ResetPwd from '@pages/auth/ResetPwd';
import SetPwd from '@pages/auth/SetPwd';
import { Navigate, Route, Routes } from 'react-router-dom';
import { LOGIN, RESET_PWD, SET_PWD } from './urls';

const AuthRoutes = () => (
  <Routes>
    <Route element={<AuthLayout />}>
      <Route index element={<Navigate to={LOGIN} replace />} />
      <Route path={LOGIN} element={<Login />} />
      <Route path={RESET_PWD} element={<ResetPwd />} />
      <Route path={SET_PWD} element={<SetPwd />} />
      <Route path="*" element={<Navigate to={LOGIN} replace />} />
    </Route>
  </Routes>
);
export default AuthRoutes;
