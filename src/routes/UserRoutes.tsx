import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { CreateUser } from '@pages/users/CreateUser';
import { EditUser } from '@pages/users/EditUser';
import UsersList from '@pages/users/UsersList';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const UsersTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Users Management">
      <UsersList />
    </HeaderLayout>
  </Suspense>
);
const UserCreatePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create User">
      <CreateUser />
    </HeaderLayout>
  </Suspense>
);
const UserEditPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Edit User">
      <EditUser />
    </HeaderLayout>
  </Suspense>
);
// Export Lazy-Loaded Route
export default {
  UsersTablePage,
  UserCreatePage,
  UserEditPage,
};
