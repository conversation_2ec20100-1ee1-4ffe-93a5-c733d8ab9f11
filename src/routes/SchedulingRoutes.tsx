import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import Scheduling from '@pages/Scheduling';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const SchedulingPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Scheduling">
      <Scheduling />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  SchedulingPage,
};
