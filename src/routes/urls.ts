//BASIC ROUTES
export const GET_LIST = 'list';
export const CREATE = 'create';
export const GET_APPROVAL_LIST = 'approval';
export const PREVIEW = ':id';

//Auth URLS
export const AUTH_PREFIX = 'auth';
export const LOGIN = 'login';
export const RESET_PWD = 'reset-password';
export const SET_PWD = 'password/reset/confirm';
export const AUTH_CALLBACK = 'callback';

//DASHBORD ROUTES & PAGES
export const APP_PREFIX = 'dashboard';
export const DASHBOARD_PAGE = '';
export const AVAILABILITY_PAGE = 'availability';
export const SCHEDULING_PAGE = 'scheduling';
export const SERVICE_REQUESTS_PAGE = 'service-requests';
export const SHIFTS_PAGE = 'shifts';
export const CLIENT_PAGE = 'client';
export const DOCTOR_PAGE = 'doctor';
export const SERVICE_PAGE = 'services';
export const SERVICE_TYPE_PAGE = 'service-types';

//PROFILE ROUTES
export const PROFILE_PREFIX = 'profile';
export const EDIT_PROFILE = 'record/:id';
export const EDIT_PROFILE_URL = 'profile/record';

//USERS ROUTES
export const USERS_PREFIX = 'users';
export const NEW_USER = 'new';
export const EDIT_USER = ':id/edit';
export const USERS_LIST = '';

//CAREGIVERS ROUTES
export const CAREGIVERS_PREFIX = 'caregivers';
export const NEW_CAREGIVER = 'new';
export const EDIT_CAREGIVER = ':id/edit';
export const CAREGIVERS_LIST = '';

//CLIENT ROUTES
export const CLIENTS_PREFIX = 'users';
export const NEW_CLIENT = 'new';
export const EDIT_CLIENT = ':id/edit';
export const CLIENTS_LIST = 'list';

//DOCTOR ROUTES
// export const DOCTOR_PREFIX = 'doctor';
// export const NEW_DOCTOR = 'new';
// export const EDIT_DOCTOR = ':id/edit';
// export const DOCTOR_LIST = 'list';

//SERVICE ROUTES
export const SERVICE_PREFIX = 'service';
export const NEW_SERVICE = 'new';
export const EDIT_SERVICE = ':id/edit';
export const SERVICE_LIST = 'list';

//SERVICE TYPES ROUTES
export const SERVICE_TYPE_PREFIX = 'service-type';
export const SERVICE_TYPE_LIST = '';
