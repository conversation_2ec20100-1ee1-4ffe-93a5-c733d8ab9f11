import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { ClientForm } from '@feat-clients/components/ClientForm/ClientForm';
import { ClientsList } from '@pages/clients/ClientList';
import { EditClient } from '@pages/clients/EditClient';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ClientTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Patient Table">
      <ClientsList />
    </HeaderLayout>
  </Suspense>
);
const ClientFormPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create Patient">
      <ClientForm />
    </HeaderLayout>
  </Suspense>
);
const ClientEditPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Edit Patient">
      <EditClient />
    </HeaderLayout>
  </Suspense>
);
// Export Lazy-Loaded Route
export default {
  ClientFormPage,
  ClientTablePage,
  ClientEditPage,
};
