import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import CaregiversListPage from '@pages/caregivers/CaregiversListPage';
import { CreateCaregiver } from '@pages/caregivers/CreateCaregiver';
import { EditCaregiverPage } from '@pages/caregivers/EditCaregiverPage';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const CaregiverTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Caregivers Management">
      <CaregiversListPage />
    </HeaderLayout>
  </Suspense>
);
const CaregiverFormPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create Caregiver">
      <CreateCaregiver />
    </HeaderLayout>
  </Suspense>
);
const CaregiverEditPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Edit Caregiver">
      <EditCaregiverPage />
    </HeaderLayout>
  </Suspense>
);
// Export Lazy-Loaded Route
export default {
  CaregiverFormPage,
  CaregiverTablePage,
  CaregiverEditPage,
};
