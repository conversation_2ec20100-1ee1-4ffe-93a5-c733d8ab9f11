import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { CaregiversList } from '@pages/caregivers/CaregiversList';
import { CreateCaregiver } from '@pages/caregivers/CreateCaregiver';
import { EditCaregiver } from '@pages/caregivers/EditCaregiver';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const CaregiverTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Caregiver Table">
      <CaregiversList />
    </HeaderLayout>
  </Suspense>
);
const CaregiverFormPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create Client">
      <CreateCaregiver />
    </HeaderLayout>
  </Suspense>
);
const CaregiverEditPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Edit Client">
      <EditCaregiver />
    </HeaderLayout>
  </Suspense>
);
// Export Lazy-Loaded Route
export default {
  CaregiverFormPage,
  CaregiverTablePage,
  CaregiverEditPage,
};
