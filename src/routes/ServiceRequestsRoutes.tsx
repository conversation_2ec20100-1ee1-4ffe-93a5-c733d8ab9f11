import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import ServiceRequest from '@pages/ServiceRequest';
import ServiceRequestsListPage from '@pages/serviceRequests/ServiceRequestsListPage';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ServiceRequestsPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create Service Request">
      <ServiceRequest />
    </HeaderLayout>
  </Suspense>
);

const ServiceRequestsTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Service Requests Management">
      <ServiceRequestsListPage />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  ServiceRequestsPage,
  ServiceRequestsTablePage,
};
