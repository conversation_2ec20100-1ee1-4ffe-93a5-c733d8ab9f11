import FullLoader from '@app/components/ui/FullLoader';
import ServiceRequest from '@pages/serviceRequest/ServiceRequest';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ServiceRequestsPage = () => (
  <Suspense fallback={<FullLoader />}>
    {/* <HeaderLayout title="Service Request"> */}
    <ServiceRequest />
    {/* </HeaderLayout> */}
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  ServiceRequestsPage,
};
