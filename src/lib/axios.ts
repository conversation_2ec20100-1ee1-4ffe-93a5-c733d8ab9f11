import type { AxiosRequestConfig } from 'axios';
import axios from 'axios';

const api = axios.create({
  baseURL: 'https://api.example.com',
});

// Add token to every request if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token'); // or use cookies or a context
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// ✅ Orval-compatible default export: the mutator
export default async function axiosMutator<T>(config: AxiosRequestConfig): Promise<T> {
  const response = await api.request<T>(config);
  return response.data;
}
