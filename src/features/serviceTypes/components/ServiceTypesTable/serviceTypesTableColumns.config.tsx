import { ServiceType } from '@api/READ_ONLY/services_api/Api';
import TableLink from '@app/components/common/TableLink';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { Typography } from 'antd';

export interface ServiceTypeRow extends Omit<ServiceType, 'serviceTypeId'>, TableRow {
  id: string | number;
  serviceTypeId: number;
}

// ...existing code...

export const getServiceTypesTableColumns = (
  editAction: (serviceType: ServiceTypeRow) => void
): BaseColumn<ServiceTypeRow>[] => [
  {
    id: 'name',
    header: 'Name',
    accessor: 'name',
    width: 120,
    cellRenderer: (name, row) => <TableLink onClick={() => editAction(row)}>{String(name ?? '')}</TableLink>,
  },
  {
    id: 'description',
    header: 'Description',
    accessor: 'description',
    width: 120,
    cellRenderer: (desc) => <Typography>{String(desc ?? '')}</Typography>,
  },
];
