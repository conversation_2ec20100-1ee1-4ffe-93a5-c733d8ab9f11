import { ApiClient } from '@api/api-configuration';
import { ServiceTypeCreate } from '@api/READ_ONLY/services_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import Table from '@app/features/table/Table';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Modal } from 'antd';
import { useState } from 'react';
import { serviceTypesFormConfig } from '../ServiceTypeForm/form.config';

const ServiceTypeTable = () => {
  const [isModalOpen, setIsModalOpen] = useState({ open: false, data: {} });
  const serviceTypes = useSchedulingStore((state) => state.serviceTypes);
  const loading = useSchedulingStore((state) => state.loadingServiceTypes);

  const setServiceTypes = useSchedulingStore((state) => state.setServiceTypes);
  const { openNotification } = useNotifications();

  const openModal = (record?: ServiceTypeCreate) => {
    // methods.reset(record || {});
    setIsModalOpen({ open: true, data: record || {} });
  };

  const onSubmit = async (formData: ServiceTypeCreate) => {
    try {
      await ApiClient.serviceApi.serviceTypes.createServiceTypeServiceTypesPost(formData);

      setIsModalOpen({ open: false, data: {} });
      openNotification('topRight', {
        title: `Service Type`,
        description: 'Service type created successfully.',
        type: 'Success',
      });
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    } finally {
      const response = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet();
      setServiceTypes(response.data);
    }
  };

  return (
    <div className="w-full h-full">
      <Modal
        title={Object.keys(isModalOpen.data).length === 0 ? 'Create New Service Type' : 'Edit Service Type'}
        open={isModalOpen.open}
        onCancel={() => {
          setIsModalOpen({ open: false, data: {} });
        }}
        footer={null}
      >
        <FormWrapper
          key={isModalOpen.open + JSON.stringify(isModalOpen.data)}
          defaultValues={isModalOpen.data}
          fields={serviceTypesFormConfig}
          onSubmit={onSubmit}
          withTabs={false}
          extraClasses={''}
        />
      </Modal>
      <Table
        data={serviceTypes}
        totalData={serviceTypes.length}
        loadingDataFetch={loading}
        addNavigate={() => {
          openModal();
        }}
        exportComponent={<></>}
        localStorageKey="ServiceTypeTable"
        onRowClick={(record) => {
          openModal(record);
        }}
      />
    </div>
  );
};

export default ServiceTypeTable;
