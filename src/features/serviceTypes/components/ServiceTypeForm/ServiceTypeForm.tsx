import { ApiClient } from '@api/api-configuration';
import { ServiceType, ServiceTypeCreate, ServiceTypeUpdate } from '@api/READ_ONLY/services_api/Api';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { Button, Form, Input, Space } from 'antd';
import { useMemo, useState } from 'react';

type Props = {
  data?: ServiceType;
  onSubmitted?: (result: ServiceType) => void;
};

type ServiceTypeFormValues = {
  name: string;
  description?: string;
};

export function ServiceTypeForm({ data, onSubmitted }: Props) {
  const { openNotification } = useNotifications();
  const [form] = Form.useForm<ServiceTypeFormValues>();
  const [submitting, setSubmitting] = useState(false);

  const initialValues = useMemo<Partial<ServiceTypeFormValues>>(
    () => ({
      name: data?.name ?? '',
      description: data?.description ?? '',
    }),
    [data]
  );

  const onFinish = async (values: ServiceTypeFormValues) => {
    try {
      setSubmitting(true);
      let result: ServiceType;
      if (data?.serviceTypeId) {
        const payload: ServiceTypeUpdate = {
          name: values.name,
          description: values.description ?? null,
        };
        const res = await ApiClient.serviceApi.serviceTypes.updateServiceTypeServiceTypesServiceTypeIdPut(
          data.serviceTypeId,
          payload
        );
        result = res.data;
        openNotification('topRight', {
          title: 'Service Type',
          description: 'Service type updated successfully.',
          type: 'Success',
        });
      } else {
        const payload: ServiceTypeCreate = {
          name: values.name,
          description: values.description ?? undefined,
        };
        const res = await ApiClient.serviceApi.serviceTypes.createServiceTypeServiceTypesPost(payload);
        result = res.data;
        openNotification('topRight', {
          title: 'Service Type',
          description: 'Service type created successfully.',
          type: 'Success',
        });
      }

      onSubmitted?.(result);
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: 'Service Type',
          description: 'Operation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Form<ServiceTypeFormValues> layout="vertical" form={form} initialValues={initialValues} onFinish={onFinish}>
      <Form.Item name="name" label="Name" rules={[{ required: true, message: 'Name is required' }]}>
        <Input placeholder="Name" />
      </Form.Item>

      <Form.Item
        name="description"
        label="Description"
        rules={[{ required: true, message: 'Description is required' }]}
      >
        <Input.TextArea rows={4} placeholder="Description" />
      </Form.Item>

      <Space>
        <Button htmlType="submit" type="primary" loading={submitting}>
          {data?.serviceTypeId ? 'Update' : 'Create'}
        </Button>
      </Space>
    </Form>
  );
}
