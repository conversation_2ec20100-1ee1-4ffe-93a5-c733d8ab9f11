import { Caregiver } from '@app/types/caregiver.types';
import { Checkbox, Tag, Typography } from 'antd';

export type DoctorTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: unknown, record?: T) => React.ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

export const getDoctorTableColumns = (): DoctorTableColumnType<Caregiver>[] => [
  {
    key: 'Name',
    title: 'Name',
    width: 120,
    render: (_, doctor?: Caregiver) => (
      <Typography>
        {doctor?.firstName} {doctor?.lastName}
      </Typography>
    ),
  },
  {
    dataIndex: 'dateOfBirth',
    key: 'dateOfBirth',
    title: 'Date of Birth',
    width: 150,
    align: 'center',
    render: (date) => (date ? new Date(date as string).toLocaleDateString() : '-'),
  },
  {
    dataIndex: 'gender',
    key: 'gender',
    title: 'Gender',
    width: 100,
    align: 'center',
    render: (gender) => <Typography>{(gender as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'nationalId',
    key: 'nationalId',
    title: 'National ID',
    width: 140,
    render: (id) => <Typography>{(id as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'phone',
    key: 'phone',
    title: 'Phone',
    width: 150,
    render: (phone) => <Typography>{(phone as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'email',
    key: 'email',
    title: 'Email',
    width: 200,
    render: (email) => <Typography>{(email as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'username',
    key: 'username',
    title: 'Username',
    width: 140,
    render: (username) => <Typography>{username as string}</Typography>,
  },
  {
    dataIndex: 'passwordHash',
    key: 'passwordHash',
    title: 'Password Hash',
    width: 180,
    render: (hash) => <Typography.Text code>{hash as string}</Typography.Text>,
  },
  {
    dataIndex: ['baseAddress', 'street'],
    key: 'baseAddress.street',
    title: 'Street',
    width: 140,
    render: (_, record?: Caregiver) => <Typography>{record?.baseAddress?.street || '-'}</Typography>,
  },
  {
    dataIndex: ['baseAddress', 'city'],
    key: 'baseAddress.city',
    title: 'City',
    width: 120,
    render: (_, record?: Caregiver) => <Typography>{record?.baseAddress?.city || '-'}</Typography>,
  },
  {
    dataIndex: 'coverageAreas',
    key: 'coverageAreas',
    title: 'Coverage Areas',
    width: 180,
    render: (areas) =>
      Array.isArray(areas) && areas?.length ? (areas as string[]).map((area) => <Tag key={area}>{area}</Tag>) : '-',
  },
  {
    dataIndex: 'travelRadius',
    key: 'travelRadius',
    title: 'Travel Radius (km)',
    width: 160,
    align: 'center',
    render: (radius) => <Typography>{(radius as number) ?? '-'}</Typography>,
  },
  {
    dataIndex: 'certifications',
    key: 'certifications',
    title: 'Certifications',
    width: 200,
    render: (certs) =>
      Array.isArray(certs) && certs?.length ? (certs as string[]).map((c) => <Tag key={c}>{c}</Tag>) : '-',
  },
  {
    dataIndex: 'skills',
    key: 'skills',
    title: 'Skills',
    width: 180,
    render: (skills) => (Array.isArray(skills) && skills?.length ? skills.map((s) => <Tag key={s}>{s}</Tag>) : '-'),
  },
  {
    dataIndex: 'specialties',
    key: 'specialties',
    title: 'Specialties',
    width: 180,
    render: (specs) => (Array.isArray(specs) && specs?.length ? specs.map((s) => <Tag key={s}>{s}</Tag>) : '-'),
  },
  {
    dataIndex: 'languagesSpoken',
    key: 'languagesSpoken',
    title: 'Languages',
    width: 160,
    render: (langs) => (Array.isArray(langs) && langs?.length ? langs.map((l) => <Tag key={l}>{l}</Tag>) : '-'),
  },
  {
    dataIndex: 'rating',
    key: 'rating',
    title: 'Rating',
    width: 100,
    align: 'center',
    render: (rating) => <Typography>{(rating as number)?.toFixed(1) ?? '-'}</Typography>,
  },
  {
    dataIndex: 'active',
    key: 'active',
    title: 'Active',
    width: 80,
    align: 'center',
    render: (active) => <Checkbox checked={!!active} disabled />,
  },
  {
    dataIndex: 'createdAt',
    key: 'createdAt',
    title: 'Created At',
    width: 180,
    render: (date) => (date ? new Date(date as string).toLocaleString() : '-'),
  },
  {
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    title: 'Updated At',
    width: 180,
    render: (date) => (date ? new Date(date as string).toLocaleString() : '-'),
  },
];
