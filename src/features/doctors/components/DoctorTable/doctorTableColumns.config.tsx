import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import TableLink from '@app/components/common/TableLink';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Tag, Typography } from 'antd';

export interface Doctor<PERSON><PERSON> extends Omit<Doctor, 'doctorId'>, TableRow {
  id: string | number;
  doctorId: number;
}

// ...existing code...

export const getDoctorTableColumns = (editNavigation?: (doctorId: number) => void): BaseColumn<DoctorRow>[] => [
  {
    id: 'name',
    header: 'Name',
    accessor: (row) => `${row.firstName ?? ''} ${row.lastName ?? ''}`.trim(),
    width: 200,
    cellRenderer: (_, doctor) =>
      editNavigation ? (
        <TableLink onClick={() => editNavigation(doctor.doctorId)}>
          <ClientDisplay firstName={doctor.firstName || ''} lastName={doctor.lastName || ''} />
        </TableLink>
      ) : (
        <ClientDisplay firstName={doctor.firstName || ''} lastName={doctor.lastName || ''} />
      ),
  },
  {
    id: 'email',
    header: 'Email',
    accessor: 'email',
    width: 200,
    type: 'email',
    cellRenderer: (email) => <Typography.Text>{String(email ?? '-')}</Typography.Text>,
  },
  {
    id: 'phone',
    header: 'Phone',
    accessor: 'phone',
    width: 150,
    cellRenderer: (phone) => <Typography.Text>{String(phone ?? '-')}</Typography.Text>,
  },
  {
    id: 'licenseNumber',
    header: 'License Number',
    accessor: 'licenseNumber',
    width: 150,
    cellRenderer: (license) => <Typography.Text>{String(license ?? '-')}</Typography.Text>,
  },
  {
    id: 'isActive',
    header: 'Status',
    accessor: 'isActive',
    width: 100,
    align: 'center',
    cellRenderer: (isActive) => <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Active' : 'Inactive'}</Tag>,
  },
];
