const DoctorTable = () => {
  return (
    <div className="w-full h-full">
      {/* <Table
        data={doctors}
        totalData={doctors.length}
        loadingDataFetch={false}
        addNavigate={() => navigate(`/${APP_PREFIX}/${DOCTOR_PAGE}/${NEW_DOCTOR}`)}
        exportComponent={<></>}
        localStorageKey="DoctorTable"
        onRowClick={(record) => {
          const path = `/${APP_PREFIX}/${DOCTOR_PAGE}/${EDIT_DOCTOR.replace(':id', `${record.id}`)}`;
          navigate(path, { state: record });
        }}
      /> */}
    </div>
  );
};

export default DoctorTable;
