import { FieldGroup } from '@app/types/form.types';

export const doctorFormConfig: FieldGroup[] = [
  {
    title: 'Personal Information',
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        width: 'third',
        rules: { required: 'First name is required' },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        width: 'third',
        rules: { required: 'Last name is required' },
      },
      {
        name: 'dateOfBirth',
        label: 'Date of Birth',
        type: 'date',
        width: 'third',
      },
      {
        name: 'gender',
        label: 'Gender',
        type: 'select',
        width: 'third',
        options: [
          { label: 'Male', value: 'Male' },
          { label: 'Female', value: 'Female' },
          { label: 'Other', value: 'Other' },
        ],
      },
      {
        name: 'nationalId',
        label: 'National ID',
        type: 'text',
        width: 'third',
      },
      {
        name: 'phone',
        label: 'Phone',
        type: 'text',
        width: 'third',
        rules: { required: 'Phone is required' },
      },
      {
        name: 'email',
        label: 'Email',
        type: 'email',
        width: 'third',
      },
      {
        name: 'languagesSpoken',
        label: 'Languages Spoken (comma-separated)',
        type: 'text',
        width: 'full',
      },
      {
        name: 'username',
        label: 'Username',
        type: 'text',
        width: 'half',
        rules: { required: 'Username is required' },
      },
      {
        name: 'passwordHash',
        label: 'Password',
        type: 'password',
        width: 'half',
        rules: { required: 'Password is required' },
      },
    ],
  },
  {
    title: 'Address & Coverage',
    fields: [
      { name: 'baseAddress.street', label: 'Street', type: 'text', width: 'third' },
      { name: 'baseAddress.city', label: 'City', type: 'text', width: 'third' },
      {
        name: 'coverageAreas',
        label: 'Coverage Areas (comma-separated)',
        type: 'text',
        width: 'third',
      },
      {
        name: 'travelRadius',
        label: 'Travel Radius (km)',
        type: 'number',
        width: 'third',
      },
    ],
  },
  {
    title: 'Certifications & Skills',
    fields: [
      {
        name: 'certifications',
        label: 'Certifications (comma-separated)',
        type: 'text',
        width: 'half',
      },
      {
        name: 'skills',
        label: 'Skills (comma-separated)',
        type: 'text',
        width: 'half',
      },
      {
        name: 'specialties',
        label: 'Specialties (comma-separated)',
        type: 'text',
        width: 'full',
      },
    ],
  },
  {
    title: 'System Settings',
    fields: [
      {
        name: 'roles',
        label: 'Roles',
        type: 'select',
        width: 'third',
        options: [{ label: 'Caregiver', value: 'caregiver' }],
        rules: { required: 'Role is required' },
      },
      { name: 'rating', label: 'Rating (0-5)', type: 'number', width: 'third' },
      { name: 'active', label: 'Active', type: 'checkbox', width: 'third' },
    ],
  },
];
