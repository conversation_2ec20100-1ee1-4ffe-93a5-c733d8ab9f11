import { Doctor, DoctorCreate } from '@api/READ_ONLY/doctors_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { doctorFormConfig } from './form.config';

type Props = {
  data?: Doctor;
  onSubmit: (formData: Doctor | DoctorCreate) => Promise<void>;
};

export function DoctorForm({ data, onSubmit }: Props) {
  return <FormWrapper defaultValues={data} onSubmit={onSubmit} fields={doctorFormConfig} />;
}
