import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, CLIENT_PAGE, CLIENTS_LIST } from '@app/routes/urls';
import { Caregiver } from '@app/types/caregiver.types';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useNavigate } from 'react-router-dom';
import { doctorFormConfig } from './form.config';

type Props = {
  data?: Caregiver;
};
export function DoctorFormInner({ data }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const onSubmit = async (formData: unknown) => {
    try {
      console.log(formData);
      openNotification('topRight', {
        title: `Client`,
        description: 'Client created successfully. ',
        type: 'Success',
      });
      console.log(`${APP_PREFIX}/${CLIENT_PAGE}/${CLIENTS_LIST}`);
      navigate(`/${APP_PREFIX}/${CLIENT_PAGE}/${CLIENTS_LIST}`);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };

  return <FormWrapper defaultValues={data} onSubmit={onSubmit} fields={doctorFormConfig} />;
}
