import Table from '@app/features/table/Table';
import { APP_PREFIX, CLIENT_PAGE, EDIT_CLIENT, NEW_CLIENT } from '@app/routes/urls';
import { dummyClients } from '@feat-clients/data/dummy';
import { useNavigate } from 'react-router-dom';

const ClientTable = () => {
  const navigate = useNavigate();

  return (
    <div className="w-full h-full">
      <Table
        data={dummyClients}
        totalData={dummyClients.length}
        loadingDataFetch={false}
        addNavigate={() => navigate(`/${APP_PREFIX}/${CLIENT_PAGE}/${NEW_CLIENT}`)}
        exportComponent={<></>}
        localStorageKey="ClientTable"
        onRowClick={(record) => {
          const path = `/${APP_PREFIX}/${CLIENT_PAGE}/${EDIT_CLIENT.replace(':id', record.id)}`;
          navigate(path, { state: record });
        }}
      />
    </div>
  );
};

export default ClientTable;
