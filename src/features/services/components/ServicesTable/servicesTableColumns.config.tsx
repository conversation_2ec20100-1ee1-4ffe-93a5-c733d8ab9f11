import { Service, ServiceType } from '@api/READ_ONLY/services_api/Api';
import TableLink from '@app/components/common/TableLink';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { Typography } from 'antd';

export interface ServiceRow extends Omit<Service, 'serviceId'>, TableRow {
  id: string | number;
  serviceId: number;
}

// ...existing code...

export const getServiceTableColumns = (
  serviceTypes: ServiceType[],
  editNavigation?: (serviceId: number) => void
): BaseColumn<ServiceRow>[] => [
  {
    id: 'name',
    header: 'Name',
    accessor: 'name',
    width: 120,
    cellRenderer: (value, row) =>
      editNavigation ? (
        <TableLink onClick={() => editNavigation(row.serviceId)}>{value as string}</TableLink>
      ) : (
        <Typography>{value as string}</Typography>
      ),
  },
  {
    id: 'description',
    header: 'Description',
    accessor: 'description',
    width: 140,
    cellRenderer: (value) => <Typography>{value as string}</Typography>,
  },
  {
    id: 'estimatedTimeMinutes',
    header: 'Estimated Time (Minutes)',
    accessor: 'estimatedTimeMinutes',
    width: 120,
    cellRenderer: (value) => <Typography>{value as string}</Typography>,
  },
  {
    id: 'serviceTypeId',
    header: 'Service Type',
    accessor: 'serviceTypeId',
    width: 120,
    cellRenderer: (value) => {
      const typeId = value as number;
      const serviceType = serviceTypes.find((s) => s.serviceTypeId === typeId);
      return <Typography>{serviceType?.name || 'Unknown'}</Typography>;
    },
  },
];
