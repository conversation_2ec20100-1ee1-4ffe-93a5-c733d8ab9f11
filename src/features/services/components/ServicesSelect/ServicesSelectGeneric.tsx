import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/caregiver_api/Api';
import {
  GenericSearchSelect,
  type ApiFetchFunction,
  type ApiQueryParams,
} from '@app/components/ui/GenericSearchSelect';
import { SelectProps } from 'antd';

type ServicesSelectProps = {
  onServiceChange?: (service: Service | Service[] | undefined) => void;
  placeholder?: string;
  pageSize?: number;
  minSearchLength?: number;
  searchDebounceMs?: number;
} & Omit<SelectProps, 'options' | 'onChange' | 'loading' | 'onSearch'>;

// API fetch function for services
const fetchServices: ApiFetchFunction<Service> = async (params: ApiQueryParams) => {
  return await ApiClient.serviceApi.services.getServicesServicesGet(params);
};

export const ServicesSelectGeneric = ({
  onServiceChange,
  placeholder = 'Select Service',
  pageSize = 10,
  minSearchLength = 3,
  searchDebounceMs = 300,
  ...selectProps
}: ServicesSelectProps) => {
  return (
    <GenericSearchSelect<Service>
      {...selectProps}
      apiFetch={fetchServices}
      getItemId={(service) => service.serviceId}
      getItemLabel={(service) => service.name}
      onSelectionChange={onServiceChange}
      placeholder={placeholder}
      pageSize={pageSize}
      minSearchLength={minSearchLength}
      searchDebounceMs={searchDebounceMs}
      searchPlaceholder={`Search services... (min ${minSearchLength} characters)`}
      renderOption={(service) => <span>{service.name}</span>}
    />
  );
};

export default ServicesSelectGeneric;
