import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, CLIENT_PAGE, CLIENTS_LIST } from '@app/routes/urls';
import { Client } from '@app/types/client.types';
import { isErrorWithErrors } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { FieldValues, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { clientFormConfig } from './form.config';

type Props = {
  data?: Client;
};
export function ClientForm({ data }: Props) {
  const methods = useForm();
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const onSubmit = methods.handleSubmit((data: FieldValues) => {
    try {
      console.log(data);
      openNotification('topRight', {
        title: `Client`,
        description: 'Client created successfully. ',
        type: 'Success',
      });
      console.log(`${APP_PREFIX}/${CLIENT_PAGE}/${CLIENTS_LIST}`);
      navigate(`/${APP_PREFIX}/${CLIENT_PAGE}/${CLIENTS_LIST}`);
    } catch (error: unknown) {
      if (isErrorWithErrors(error)) {
        openNotification('topRight', {
          title: `Client`,
          description: 'Client creation failed. ' + error.errors.join(','),
          type: 'Warning',
        });
      } else {
        openNotification('topRight', {
          title: `Client`,
          description: 'Client creation failed due to unknown error.',
          type: 'Warning',
        });
      }
    }
  });

  return <FormWrapper defaultValues={data} fields={clientFormConfig} onSubmit={onSubmit} />;
}
