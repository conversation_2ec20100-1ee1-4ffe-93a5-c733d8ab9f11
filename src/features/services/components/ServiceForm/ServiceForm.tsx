import { ApiClient } from '@api/api-configuration';
import { Service, ServiceCreate, ServiceUpdate } from '@api/READ_ONLY/services_api/Api';
import { APP_PREFIX, SERVICE_LIST, SERVICE_PAGE } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Button, Form, Input, InputNumber, Select, Space } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

type Props = {
  data?: Service;
};
export function ServiceForm({ data }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();
  const setServiceTypes = useSchedulingStore((s) => s.setServiceTypes);
  const serviceTypes = useSchedulingStore((s) => s.serviceTypes);

  type ServiceFormValues = {
    name: string;
    serviceTypeId: number;
    estimatedTimeMinutes: number;
    description?: string;
  };

  const [submitting, setSubmitting] = useState(false);
  const [form] = Form.useForm<ServiceFormValues>();

  const initialValues = useMemo<Partial<ServiceFormValues>>(
    () => ({
      name: data?.name ?? '',
      serviceTypeId: data?.serviceTypeId ?? undefined,
      estimatedTimeMinutes: data?.estimatedTimeMinutes ?? undefined,
      description: data?.description ?? '',
    }),
    [data]
  );

  const onFinish = async (values: ServiceFormValues) => {
    try {
      setSubmitting(true);
      if (data?.serviceId) {
        const payload: ServiceUpdate = {
          name: values.name,
          serviceTypeId: values.serviceTypeId,
          estimatedTimeMinutes: values.estimatedTimeMinutes,
          description: values.description ?? null,
        };
        await ApiClient.serviceApi.services.updateServiceServicesServiceIdPut(data.serviceId, payload);
        openNotification('topRight', {
          title: 'Service',
          description: 'Service updated successfully.',
          type: 'Success',
        });
      } else {
        const payload: ServiceCreate = {
          name: values.name,
          serviceTypeId: values.serviceTypeId,
          estimatedTimeMinutes: values.estimatedTimeMinutes,
          description: values.description ?? undefined,
        };
        await ApiClient.serviceApi.services.createServiceServicesPost(payload);
        openNotification('topRight', {
          title: 'Service',
          description: 'Service created successfully.',
          type: 'Success',
        });
      }
      navigate(`/${APP_PREFIX}/${SERVICE_PAGE}/${SERVICE_LIST}`);
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: 'Service',
          description: 'Operation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    } finally {
      setSubmitting(false);
    }
  };
  useEffect(() => {
    const fetchTypes = async () => {
      const res = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet();
      setServiceTypes(res.data.data);
    };
    fetchTypes();
  }, [setServiceTypes]);

  return (
    <div className="bg-white p-4 ">
      <Form<ServiceFormValues> form={form} layout="vertical" initialValues={initialValues} onFinish={onFinish}>
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-12 sm:col-span-4">
            <Form.Item name="name" label="Name" rules={[{ required: true, message: 'Name is required' }]}>
              <Input placeholder="Enter service name" />
            </Form.Item>
          </div>

          <div className="col-span-12 sm:col-span-4">
            <Form.Item
              name="serviceTypeId"
              label="Service Type"
              rules={[{ required: true, message: 'Service type is required' }]}
            >
              <Select
                placeholder="Select service type"
                options={serviceTypes.map((t) => ({ label: t.name, value: t.serviceTypeId }))}
                showSearch
                optionFilterProp="label"
              />
            </Form.Item>
          </div>

          <div className="col-span-12 sm:col-span-4">
            <Form.Item
              name="estimatedTimeMinutes"
              label="Estimated Time (Minutes)"
              rules={[{ required: true, message: 'Duration is required' }]}
            >
              <InputNumber className="!w-full" min={1} placeholder="e.g. 60" />
            </Form.Item>
          </div>

          <div className="col-span-12">
            <Form.Item name="description" label="Description">
              <Input.TextArea rows={4} placeholder="Optional description" />
            </Form.Item>
          </div>
        </div>

        <Space>
          <Button onClick={() => navigate(-1)}>Cancel</Button>
          <Button type="primary" htmlType="submit" loading={submitting}>
            {data?.serviceId ? 'Update' : 'Create'}
          </Button>
        </Space>
      </Form>
    </div>
  );
}
