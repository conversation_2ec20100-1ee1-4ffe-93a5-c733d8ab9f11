import { ServiceType } from '@api/READ_ONLY/services_api/Api';
import { FieldGroup } from '@app/types/form.types';

export const getServicesFormConfig = (serviceTypes: ServiceType[]): FieldGroup[] => [
  {
    title: 'Service Details',
    fields: [
      { name: 'name', label: 'Name', type: 'text', width: 'third', rules: { required: 'Name is required' } },
      {
        name: 'serviceTypeId',
        label: 'Service Type',
        rules: { required: 'Service type is required' },

        type: 'select',
        width: 'third',
        options: serviceTypes.map((t) => ({
          label: t.name,
          value: t.serviceTypeId,
        })),
      },
      {
        name: 'estimatedTimeMinutes',
        rules: { required: 'Duration is required' },
        label: 'Estimated Time (Minutes)',
        type: 'number',
        width: 'third',
      },
      { name: 'description', label: 'Description', type: 'textarea', width: 'full' },
    ],
  },
];
