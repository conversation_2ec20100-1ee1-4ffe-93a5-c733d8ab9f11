import { ApiClient } from '@api/api-configuration';
import { Caregiver, CaregiverCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, CAREGIVERS_LIST, CAREGIVERS_PREFIX } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import { transformList } from '@app/utils/transformToList';
import useNotifications from '@context/notifications/useNotificationContext';
import { useNavigate } from 'react-router-dom';
import { caregiverFormConfig } from './form.config';

type Props = {
  data?: Partial<CaregiverCreate>; // partial because defaultValues can be partial
  externalOnSubmit?: (formData: Caregiver) => void;
};

export function CaregiverFormInner({ data, externalOnSubmit }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const internalOnSubmit = async (formData: Caregiver) => {
    try {
      console.log('Form data:', formData);
      const transformedData = {
        ...formData,
        certifications: transformList(formData.certifications),
        skills: transformList(formData.skills),
        specialties: transformList(formData.specialties),
        coverageAreas: transformList(formData.coverageAreas),
        languagesSpoken: transformList(formData.languagesSpoken),
      };
      const response = await ApiClient.caregiverApi.caregivers.createCaregiverCaregiversPost(transformedData);
      console.log('Response:', response);
      openNotification('topRight', {
        title: `Caregiver`,
        description: 'Caregiver created successfully.',
        type: 'Success',
      });
      navigate(`/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${CAREGIVERS_LIST}`);
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };
  const handleSubmit = externalOnSubmit ?? internalOnSubmit;

  return <FormWrapper<Caregiver> defaultValues={data} fields={caregiverFormConfig} onSubmit={handleSubmit} />;
}
