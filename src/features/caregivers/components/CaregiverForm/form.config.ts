import { FieldGroup } from '@app/types/form.types';
import { ServicesFieldWrapper } from './ServicesFieldWrapper';

export type extraFormProps = { changePassword: string; changeEmail: string };

export const caregiverFormConfig: FieldGroup[] = [
  {
    title: 'Personal Information',
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        width: 'third',
        rules: { required: 'First name is required' },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        width: 'third',
        rules: { required: 'Last name is required' },
      },
      {
        name: 'email',
        label: 'Email',
        type: 'email',
        width: 'third',
        rules: { required: 'Email is required' },
      },
      {
        name: 'city',
        label: 'City',
        type: 'text',
        width: 'third',
        rules: { required: 'City is required' },
      },
      {
        name: 'phone',
        label: 'Phone',
        type: 'text',
        width: 'third',
      },
      {
        name: 'dateOfBirth',
        label: 'Date of Birth',
        type: 'date',
        width: 'third',
      },
      {
        name: 'gender',
        label: 'Gender',
        type: 'select',
        width: 'third',
        options: [
          { label: 'Male', value: 'Male' },
          { label: 'Female', value: 'Female' },
          { label: 'Other', value: 'Other' },
        ],
      },
      {
        name: 'nationalId',
        label: 'National ID',
        type: 'text',
        width: 'third',
      },
      {
        name: 'languagesSpoken',
        label: 'Languages Spoken (comma-separated)',
        type: 'list',
        width: 'third',
      },
    ],
  },
  {
    title: 'Certifications & Skills',
    fields: [
      {
        name: 'certifications',
        label: 'Certifications (comma-separated)',
        type: 'list',
        width: 'half',
      },
      {
        name: 'skills',
        label: 'Skills (comma-separated)',
        type: 'list',
        width: 'half',
      },
      {
        name: 'services',
        label: 'Services',
        type: 'custom',
        width: 'full',
        component: ServicesFieldWrapper,
      },
    ],
  },
  {
    title: 'Address & Coverage',
    fields: [
      // { name: 'baseAddress.street', label: 'Street', type: 'text', width: 'third' },
      // { name: 'baseAddress.city', label: 'City', type: 'text', width: 'third' },
      {
        name: 'coverageAreas',
        label: 'Coverage Areas (comma-separated)',
        type: 'list',
        width: 'half',
      },
      {
        name: 'travelRadiusKm',
        label: 'Travel Radius (km)',
        type: 'number',
        width: 'half',
      },
    ],
  },

  // {
  //   title: 'Availability & Settings',
  //   fields: [
  //     {
  //       name: 'rating',
  //       label: 'Rating (0-5)',
  //       type: 'number',
  //       width: 'third',
  //     },
  //     {
  //       name: 'active',
  //       label: 'Active',
  //       type: 'checkbox',
  //       width: 'third',
  //     },
  //   ],
  // },
];
