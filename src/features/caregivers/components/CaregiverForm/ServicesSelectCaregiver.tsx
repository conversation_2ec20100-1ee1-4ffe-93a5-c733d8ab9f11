import { ApiClient } from '@api/api-configuration';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Transfer } from 'antd';
import { Key } from 'antd/es/table/interface';
import { useEffect, useState } from 'react';

interface ServicesSelectCaregiverProps {
  value?: unknown; // form integration may pass unknown
  onChange: (val: unknown) => void;
  error?: string;
}

export function ServicesSelectCaregiver({ value, onChange }: ServicesSelectCaregiverProps) {
  const { openNotification } = useNotifications();
  const therapies = useSchedulingStore((state) => state.services);
  const setTherapies = useSchedulingStore((state) => state.setServices);

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // Fetch services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await ApiClient.serviceApi.services.getServicesServicesGet();
        setTherapies(response.data);
      } catch (err) {
        openNotification('topRight', {
          title: 'Services',
          description: 'Failed to fetch services.',
          type: 'Warning',
        });
      }
    };
    fetchServices();
  }, [openNotification, setTherapies]);

  // Update selected keys when value or therapies change
  useEffect(() => {
    if (!Array.isArray(value) || therapies.length === 0) {
      setSelectedKeys([]);
      return;
    }
    const therapyMap = new Map(therapies.map((t) => [String(t.serviceId), t]));
    const keys = value
      .map((v) => {
        const key = String(v);

        return therapyMap.has(key) ? key : null;
      })
      .filter((k): k is string => k !== null);
    setSelectedKeys(keys);
  }, [value, therapies]);
  return (
    <Transfer
      dataSource={therapies.map((t) => ({
        key: String(t.serviceId),
        name: t.name,
      }))}
      titles={['Services', 'Selected']}
      targetKeys={selectedKeys}
      onChange={(keys: Key[]) => onChange(keys.map((k) => String(k)))}
      render={(item) => item.name}
      listStyle={{ width: 200, height: 300 }}
      showSearch
      filterOption={(inputValue, item) => item.name.toLowerCase().includes(inputValue.toLowerCase())}
    />
  );
}
