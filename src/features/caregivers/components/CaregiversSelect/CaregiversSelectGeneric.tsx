import { ApiClient } from '@api/api-configuration';
import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import AppAvatar from '@app/components/ui/Avatar';
import {
  GenericSearchSelect,
  type ApiFetchFunction,
  type ApiQueryParams,
} from '@app/components/ui/GenericSearchSelect';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { SelectProps } from 'antd';
import { memo } from 'react';

type CaregiversSelectProps = {
  onCaregiverChange?: (caregiver: CaregiverWithServices | CaregiverWithServices[] | undefined) => void;
  placeholder?: string;
  pageSize?: number;
  minSearchLength?: number;
  searchDebounceMs?: number;
} & Omit<SelectProps, 'options' | 'onChange' | 'loading' | 'onSearch'>;

// Memoized option component for performance
const CaregiverOption = memo(({ firstName, lastName }: { firstName: string; lastName: string }) => {
  const fullname = `${firstName} ${lastName}`;
  const fullnameLetters = getInitials(fullname, { fallback: '' });

  return (
    <div className="flex items-center gap-2">
      <AppAvatar isSelected={true} fullName={fullname} value={fullnameLetters} />
      {`${firstName}, ${lastName}`}
    </div>
  );
});
CaregiverOption.displayName = 'CaregiverOption';

// API fetch function for caregivers
const fetchCaregivers: ApiFetchFunction<CaregiverWithServices> = async (params: ApiQueryParams) => {
  return await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet(params);
};

export const CaregiversSelectGeneric = ({
  onCaregiverChange,
  placeholder = 'Select Caregiver',
  pageSize = 10,
  minSearchLength = 3,
  searchDebounceMs = 300,
  ...selectProps
}: CaregiversSelectProps) => {
  return (
    <GenericSearchSelect<CaregiverWithServices>
      {...selectProps}
      apiFetch={fetchCaregivers}
      getItemId={(caregiver) => caregiver.caregiverId}
      getItemLabel={(caregiver) => `${caregiver.firstName} ${caregiver.lastName}`}
      onSelectionChange={onCaregiverChange}
      placeholder={placeholder}
      pageSize={pageSize}
      minSearchLength={minSearchLength}
      searchDebounceMs={searchDebounceMs}
      searchPlaceholder="Search caregivers"
      renderOption={(caregiver) => <CaregiverOption firstName={caregiver.firstName} lastName={caregiver.lastName} />}
    />
  );
};

export default CaregiversSelectGeneric;
