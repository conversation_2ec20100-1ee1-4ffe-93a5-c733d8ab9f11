import { Caregiver, Service } from '@api/READ_ONLY/caregiver_api/Api';
import DateTimeDisplay from '@app/components/common/DateTimeDisplay';
import TableLink from '@app/components/common/TableLink';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Typography } from 'antd';
import { TruncatedList } from '../../../../components/common/TruncatedList';

export interface CaregiverRow extends Omit<Caregiver, 'caregiverId'>, TableRow {
  id: string | number;
  caregiverId: number;
}

// ...existing code...

export const getCaregiverTableColumns = (
  editNavigation?: (caregiverId: number) => void
): BaseColumn<CaregiverRow>[] => [
  {
    id: 'name',
    header: 'Name',
    accessor: (row) => `${row.firstName} ${row.lastName}`,
    width: 220,
    cellRenderer: (_, caregiver) =>
      editNavigation ? (
        <TableLink onClick={() => editNavigation(caregiver.caregiverId)}>
          <ClientDisplay firstName={caregiver.firstName || ''} lastName={caregiver.lastName || ''} />
        </TableLink>
      ) : (
        <ClientDisplay firstName={caregiver.firstName || ''} lastName={caregiver.lastName || ''} />
      ),
  },
  {
    id: 'dateOfBirth',
    header: 'Date of Birth',
    accessor: 'dateOfBirth',
    width: 150,
    cellRenderer: (date) => (
      <DateTimeDisplay startTime={date as string} showDateSeparately={false} dateFormat="MMM DD, YYYY" timeFormat="" />
    ),
  },
  // {
  //   dataIndex: 'gender',
  //   key: 'gender',
  //   title: 'Gender',
  //   width: 100,
  //   align: 'center',
  //   render: (gender) => <Typography>{typeof gender === 'string' ? gender : '-'}</Typography>,
  // },
  {
    id: 'phone',
    header: 'Phone',
    accessor: 'phone',
    width: 150,
    type: 'phone',
    cellRenderer: (phone) => <Typography>{(phone as string) || '-'}</Typography>,
  },
  {
    id: 'email',
    header: 'Email',
    accessor: 'email',
    width: 200,
    type: 'email',
    cellRenderer: (email) => <Typography>{(email as string) || '-'}</Typography>,
  },
  {
    id: 'username',
    header: 'Username',
    accessor: 'username',
    width: 140,
    cellRenderer: (username) => <Typography>{username as string}</Typography>,
  },

  // {
  //   dataIndex: ['baseAddress', 'street'],
  //   key: 'baseAddress.street',
  //   title: 'Street',
  //   width: 140,
  //   render: (_, record) => <Typography>{record??.street || '-'}</Typography>,
  {
    id: 'services',
    header: 'Services',
    accessor: 'services',
    width: 250,
    cellRenderer: (value) => (
      <TruncatedList
        items={
          Array.isArray(value)
            ? value.map((service: Service) => ({
                id: service.serviceId,
                name: service.name,
              }))
            : []
        }
        maxItems={3}
        separator=", "
      />
    ),
  },
  //       : '-',
  // },
  // {
  //   dataIndex: 'travelRadius',
  //   key: 'travelRadius',
  //   title: 'Travel Radius (km)',
  //   width: 160,
  //   align: 'center',
  //   render: (radius) => <Typography>{(radius as string) ?? '-'}</Typography>,
  // },
  // ...existing code...
  // {
  //   dataIndex: 'languagesSpoken',
  //   key: 'languagesSpoken',
  //   title: 'Languages',
  //   width: 160,
  //   render: (langs) =>
  //     Array.isArray(langs) && langs.length > 0 ? langs.map((l) => <Tag key={l as Key}>{l as string}</Tag>) : '-',
  // },
  // {
  //   dataIndex: 'rating',
  //   key: 'rating',
  //   title: 'Rating',
  //   width: 100,
  //   align: 'center',
  //   render: (rating) => <Typography>{typeof rating === 'number' ? rating.toFixed(1) : '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'active',
  //   key: 'active',
  //   title: 'Active',
  //   width: 80,
  //   align: 'center',
  //   render: (active) => <Checkbox checked={!!active} disabled />,
  // },
  // {
  //   dataIndex: 'createdAt',
  //   key: 'createdAt',
  //   title: 'Created At',
  //   width: 180,
  //   render: (date) => (date ? new Date(date as string).toLocaleString() : '-'),
  // },
  // {
  //   dataIndex: 'updatedAt',
  //   key: 'updatedAt',
  //   title: 'Updated At',
  //   width: 180,
  //   render: (date) => (date ? new Date(date as string).toLocaleString() : '-'),
  // },
];
