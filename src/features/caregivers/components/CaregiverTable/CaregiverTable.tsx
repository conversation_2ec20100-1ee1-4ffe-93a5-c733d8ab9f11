import Table from '@app/features/table/Table';
import { APP_PREFIX, CAREGIVERS_PREFIX, EDIT_CAREGIVER, NEW_CAREGIVER } from '@app/routes/urls';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useNavigate } from 'react-router-dom';

const CaregiverTable = () => {
  const navigate = useNavigate();
  const caregivers = useSchedulingStore((state) => state.caregivers);
  const loading = useSchedulingStore((state) => state.loadingCaregivers);
  return (
    <div className="w-full h-full">
      <Table
        data={caregivers}
        totalData={caregivers.length}
        loadingDataFetch={loading}
        addNavigate={() => navigate(`/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${NEW_CAREGIVER}`)}
        exportComponent={<></>}
        localStorageKey="DoctorTable"
        onRowClick={(record) => {
          const path = `/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${EDIT_CAREGIVER.replace(':id', `${record.caregiverId}`)}`;
          navigate(path, { state: record });
        }}
      />
    </div>
  );
};

export default CaregiverTable;
