import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, CAREGIVERS_PREFIX, NEW_CAREGIVER } from '@app/routes/urls';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { CaregiverRow, getCaregiverTableColumns } from './caregiverTableColumns.config';

const CAREGIVER_URL_PARAM_CONFIG = {
  external: {
    searchTerm: 'search',
  },
} as const;

interface CaregiverTableProps {
  onDataChange?: (data: CaregiverRow[], totalCount: number) => void;
  onLoadingChange?: (loading: boolean) => void;
}

const CaregiverTable: React.FC<CaregiverTableProps> = ({ onDataChange, onLoadingChange }) => {
  const navigate = useNavigate();

  const transformCaregiverToRow = (caregiver: Caregiver): CaregiverRow => ({
    ...caregiver,
    id: caregiver.caregiverId,
  });

  const fetchCaregivers = async (params: { limit: number; offset: number; query?: string }) => {
    const searchQuery = { ...(params.query ? { query: params.query } : {}) };
    const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet({
      ...searchQuery,
      limit: params.limit,
      offset: params.offset,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'caregivers',
      urlParamConfig: CAREGIVER_URL_PARAM_CONFIG,
      fetchData: fetchCaregivers,
      transformData: transformCaregiverToRow,
      columns: getCaregiverTableColumns(),
      searchPlaceholder: 'Search caregivers...',
      addButton: {
        text: 'Add Caregiver',
        onClick: () => navigate(`/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${NEW_CAREGIVER}`),
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [navigate]
  );

  return <GenericDataTable config={tableConfig} onDataChange={onDataChange} onLoadingChange={onLoadingChange} />;
};

export default CaregiverTable;
