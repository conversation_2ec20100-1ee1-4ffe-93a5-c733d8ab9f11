import { ApiClient } from '@api/api-configuration';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { ServiceRequestCreateReq, ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import { VisitCreate } from '@api/READ_ONLY/visits_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { DateTimeRecurrenceValues } from '@feat-service-requests/types';
import { useMutation } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';
import { Dayjs } from 'dayjs';

interface UseCreateServiceRequestProps {
  patient: Client | undefined;
  notes: string;
  dateTimeRecurrenceValues: DateTimeRecurrenceValues;
  resetAllValuesToDefault: () => void;
  serviceIds: number[];
  rruleString: string;
  visitDates: Dayjs[];
}

const combineDateAndTime = ({ date, time }: { date: Dayjs; time: Dayjs }) =>
  date.hour(time.hour()).minute(time.minute()).second(0).millisecond(0).format('YYYY-MM-DDTHH:mm:ss');

export const useCreateServiceRequest = ({
  dateTimeRecurrenceValues,
  patient,
  notes,
  serviceIds,
  resetAllValuesToDefault,
  rruleString,
  visitDates,
}: UseCreateServiceRequestProps) => {
  const { openNotification } = useNotifications();

  const createNewVisitsRequestMutation = useMutation({
    mutationFn: ({ payload }: { payload: VisitCreate[] }) => ApiClient.visitsApi.visits.createVisitsVisitsPost(payload),
    onSuccess: () => {
      openNotification('topRight', {
        title: `Service Request`,
        description: 'Visit created successfully. ',
        type: 'Success',
      });

      resetAllValuesToDefault();
    },
    onError: (error) => {
      console.log('error from POST visits', error);

      openNotification('topRight', {
        title: `Service Request`,
        description: 'Visits creation failed.',
        type: 'Warning',
      });
    },
  });

  const createNewServicesRequestMutation = useMutation({
    mutationFn: ({ payload }: { payload: ServiceRequestCreateReq }) =>
      ApiClient.serviceRequestsApi.serviceRequests.createRequestServiceRequestsPost(payload),
    onSuccess: (res: AxiosResponse<ServiceRequestResponse>) => {
      // TODO : again silent return handle it ? show something ?
      if (!patient) return;

      // /**
      //  * TODO: the address need to be changed with proprity. If the user selecs and address in service request form then we need to send this address here
      //  * else we use the patient's address
      //  */

      const payload = visitDates.map((date) => {
        const startTime = combineDateAndTime({
          date: date,
          time: dateTimeRecurrenceValues.fromTime,
        });

        const endTime = combineDateAndTime({
          date: date,
          time: dateTimeRecurrenceValues.toTime,
        });

        return {
          serviceRequestId: res.data.serviceRequestId,
          clientId: patient.clientId,
          startTime: startTime,
          endTime: endTime,
          notes: notes.trim() ?? '',
          street: patient?.street || '',
          city: patient?.city || '',
          postalCode: patient?.postalCode || '',
        };
      });

      createNewVisitsRequestMutation.mutate({
        payload: payload,
      });
    },
    onError: (error) => {
      console.log('error from POST service request', error);

      openNotification('topRight', {
        title: `Service Request`,
        description: 'Visit creation failed.',
        type: 'Warning',
      });
    },
  });

  const handleOnCreateButtonClick = () => {
    // Maybe notify that this happened is a silent return
    if (!patient?.clientId) return;

    const fromDate = combineDateAndTime({
      date: dateTimeRecurrenceValues.startingDate,
      time: dateTimeRecurrenceValues.fromTime,
    });

    const toDate = combineDateAndTime({
      date: dateTimeRecurrenceValues.endingDate || dateTimeRecurrenceValues.startingDate,
      time: dateTimeRecurrenceValues.toTime,
    });

    createNewServicesRequestMutation.mutate({
      payload: {
        clientId: patient.clientId,
        serviceIds: serviceIds,
        fromDate: fromDate,
        toDate: toDate,
        notes: notes.trim() ?? '',
        rrule: rruleString,
      },
    });
  };

  return {
    actions: {
      handleOnCreateButtonClick,
    },
    isActionInProgress: createNewServicesRequestMutation.isPending || createNewVisitsRequestMutation.isPending,
  };
};
