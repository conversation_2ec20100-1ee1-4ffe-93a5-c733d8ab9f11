import { ApiClient } from '@api/api-configuration';
import { SuggestedVisitsRequest } from '@api/READ_ONLY/visits_api/Api';
import { useQuery } from '@tanstack/react-query';
import { Dayjs } from 'dayjs';

interface UseSuggestedVisitsProps {
  visitDates: string[];
  dateTime: {
    fromTime: Dayjs;
    toTime: Dayjs;
  };
  caregiverIds: number[];
  serviceIds: number[];
}

export const useSuggestedVisits = (info: UseSuggestedVisitsProps) => {
  const payload: SuggestedVisitsRequest = {
    dates: info.visitDates,
    timeSlot: {
      startTime: info.dateTime.fromTime?.format('HH:mm') ?? '',
      endTime: info.dateTime.toTime?.format('HH:mm') ?? '',
    },
    caregiverIds: [],
    serviceIds: info.serviceIds,
  };

  const isValidToFetchAvailableVisits =
    info.visitDates.length > 0 &&
    Boolean(info.dateTime.fromTime) &&
    Boolean(info.dateTime.toTime) &&
    info.serviceIds.length > 0;

  const { data, isFetching, isLoading } = useQuery({
    queryFn: () =>
      ApiClient.visitsApi.visits.postSuggestedVisitsFromAvailabilityVisitsSuggestedFromAvailabilityPost(payload),
    queryKey: ['suggestedVisits', payload],
    enabled: isValidToFetchAvailableVisits,
  });

  return {
    availableVisits: data?.data || [],
    areVisitsFetching: isFetching,
    areVisitsLoading: isLoading,
    isValidToFetchAvailableVisits,
  };
};

export default useSuggestedVisits;
