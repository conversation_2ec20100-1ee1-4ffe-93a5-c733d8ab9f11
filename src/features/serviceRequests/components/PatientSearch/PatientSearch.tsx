import { ApiClient } from '@api/api-configuration';
import { Client, ClientCreate } from '@api/READ_ONLY/client_api/Api';
import FetchSearchSelect, { FetchDataFn } from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Input, Modal } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { AxiosResponse } from 'axios';
import { ReactNode, useState } from 'react';

export function PatientSearch({
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
}: {
  searchTerm?: string;
  debounce?: number;
  disabled?: boolean;
  setValue: (e: Client | Client[] | undefined | string) => void;
  value?: string;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: Client) => boolean;
}) {
  const clients = useSchedulingStore((state) => state.clients);
  const setClients = useSchedulingStore((state) => state.setClients);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newPhone, setNewPhone] = useState('');
  // const navigate = useNavigate();
  function isValidPhone(phone: string): boolean {
    const cleaned = phone.replace(/\s|-/g, ''); // remove spaces and dashes
    const phoneRegex = /^(\+?\d{1,4})?[\s.-]?\(?\d{2,4}\)?[\s.-]?\d{3,4}[\s.-]?\d{3,4}$/;

    return phoneRegex.test(cleaned);
  }
  const fetchData: FetchDataFn<Client> = async (searchValue) => {
    console.log('Fetching patients with search:', searchValue);
    let response!: AxiosResponse<Client[]>;
    if (searchValue) {
      // Fetch all clients if no search term
      response = await ApiClient.clientApi.clients.searchClientClientsSearchDataGet(searchValue || '');
    } else {
      // Fetch all clients if no search term
      response = await ApiClient.clientApi.clients.getClientsClientsGet();
    }
    setClients(response.data); // or response.data depending on client
    return {
      Results: response.data,
      TotalResults: response.data.length,
    };
  };

  const handleNewPatient = async () => {
    const newPatient: ClientCreate = {
      firstName: newPhone,
      lastName: newPhone,
      username: newPhone,
      dateOfBirth: new Date().toISOString().split('T')[0],
      phone: newPhone,
    };

    try {
      await ApiClient.clientApi.clients.createClientClientsPost(newPatient);

      // 1. Fetch the newly created patient by phone
      const url = `http://10.10.20.113:4050/clients/search/${newPhone}?offset=0&limit=1`;
      const response = await fetch(url);
      const data = await response.json();

      const createdClient = data?.[0] || data?.data?.[0]; // adjust if needed depending on response shape

      if (createdClient) {
        // 2. Add to state
        setClients([createdClient]);

        // 3. Select the new patient
        setValue(createdClient.clientId);
      }
    } catch (error) {
      console.error('Failed to create/select new patient', error);
    } finally {
      setIsModalOpen(false);
    }
  };

  const handleNoPatientFound = (input: string | undefined) => {
    if (input && isValidPhone(input)) {
      setNewPhone(input);
      setIsModalOpen(true);
    }
  };

  // const customTagRender = (props: CustomTagProps) => {
  //   const { label, closable, onClose } = props;
  //   console.log('test custom');
  //   return (
  //     <Tag color="blue" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
  //       👤 {label}
  //     </Tag>
  //   );
  // };
  return (
    <>
      <FetchSearchSelect<Client>
        data={{
          Results: clients,
          TotalResults: clients.length,
        }}
        // tagRender={customTagRender}
        disabled={disabled}
        value={value}
        onValueChange={(item) => setValue(item)}
        getId={(item) => item.clientId}
        getLabel={(item) => {
          const isUnknown = item?.firstName === 'UNKNOWN' && item?.lastName === 'UNKNOWN';
          if (isUnknown) {
            return item?.phone ?? ''; // <- default to empty string if phone is undefined
          }

          const name = `${item?.firstName ?? ''} ${item?.lastName ?? ''}`.trim();
          const phone = item?.phone ?? '';

          return `${name} - ${phone}`.trim(); // always returns a string
        }}
        fetchData={fetchData}
        placeholder="Select Patient"
        optionRender={optionRender}
        optionDisable={optionDisable}
        allowNewValue={true}
        newValueText="No patient found. Add manually?"
        newValueCb={(e, input) => handleNoPatientFound(input)}
      />

      <Modal
        title="Create New Patient"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleNewPatient}
      >
        <p>New patient will be created with phone:</p>
        <Input value={newPhone} disabled />
      </Modal>
    </>
  );
}
