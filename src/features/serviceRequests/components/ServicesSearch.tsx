import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import FetchSearchSelect from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { CommonListResponse } from '@app/types/common.types';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { DefaultOptionType } from 'antd/es/select';
import { ReactNode } from 'react';

type Option = {
  label: string;
  value: string;
  service: Service;
};

const toOption = (s: Service): Option => ({
  label: `${s.name} ${s.estimatedTimeMinutes} minutes`,
  value: s.serviceId.toString(), // use unique ID as value
  service: s,
});

export function ServicessSearch({
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
  selectedValues,
}: {
  disabled?: boolean;
  setValue: (v: Service | Service[] | string | undefined) => void;
  value: Service | Service[] | string | undefined;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: Service) => boolean;
  selectedValues: Service[];
}) {
  const services = useSchedulingStore((state) => state.services);
  const setServices = useSchedulingStore((state) => state.setServices);

  // Get mapped value from selected Service (or array of Service)
  const mappedValue: string | undefined = (() => {
    if (!value) return undefined;
    if (typeof value === 'string') return value;
    if (Array.isArray(value)) return value.length > 0 ? value[0].serviceId.toString() : undefined;
    return value.serviceId.toString();
  })();

  const handleChange = (v: string | Option | Option[] | undefined) => {
    if (typeof v === 'string' || v === undefined) {
      setValue(v);
      return;
    }

    if (Array.isArray(v)) {
      setValue(v.map((opt) => opt.service));
    } else {
      setValue(v.service);
    }
  };

  const fetchData = async () => {
    const response = await ApiClient.serviceApi.services.getServicesServicesGet();
    console.log('selected', selectedValues);

    const filtered = response.data.filter(
      (d) => !(selectedValues ?? []).some((selected) => selected.serviceId === d.serviceId)
    );

    setServices(filtered);

    return {
      Results: filtered.map(toOption),
      TotalResults: filtered.length,
    } as CommonListResponse<Option>;
  };

  return (
    <FetchSearchSelect<Option>
      data={{
        Results: services.map(toOption),
        TotalResults: services.length,
      }}
      disabled={disabled}
      value={mappedValue}
      onValueChange={handleChange}
      getId={(o) => o.value}
      getLabel={(o) => o.label}
      fetchData={fetchData}
      placeholder="Select Service"
      optionRender={optionRender}
      optionDisable={(o) => (optionDisable ? optionDisable(o.service) : false)}
    />
  );
}
