import FormElement from '@app/components/ui/FormElement';
import { FREQUENCY_OPTIONS } from '@feat-service-requests/const';
import { Button, Checkbox, DatePicker, InputNumber, Modal, Select, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useState } from 'react';

// I do not use the types from the rest of the types.ts cause I do not want to couple this component to the rest of the codebase.
type RecurringEventValues = {
  startingDate: Dayjs;
  endingDate: Dayjs | null;
  interval: number;
  frequency: 'daily' | 'weekly';
  repetitionDaysOfTheWeek: number[];
};

interface RecurringEventsModalProps {
  isOpen: boolean;
  initialValues: RecurringEventValues;
  onConfirm: (value: RecurringEventValues) => void;
  onCancel: () => void;
}

const WEEK_DAYS = [0, 1, 2, 3, 4, 5, 6];

const FREQUENCY_SELECT_OPTIONS = Object.values(FREQUENCY_OPTIONS).map((value) => ({
  label: value,
  value,
}));

// TODO : Replace all this with the actual translations -- Will be removed when translations are added
const DAYS_TRANSLATION = {
  'sunday-letter': 'S',
  'monday-letter': 'M',
  'tuesday-letter': 'T',
  'wednesday-letter': 'W',
  'thursday-letter': 'T',
  'friday-letter': 'F',
  'saturday-letter': 'S',
  'sunday-full': 'Sunday',
  'monday-full': 'Monday',
  'tuesday-full': 'Tuesday',
  'wednesday-full': 'Wednesday',
  'thursday-full': 'Thursday',
  'friday-full': 'Friday',
  'saturday-full': 'Saturday',
} as const;

// Use Translation key for the label
const REPEATED_DAYS_DATA: {
  label: keyof typeof DAYS_TRANSLATION;
  value: number;
  tooltip: keyof typeof DAYS_TRANSLATION;
}[] = [
  { label: 'sunday-letter', value: 0, tooltip: 'sunday-full' },
  { label: 'monday-letter', value: 1, tooltip: 'monday-full' },
  { label: 'tuesday-letter', value: 2, tooltip: 'tuesday-full' },
  { label: 'wednesday-letter', value: 3, tooltip: 'wednesday-full' },
  { label: 'thursday-letter', value: 4, tooltip: 'thursday-full' },
  { label: 'friday-letter', value: 5, tooltip: 'friday-full' },
  { label: 'saturday-letter', value: 6, tooltip: 'saturday-full' },
];

const RecurringEventsModal = ({ isOpen, initialValues, onConfirm, onCancel }: RecurringEventsModalProps) => {
  const [recurringValues, setRecurringValues] = useState<RecurringEventValues>({
    startingDate: initialValues.startingDate,
    endingDate: initialValues.endingDate,
    interval: initialValues.interval,
    frequency: initialValues.frequency,
    repetitionDaysOfTheWeek: initialValues.repetitionDaysOfTheWeek,
  });

  const handleRepetitionDaysChange = (selectedDay: number) => {
    const isDaySelected = recurringValues.repetitionDaysOfTheWeek.includes(selectedDay);

    if (isDaySelected) {
      // remove it
      setRecurringValues((prev) => ({
        ...prev,
        repetitionDaysOfTheWeek: prev.repetitionDaysOfTheWeek.filter((day) => day !== selectedDay),
      }));
      return;
    }

    // or add it
    setRecurringValues((prev) => ({
      ...prev,
      repetitionDaysOfTheWeek: [...prev.repetitionDaysOfTheWeek, selectedDay],
    }));
  };

  const isConfirmedButtonDisabled =
    (recurringValues.repetitionDaysOfTheWeek.length === 0 && recurringValues.frequency === 'weekly') ||
    !recurringValues.startingDate ||
    !recurringValues.endingDate;

  return (
    <Modal
      title="Recurring events"
      okText="Save"
      cancelText="Cancel"
      closable={{ 'aria-label': 'Custom Close Button' }}
      open={isOpen}
      onOk={() => onConfirm(recurringValues)}
      onCancel={() => {
        onCancel();
        setRecurringValues(initialValues);
      }}
      okButtonProps={{
        disabled: isConfirmedButtonDisabled,
      }}
    >
      <div className="flex flex-col  w-full">
        <FormElement label="Starting date" required preserveErrorSpace={false}>
          <DatePicker
            value={recurringValues.startingDate}
            onChange={(dayJsDate) =>
              setRecurringValues({
                ...recurringValues,
                startingDate: dayJsDate,
                endingDate: dayJsDate?.add(1, 'day') ?? null,
              })
            }
            format="DD-MM-YYYY"
            placeholder="Select date"
            className="w-full"
            disabledDate={(currentDate) => currentDate && currentDate < dayjs().startOf('day')}
            allowClear={false}
          />
        </FormElement>

        <div className="rounded flex flex-col gap-4 mt-4 w-full">
          <div className="flex flex-wrap items-center gap-3 w-full">
            <div className="flex gap-4 items-center mb-1">
              <span className="mr-2">Frequency</span>

              <Select
                className="w-[140px]"
                options={FREQUENCY_SELECT_OPTIONS}
                value={recurringValues.frequency}
                onChange={(value) => {
                  setRecurringValues({
                    ...recurringValues,
                    frequency: value,
                    interval: 1,
                    repetitionDaysOfTheWeek: [],
                  });
                }}
              />

              <span className="mr-2">Every</span>

              <InputNumber
                min={1}
                className="!w-16 ml-2"
                value={recurringValues.interval}
                onChange={(value) => {
                  setRecurringValues({ ...recurringValues, interval: value ?? 1 });
                }}
              />

              <span className="mr-2">
                {recurringValues.frequency === 'daily'
                  ? `${recurringValues.interval > 1 ? 'days' : 'day'}`
                  : `${recurringValues.interval > 1 ? 'weeks' : 'week'}`}
              </span>
            </div>

            {recurringValues.frequency === FREQUENCY_OPTIONS.WEEKLY && (
              <div className="w-full flex justify-between">
                <div className="flex flex-wrap items-center gap-2">
                  {REPEATED_DAYS_DATA.map((item) => (
                    <Tooltip title={DAYS_TRANSLATION[item.tooltip]} key={item.value}>
                      <Button
                        size="small"
                        shape="circle"
                        type={recurringValues.repetitionDaysOfTheWeek.includes(item.value) ? 'primary' : 'default'}
                        onClick={() => handleRepetitionDaysChange(item.value)}
                      >
                        {DAYS_TRANSLATION[item.label]}
                      </Button>
                    </Tooltip>
                  ))}
                </div>

                <Checkbox
                  checked={recurringValues.repetitionDaysOfTheWeek.length === 7}
                  onChange={(e) => {
                    setRecurringValues((prev) => ({
                      ...prev,
                      repetitionDaysOfTheWeek: e.target.checked ? WEEK_DAYS : [],
                    }));
                  }}
                >
                  Every day
                </Checkbox>
              </div>
            )}
          </div>

          <FormElement label={<p className="mr-4">Ending date</p>} required preserveErrorSpace={false}>
            <DatePicker
              value={recurringValues.endingDate}
              onChange={(dayJsDate) => setRecurringValues({ ...recurringValues, endingDate: dayJsDate })}
              format="DD-MM-YYYY"
              placeholder="Select date"
              className="w-full"
              disabledDate={(currentDate) => {
                if (!currentDate) return false;

                const start = recurringValues.startingDate;
                const min = start.add(1, 'day');
                const max = start.add(1, 'year');

                // Disable before min OR after max
                return currentDate.isBefore(min, 'day') || currentDate.isAfter(max, 'day');
              }}
              allowClear={false}
            />
          </FormElement>
        </div>
      </div>
    </Modal>
  );
};

export default RecurringEventsModal;
