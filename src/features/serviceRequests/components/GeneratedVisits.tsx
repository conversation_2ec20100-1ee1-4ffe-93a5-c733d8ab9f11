// components/GeneratedVisits.tsx
import { ApiClient } from '@api/api-configuration';
import { Caregiver, Service } from '@api/READ_ONLY/caregiver_api/Api';
import { SuggestedVisitSlot } from '@api/READ_ONLY/visits_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

// Why use dayjs? is there a specific scenario where plain javascript cannot handle it?
dayjs.extend(isSameOrBefore);

export function getDateListBetween(startStr: string, endStr: string): string[] {
  const start = dayjs(startStr).startOf('day');
  const end = dayjs(endStr).startOf('day');
  const result: string[] = [];

  let current = start.clone();

  while (current.isSameOrBefore(end)) {
    result.push(current.format('YYYY-MM-DD'));
    current = current.add(1, 'day');
  }

  return result;
}
const GeneratedVisits = () => {
  // Basically we are using a context to avoid 1 step drilling ? Why we get the data from the formContext
  // My main issue is that i have to maintain 2 places here and make sure that i have the correct values.

  const { watch } = useFormContext();
  const { openNotification } = useNotifications();

  // A small component should not have the responsibility to fetch data.
  const caregiversStored = useSchedulingStore((state) => state.caregivers);
  const therapiesStored = useSchedulingStore((state) => state.services);

  // This component uses state from the Parent form that i was not even aware of.... How we could improve this?

  // Using watch to have a live connection to the form for every single
  const dateRange = watch('dateRange');
  const repeatInterval = watch('repeatInterval'); // number of visits per cycle
  const repeatUnit = watch('repeatUnit'); // 'week' | 'month'
  const repeatDays = watch('repeatDays') || ([] as string[]);
  const time = watch('time');
  const caregivers = watch('caregivers') || [];
  const therapies = watch('therapies') || [];

  const [visits, setVisits] = useState<SuggestedVisitSlot[]>([]);

  // This useEffect has so many dependencies that it is hard to understand what it does.
  useEffect(() => {
    const getProposedVisits = async () => {
      try {
        const startTime = dayjs(dateRange.start).format('HH:mm');
        const endTime = dayjs(dateRange.end).format('HH:mm');
        console.log('caregivers', caregivers);

        const response =
          await ApiClient.visitsApi.visits.postSuggestedVisitsFromAvailabilityVisitsSuggestedFromAvailabilityPost({
            dates: getDateListBetween(dateRange.start, dateRange.end),
            timeSlot: {
              startTime,
              endTime,
            },
            caregiverIds: caregivers.map((c: Caregiver) => c),
            serviceIds: therapies.map((t: Service) => t.serviceId),
          });

        setVisits(response.data);
        console.log('response', response);
        openNotification('topRight', {
          title: `Suggested visits`,
          description: 'Suggested visits fetched successfully. ',
          type: 'Success',
        });
        // TODO: Process and set visits from response here
        // setVisits(response.data); <-- assuming backend returns visits array
      } catch (err) {
        console.error('Error fetching suggested visits:', err);
        openNotification('topRight', {
          title: `Suggested visits`,
          description: 'Suggested visits fetching failed.',
          type: 'Warning',
        });
      }
    };

    if (!dateRange) {
      setVisits([]);
      console.log('inside');
      return;
    }

    getProposedVisits();
    // setVisits(generated);
  }, [dateRange, repeatInterval, repeatUnit, repeatDays, time, caregivers, therapies]);

  console.log('caregivers', caregivers);

  // Good practice to avoid rendering if not needed.
  if (visits.length === 0) return null;

  // but where is the loading state since we are using useEffect ?

  return (
    <div className="space-y-2 mt-2">
      <div className="h-[300px] overflow-y-scroll flex flex-wrap gap-2">
        {visits.map((v) => {
          const caregiverCurrentServiceRequest = caregiversStored.find(
            (c: Caregiver) => c.caregiverId === v.caregiverId
          );
          const therapyCurrentServiceRequest = therapiesStored.find((t: Service) => t.serviceId === v.serviceIds[0]);

          return (
            <div
              key={v.serviceIds + ' ' + v.caregiverId}
              className={`bg-white rounded-lg shadow-md p-4 w-64
                ${'border border-green-300 text-green-800'}
                hover:shadow-lg transition-shadow
              `}
            >
              <div className="mb-2 font-semibold">
                {v.date}: {v.startTime}-{v.endTime}
              </div>

              <div className="text-sm mb-1">
                Therapy: <span className="font-medium">{therapyCurrentServiceRequest?.name ?? 'Unknown Therapy'}</span>
              </div>

              <div className="text-sm">
                Caregiver:{' '}
                <span className="font-medium">
                  {caregiverCurrentServiceRequest
                    ? caregiverCurrentServiceRequest?.firstName + ' ' + caregiverCurrentServiceRequest?.lastName
                    : 'Unknown caregiver'}
                </span>
              </div>

              <div
                className={`mt-3 inline-block px-3 py-1 rounded-full text-xs font-semibold
                  ${'bg-green-500 text-white'}`}
              >
                {'Available'}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GeneratedVisits;
