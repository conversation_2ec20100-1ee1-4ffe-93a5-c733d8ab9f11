import { Service } from '@api/READ_ONLY/services_api/Api';
import { formatMinutesToHours } from '@app/utils/dates/formatMinutesToHours';
import { Button } from 'antd';
import { forwardRef, memo, useCallback, useImperativeHandle, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { GiAlarmClock } from 'react-icons/gi';
import { MdDeleteForever } from 'react-icons/md';

type Slot = {
  id: number;
  service?: Service;
};

type ServicesSelectionPanelProps = {
  value: { id: number; estimatedTimeMinutes: number }[]; // parent only cares about selected service IDs
  onChange: (next: { id: number; estimatedTimeMinutes: number }[]) => void;
  totalEstimatedTimeOfServicesInMinutes: number;
  error?: string;
};

type SlotProps = {
  slot: Slot;
  selectedServices: Service[];
  onSetValue: (slotId: number, service: Service) => void;
  onDelete: (slotId: number) => void;
};

// Duplication of the type and also under CreateNewServiceRequestPanel.tsx
type ServicesSelectionPanelHandle = {
  reset: () => void;
};

// Memoized SlotItem
const SlotItemComponent = ({ slot, onDelete }: SlotProps) => (
  <div className="flex items-center gap-2 mb-2">
    {/* <ServicesSearch
      disabled={false}
      value={slot.service}
      selectedValues={selectedServices}
      onServiceSelect={(selectedService) => onSetValue(slot.id, selectedService as Service)}
    /> */}
    <Button type="text" icon={<MdDeleteForever color="red" size={22} />} danger onClick={() => onDelete(slot.id)} />
  </div>
);

const SlotItem = memo(SlotItemComponent);

// Main component
const ServicesSelectionPanel = forwardRef<ServicesSelectionPanelHandle, ServicesSelectionPanelProps>(
  ({ value, onChange, totalEstimatedTimeOfServicesInMinutes, error }, ref) => {
    const [slots, setSlots] = useState<Slot[]>([]);

    // Expose reset method to parent
    useImperativeHandle(ref, () => ({
      reset: () => {
        setSlots([]);
        onChange([]); // notify parent
      },
    }));

    const areAllServicesSlotsProperlyFilled = slots.every((slot) => slot.service);

    const handleAddService = () => {
      setSlots((prev) => {
        if (!areAllServicesSlotsProperlyFilled) return prev;
        return [...prev, { id: Date.now(), service: undefined }];
      });
    };

    const handleSelectedServicesUpdate = useCallback(
      (slotId: number, newService: Service) => {
        setSlots((prevSlots) => {
          const nextSlots = prevSlots.map((s) => (s.id === slotId ? { ...s, service: newService } : s));

          const selectedServices = nextSlots.reduce<{ id: number; estimatedTimeMinutes: number }[]>(
            (services, slot) => {
              if (slot.service)
                services.push({
                  id: slot.service.serviceId,
                  estimatedTimeMinutes: slot.service.estimatedTimeMinutes ?? 0,
                });
              return services;
            },
            []
          );

          onChange(selectedServices);

          return nextSlots;
        });
      },
      [onChange]
    );

    const handleDelete = useCallback(
      (slotId: number) => {
        const slotToDelete = slots.find((s) => s.id === slotId);
        if (!slotToDelete) return;

        setSlots((prev) => prev.filter((s) => s.id !== slotId));
        onChange(value.filter((s) => s.id !== slotToDelete.service?.serviceId));
      },
      [slots, value, onChange]
    );

    const selectedServices = slots.map((s) => s.service).filter((s): s is Service => s !== undefined);

    return (
      <div>
        <div className="w-full">
          <div className="mb-2 flex items-center justify-between">
            <div className="flex">
              <span className="text-red-500 mr-1">*</span>
              <span className="text-sm font-medium">Services : </span>
            </div>

            {/* <div className="text-base font-medium  px-2 py-1 rounded-lg ml-4 flex items-center gap-2 transform -translate-y-0.5">
              <GiAlarmClock size={20} className="text-blue-600" />

              <div>
                <span className="text-sm text-gray-900">Total services time:</span>
                <span className="text-blue-600 ml-2">{totalEstimatedTimeOfServicesInMinutes} minutes</span>
              </div>
            </div> */}

            <div className="flex items-center gap-2">
              <GiAlarmClock className="text-blue-600 text-xl" />
              <span className="text-sm text-gray-900 font-medium">Total services time:</span>
              <span className="text-blue-600 font-semibold">
                {formatMinutesToHours(totalEstimatedTimeOfServicesInMinutes)}
              </span>
            </div>
          </div>

          <div>
            {slots.map((slot) => (
              <SlotItem
                key={slot.id}
                slot={slot}
                selectedServices={selectedServices}
                onSetValue={handleSelectedServicesUpdate}
                onDelete={handleDelete}
              />
            ))}
          </div>

          {/* <Tooltip
            placement="right"
            title="Please fill your last service before adding a new one."
            open={!areAllServicesSlotsProperlyFilled ? undefined : false}
            arrow={{ pointAtCenter: true }}
            autoAdjustOverflow
          > */}
          <Button
            type="dashed"
            block
            icon={<FaPlus color={'var(--color-app-primary)'} size={16} />}
            className="min-h-[40px] mt-2"
            onClick={handleAddService}
            disabled={!areAllServicesSlotsProperlyFilled}
          >
            <span className="text-[16px] font-medium text-app-primary">Add Service</span>
          </Button>
          {/* </Tooltip> */}
        </div>

        <>{error && <p className="text-red-500 text-xs mt-1 ml-1">{error}</p>}</>
      </div>
    );
  }
);

// Set display name to remove warning in devtools
ServicesSelectionPanel.displayName = 'ServicesSelectionPanel';

export default ServicesSelectionPanel;
