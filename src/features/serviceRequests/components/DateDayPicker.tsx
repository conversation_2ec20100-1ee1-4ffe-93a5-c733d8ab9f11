import { DatePicker, TimePicker, message } from 'antd';
import type { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

type DateDayPickerProps = {
  value?: { start: Dayjs; end: Dayjs } | null;
  onChange?: (value: { start: Dayjs; end: Dayjs } | null) => void;
};

const DateDayPicker: React.FC<DateDayPickerProps> = ({ value, onChange }) => {
  const [fromDate, setFromDate] = useState<Dayjs | null>(value?.start ?? null);
  const [fromTime, setFromTime] = useState<Dayjs | null>(value?.start ?? null);
  const [toTime, setToTime] = useState<Dayjs | null>(value?.end ?? null);

  const validateAndTriggerChange = (date: Dayjs | null, startTime: Dayjs | null, endTime: Dayjs | null) => {
    if (date && startTime && endTime) {
      const start = date.hour(startTime.hour()).minute(startTime.minute()).second(0);
      const end = date.hour(endTime.hour()).minute(endTime.minute()).second(0);

      if (start.isAfter(end)) {
        message.error('Start time must be before end time.');
        onChange?.(null);
        return;
      }

      onChange?.({ start, end });
    } else {
      onChange?.(null);
    }
  };

  const handleFromDateChange = (date: Dayjs | null) => {
    setFromDate(date);
    validateAndTriggerChange(date, fromTime, toTime);
  };

  const handleFromTimeChange = (time: Dayjs | null) => {
    setFromTime(time);
    validateAndTriggerChange(fromDate, time, toTime);
  };

  const handleToTimeChange = (time: Dayjs | null) => {
    setToTime(time);
    validateAndTriggerChange(fromDate, fromTime, time);
  };

  // useEffect should never be used to sync state. Only if you want to sync react state with a 3rd party. For example Local Storage or Making an api call. Those things are external from react. So they consider 3rd party
  // Have it in mind like if i use useEffect to sync my state - like a HINT that i can improve things
  useEffect(() => {
    if (value) {
      setFromDate(value.start);
      setFromTime(value.start);
      setToTime(value.end);
    }
  }, [value]);

  return (
    <div>
      <div className="flex gap-4">
        <div className="flex flex-col gap-2 w-full">
          <div>Date</div>
          <DatePicker
            onChange={handleFromDateChange}
            value={fromDate}
            format="DD-MM-YYYY"
            placeholder="Select date"
            className="w-full"
          />
          <div className="flex gap-2">
            <div className="w-full">
              <div>Start Time</div>
              <TimePicker
                className="w-full"
                onChange={handleFromTimeChange}
                value={fromTime}
                format="HH:mm"
                minuteStep={15}
                needConfirm={false}
              />
            </div>
            <div className="w-full">
              <div>End Time</div>
              <TimePicker
                className="w-full"
                onChange={handleToTimeChange}
                value={toTime}
                format="HH:mm"
                minuteStep={15}
                needConfirm={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateDayPicker;
