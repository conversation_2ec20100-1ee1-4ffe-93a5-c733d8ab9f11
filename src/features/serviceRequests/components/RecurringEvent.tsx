import { Button, InputNumber, Select, TimePicker, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

const daysShort = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
const dayKeys = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

const RecurringEvent = () => {
  const { control, setValue, watch } = useFormContext();

  const repeatDays: string[] = watch('repeatDays') || [];
  const dateRange = watch('dateRange'); // [Dayjs, Dayjs]

  useEffect(() => {
    if (dateRange) {
      const startDay = dateRange[0].format('dddd'); // e.g. "Monday"
      setValue('repeatDays', [startDay], { shouldDirty: true });
    }
  }, [dateRange]);
  const toggleDay = (day: string) => {
    const updated = repeatDays.includes(day) ? repeatDays.filter((d) => d !== day) : [...repeatDays, day];

    setValue('repeatDays', updated, { shouldDirty: true });
  };

  return (
    <div className="rounded flex flex-col gap-4 mt-4 w-full">
      <div className="flex flex-wrap items-center gap-3 w-full">
        <span>Repeat every</span>

        <Controller
          name="repeatInterval"
          control={control}
          defaultValue={1}
          render={({ field }) => <InputNumber min={1} className="!w-16" {...field} />}
        />

        <Controller
          name="repeatUnit"
          control={control}
          defaultValue="week"
          render={({ field }) => (
            <Select
              className="min-w-[100px]"
              {...field}
              options={[
                { label: 'week', value: 'week' },
                { label: 'month', value: 'month' },
              ]}
            />
          )}
        />
      </div>

      {/* Day selection */}
      <div className="flex flex-wrap items-center gap-2">
        <span>Repeat on</span>
        {dayKeys.map((key, i) => (
          <Tooltip title={key} key={key}>
            <Button
              size="small"
              shape="circle"
              type={repeatDays.includes(key) ? 'primary' : 'default'}
              onClick={() => toggleDay(key)}
            >
              {daysShort[i]}
            </Button>
          </Tooltip>
        ))}
      </div>

      {/* Time selection */}
      <div className="flex items-center gap-2">
        <span>At time</span>
        <Controller
          name="time"
          control={control}
          defaultValue={dayjs().hour(9).minute(0)}
          render={({ field }) => (
            <TimePicker
              use12Hours
              format="h:mm A"
              size="large"
              value={field.value}
              onChange={field.onChange}
              className="min-w-[120px]"
            />
          )}
        />
      </div>
    </div>
  );
};

export default RecurringEvent;
