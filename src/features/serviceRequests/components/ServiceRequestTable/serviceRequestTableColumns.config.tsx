import { ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import DateTimeDisplay from '@app/components/common/DateTimeDisplay';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Tag, Typography } from 'antd';

export interface ServiceRequestRow extends Omit<ServiceRequestResponse, 'serviceRequestId'>, TableRow {
  id: string | number;
  serviceRequestId: number;
}

// ...existing code...

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return 'orange';
    case 'approved':
      return 'green';
    case 'rejected':
      return 'red';
    case 'in_progress':
      return 'blue';
    case 'completed':
      return 'success';
    default:
      return 'default';
  }
};

export const getServiceRequestTableColumns = (): BaseColumn<ServiceRequestRow>[] => [
  {
    id: 'client',
    header: 'Client',
    accessor: (row) => `${row.client?.firstName ?? ''} ${row.client?.lastName ?? ''}`.trim(),
    width: 180,
    cellRenderer: (_, request) => (
      <ClientDisplay firstName={request.client.firstName || ''} lastName={request.client.lastName || ''} />
    ),
  },
  {
    id: 'preferredCaregiver',
    header: 'Preferred Caregiver',
    accessor: (row) =>
      row.preferredCaregiver
        ? `${row.preferredCaregiver.firstName ?? ''} ${row.preferredCaregiver.lastName ?? ''}`.trim()
        : '',
    width: 180,
    cellRenderer: (_, request) => (
      <div>
        {request?.preferredCaregiver ? (
          <>
            <Typography.Text>
              {request.preferredCaregiver.firstName} {request.preferredCaregiver.lastName}
            </Typography.Text>
            <div>
              <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                {request.preferredCaregiver.phone}
              </Typography.Text>
            </div>
          </>
        ) : (
          <Typography.Text type="secondary">No preference</Typography.Text>
        )}
      </div>
    ),
  },
  {
    id: 'fromDate',
    header: 'From Date',
    accessor: 'fromDate',
    width: 130,
    cellRenderer: (_, request) => (
      <DateTimeDisplay
        startTime={request?.fromDate}
        endTime={request?.toDate}
        showDateSeparately
        timeClassName="text-gray-600"
      />
    ),
  },
  {
    id: 'services',
    header: 'Services',
    accessor: 'services',
    width: 200,
    cellRenderer: (_, request) => (
      <div>
        {request?.services?.map((service) => (
          <Tag key={service.serviceId} style={{ marginBottom: 2 }}>
            {service.name}
          </Tag>
        )) || '-'}
      </div>
    ),
  },
  {
    id: 'status',
    header: 'Status',
    accessor: 'status',
    width: 120,
    align: 'center',
    cellRenderer: (status) => (
      <Tag color={getStatusColor(status as string)}>
        {((status as string) || 'PENDING').replace('_', ' ').toUpperCase()}
      </Tag>
    ),
  },
  {
    id: 'createdAt',
    header: 'Created',
    accessor: 'createdAt',
    width: 120,
    cellRenderer: (date) => (
      <DateTimeDisplay startTime={date as string} showDateSeparately={false} dateFormat="MMM DD, YYYY" />
    ),
  },
];
