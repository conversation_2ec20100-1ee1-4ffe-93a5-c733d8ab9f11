import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Service } from '@api/READ_ONLY/services_api/Api';
import { Button } from 'antd';
import { ServicessSearch } from './ServicesSearch';

type Props = {
  value: (Service | undefined)[]; // allow editable slots
  onChange: (next: (Service | undefined)[]) => void;
};

const Therapies = ({ value, onChange }: Props) => {
  const handleAddService = () => onChange([...(value ?? []), undefined]);

  const handleSetValue = (index: number, newService: Service) => {
    const next = [...value];
    next[index] = newService;
    onChange(next);
  };

  const handleDelete = (index: number) => {
    const next = [...value];
    next.splice(index, 1);
    onChange(next);
  };

  return (
    <div className="w-full">
      <div className="mb-1 text-gray-400">Services</div>

      {value?.map((service, idx) => (
        <div key={service?.serviceId} className="flex items-center gap-2 mb-2">
          <ServicessSearch
            disabled={false}
            value={service}
            selectedValues={value.filter((s): s is Service => s !== undefined)}
            setValue={(d) => handleSetValue(idx, d as Service)}
          />
          <Button type="text" icon={<DeleteOutlined />} danger onClick={() => handleDelete(idx)} />
        </div>
      ))}

      <Button type="dashed" block icon={<PlusOutlined />} onClick={handleAddService}>
        Add Service
      </Button>
    </div>
  );
};

export default Therapies;
