// TODO : DELETE THIS
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Switch } from 'antd';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import DateDayPicker from './components/DateDayPicker';
import RecurringEvent from './components/RecurringEvent';
import SuggestedCaregivers from './components/SuggestedCaregivers';
import Therapies from './components/Therapies';

interface DefaultFormValues {
  // patient: string;
  dateRange: string | null;
  // repeatDays: any;
  // Instead of reccuring use "is" or "has" in front just to always indicate that is a booleans
  isRecurring: boolean;
  therapies: any[];
  caregivers: any[];
  visits: any[];
}

interface ServiceRequestFormProps {
  // Might not needed will see
  // patientId: string;
  isDisabled: boolean;
  onSubmit: (data: FieldValues) => void;
  formId: string;
}

// This should be better to be passed as props into the form
const DEFAULT_FORM_VALUES: DefaultFormValues = {
  dateRange: null,
  // repeatDays: [],

  isRecurring: false,
  therapies: [],
  caregivers: [],
  visits: [],
};

// As a form a want to be completely dummy - Meaning I want to be able to be used even as add or edit with the same design.
// My form id should be passed as props - not be hardcoded
// What i am responsible for ? To render the fields and give back the selected data when the user clicks Create.
export default function ServiceRequestForm({ isDisabled, onSubmit, formId }: ServiceRequestFormProps) {
  // const { openNotification } = useNotifications();

  const methods = useForm({ defaultValues: DEFAULT_FORM_VALUES });

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
  } = methods;

  // The form should only be responsible for providing the selected info from the user. Not make api calls, clear or transform data and inform the user with notifications.
  // Also needed to revalidate or somehow update the list that shows all the service requests.

  // const onSubmit2 = async (data: FieldValues) => {
  //   console.log('data', data.dateRange.start.toString());

  //   // you should not be able to submit the form if it has no patient id. Catch the error earlier and show a message.
  //   // Fail fast if no caregiver id. - Meaning do not give myself a way out so i do not have to check for it. with ?? or optional chaining

  //   // const payload = {
  //   //   client: data.patient?.clientId ?? 0, // You might want to confirm the correct patient ID key
  //   //   preferredCaregiver: data.caregivers?.[0] ?? 0,
  //   //   serviceIds: (data.therapies ?? []).map((t: Service) => t.serviceId),
  //   //   notes: data.notes ?? '',
  //   //   fromDate: data.dateRange.start.format(),
  //   //   toDate: data.dateRange.end.format(),
  //   // };

  //   // try {
  //   //   await ApiClient.serviceRequestsApi.serviceRequests.createRequestServiceRequestsPost(payload);

  //   //   openNotification('topRight', {
  //   //     title: `Service Request`,
  //   //     description: 'Service Request created successfully. ',
  //   //     type: 'Success',
  //   //   });
  //   // } catch (err) {
  //   //   // How we handle the the specific errors?

  //   //   console.log(err);

  //   //   openNotification('topRight', {
  //   //     title: `Service Request`,
  //   //     description: 'Service Request failed.',
  //   //     type: 'Warning',
  //   //   });
  //   // }
  // };

  const recurring = getValues('isRecurring');

  return (
    // Adding the Fragment just for now so i will be able to return the jsx
    <>
      {/* <FormProvider {...methods}> */}
      <form id={formId} onSubmit={handleSubmit(onSubmit)} className="flex flex-col flex-1">
        {/* Row 2: Date range and recurrence - This is not just a row  but 2 :p It holds 1 row with the Date and another with start and end time */}
        <div className="flex md:flex-row flex-col items-start mb-6">
          <div className="w-full flex gap-2">
            <div className="flex flex-col w-full gap-4">
              <div className="w-full">
                <Controller
                  disabled={isDisabled}
                  name="dateRange"
                  rules={{ required: 'Date range is required' }}
                  control={control}
                  render={({ field }) => (
                    <DateDayPicker onChange={field.onChange} />

                    // <RangePicker
                    //   className="w-full"
                    //   showTime
                    //   format="YYYY-MM-DD"
                    //   size="large"
                    //   value={field.value}
                    //   onChange={field.onChange}
                    //   placeholder={['From', 'To']}
                    // />
                  )}
                />
              </div>

              <div className="flex justify-start gap-2">
                <Controller
                  name="isRecurring"
                  control={control}
                  render={({ field }) => <Switch checked={field.value} onChange={field.onChange} />}
                />

                <span>Set Recurring Visits</span>
              </div>
            </div>

            {errors.dateRange && <p className="text-app-danger text-xs mt-1">{errors.dateRange.message}</p>}

            {/* this will never get displayed for now cause i hardcoded the value to false and there is no switch, its commented */}
            {recurring && <RecurringEvent />}
          </div>
        </div>

        {/* Row 3: Services */}
        <div className="flex md:flex-row flex-col items-start mb-6">
          <div className="w-full">
            <Controller
              name="therapies"
              rules={{ required: 'At least one therapy is required' }}
              control={control}
              render={({ field }) => <Therapies value={field.value} onChange={field.onChange} />}
            />

            {errors.therapies && <p className="text-app-danger text-xs mt-1">{errors.therapies.message}</p>}
          </div>
        </div>

        {/* Row 4: Caregivers */}
        <div className="flex md:flex-row flex-col items-start mb-6">
          <div className="">
            <div className="mb-1 ">Suggested Caregivers</div>
            <Controller
              name="caregivers"
              control={control}
              render={({ field }) => <SuggestedCaregivers selected={field.value} setSelected={field.onChange} />}
            />
          </div>
        </div>

        {/* I will comment out the generated visits for now cause the whole component seems that needs to be reworked. */}
        {/* Its the only component that uses the form context. So for now i will remove the Form provider and see how it goes.
        If i need it i will simply pass the data as props not use the context. for 1 step drilling */}

        {/* Row 5: Recurring visits */}
        {/* <div className="flex md:flex-row flex-col items-start mb-6">
          <div className="">
            <Controller name="visits" control={control} render={() => <GeneratedVisits />} />
          </div>
        </div> */}
      </form>
      {/* </FormProvider> */}
    </>
  );
}
