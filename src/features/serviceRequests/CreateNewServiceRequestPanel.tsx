import { Service } from '@api/READ_ONLY/caregiver_api/Api';
import { Client } from '@api/READ_ONLY/client_api/Api';
import AlertDialog from '@app/components/ui/AlertDialog/AlertDialog';
import FormElement from '@app/components/ui/FormElement';
import { formatMinutesToHours } from '@app/utils/dates/formatMinutesToHours';
import ClientSelectGeneric from '@feat-clients/components/ClientSelect';
import ServicesSelectGeneric from '@feat-services/components/ServicesSelect/ServicesSelectGeneric';
import { Button, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useMemo, useRef, useState } from 'react';
import { AiOutlineMore } from 'react-icons/ai';
import { GiAlarmClock } from 'react-icons/gi';
import SimpleBar from 'simplebar-react';
import AvailableVisits from './components/AvailableVisits/AvailableVisits';
import DateDayPicker from './components/DateDayPicker/DateDayPicker';
import { REPETITION_EVENTS } from './const';
import { useCreateServiceRequest } from './hooks/useServiceRequestActions';
import useServiceRequestDetails from './hooks/useServiceRequestInfo';
import useSuggestedVisits from './hooks/useSuggestedVisits';
import buildRRule from './rruleGenerate';
// Duplication of the type and also under ServicesSelectionPanel.tsx
type ServicesSelectionPanelHandle = {
  reset: () => void;
};

const CreateNewServiceRequestPanel = () => {
  const panelRef = useRef<ServicesSelectionPanelHandle>(null);
  const [isOpen, setIsOpen] = useState(false);

  const [errors, setErrors] = useState<{
    patientIdRequired: string;
    servicesRequired: string;
  }>({
    patientIdRequired: '',
    servicesRequired: '',
  });

  const { isValidToCreateANewAAServiceRequest, dateTime, notes, patient, serviceRequestExtraDetails } =
    useServiceRequestDetails();

  // I am pretty sure. All those use memos are actually useLess cause from my hook useServiceRequestDetails a new reference object gets created each time a setter is called. its not a primitive value, so everything will reconcile and rerender. Investigate later

  const totalEstimatedTimeOfServices = useMemo(
    () => serviceRequestExtraDetails.value.services.reduce((total, s) => total + s.estimatedTimeMinutes, 0),
    [serviceRequestExtraDetails.value.services]
  );

  const rrule = useMemo(() => {
    if (!dateTime.values.endingDate) return null;

    return buildRRule({
      zoneId: 'Europe/Athens',
      ...dateTime.values,
    });
  }, [dateTime.values]);

  const generatedDaysToGetSuggestedVisits = useMemo(() => {
    if (!dateTime.values.endingDate) {
      return {
        yearlyMonthlyAndDailyFormat: [dateTime.values.startingDate.format('YYYY-MM-DD')],
        fullDayJsDate: [dateTime.values.startingDate],
      };
    }

    const availableDays = rrule?.all() || [];

    const yearlyMonthlyAndDailyFormat = availableDays.map((d) => dayjs(d).format('YYYY-MM-DD'));
    const fullDayJsDate = availableDays.map((d) => dayjs(d));

    return {
      yearlyMonthlyAndDailyFormat,
      fullDayJsDate,
    };
  }, [dateTime.values.endingDate, dateTime.values.startingDate, rrule]);

  const { availableVisits, areVisitsFetching, isValidToFetchAvailableVisits } = useSuggestedVisits({
    visitDates: generatedDaysToGetSuggestedVisits.yearlyMonthlyAndDailyFormat,
    dateTime: {
      fromTime: dateTime.values.fromTime,
      toTime: dateTime.values.toTime,
    },
    caregiverIds: [],
    serviceIds: serviceRequestExtraDetails.value.services.map((service) => service.id),
  });

  const resetAllValuesToDefault = () => {
    patient.resetToDefault();
    notes.resetToDefault();
    dateTime.resetToDefault();
    setIsOpen(false);
    // Clearing the internal state of the services panel
    panelRef.current?.reset();
  };

  // TODO refactor this it does to much accepts too many props  - no clear separation of concerns
  const { actions, isActionInProgress } = useCreateServiceRequest({
    notes: notes.value,
    patient: patient.value,
    resetAllValuesToDefault,
    dateTimeRecurrenceValues: dateTime.values,
    serviceIds: serviceRequestExtraDetails.value.services.map((service) => service.id),
    rruleString: rrule?.toString() || '',
    visitDates: generatedDaysToGetSuggestedVisits.fullDayJsDate,
  });

  return (
    <div className="w-full max-w-[1440px] mx-auto flex h-full">
      {/* Left panel - Service Request Form */}
      <div className="col-span-6 flex flex-col max-w-[700px] w-full min-w-[400px] pr-1.5 justify-between border-r border-gray-200">
        <SimpleBar forceVisible="y" autoHide style={{ height: 'calc(100% - 32px)' }} className="pr-4">
          <div className="flex flex-col mb-8">
            <div
              className={`flex items-center justify-center gap-4 mb-2 ${isActionInProgress ? 'opacity-50 pointer-events-none' : ''}`}
              style={{ position: 'relative' }}
            >
              <FormElement
                label="Patient"
                required
                error={errors.patientIdRequired}
                // className="mr-14"
              >
                <ClientSelectGeneric
                  size="large"
                  value={patient.value?.clientId}
                  onClientChange={(selectedPatient) => {
                    patient.updateValue(selectedPatient as Client);
                    setErrors((prev) => ({ ...prev, patientIdRequired: '' }));
                  }}
                />
              </FormElement>

              {/* <button
                type="button"
                aria-label="Patient history"
                title="Patient history"
                className="inline-flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-sm
             transition-all duration-200 hover:bg-gray-50 hover:shadow-md active:scale-95"
                style={{
                  position: 'absolute',
                  top: 24,
                  right: 0,
                }}
              >
                <MdWorkHistory className="h-5 w-5" color="var(--color-app-primary)" />
              </button> */}
            </div>

            <DateDayPicker
              dateTimeRecurrenceValues={dateTime.values}
              isDisabled={isActionInProgress}
              onSubmitRecurringEvents={(dateTimeRecurrenceValues) => {
                dateTime.updateDateTimeOnlyRecurrenceValues(dateTimeRecurrenceValues);

                if (dateTimeRecurrenceValues.frequency === 'weekly') {
                  dateTime.updateDateTimeRecurrenceValuesByKey('repeat', REPETITION_EVENTS.WEEKLY);
                }

                if (dateTimeRecurrenceValues.frequency === 'daily') {
                  dateTime.updateDateTimeRecurrenceValuesByKey('repeat', REPETITION_EVENTS.DAILY);
                }
              }}
              updateDateTimeRecurrenceValuesByKey={(key, value) => {
                dateTime.updateDateTimeRecurrenceValuesByKey(key, value);
              }}
            />

            <FormElement label="Notes" className="mt-4">
              <textarea
                className={`w-full h-24 border border-gray-300 rounded p-2 resize-none ${isActionInProgress ? 'opacity-50 pointer-events-none' : ''}`}
                onBlur={(e) => notes.updateValue(e.target.value)}
              />
            </FormElement>

            <div className="flex-1 min-w-[300px]">
              {/* Was like that */}

              {/* <ServicesSelectionPanel
              ref={panelRef}
              value={serviceRequestExtraDetails.value.services}
              onChange={(servicesIds) => {
                serviceRequestExtraDetails.updateValueByKey('services', servicesIds);
                setErrors((prev) => ({ ...prev, servicesRequired: '' }));
              }}
              totalEstimatedTimeOfServicesInMinutes={totalEstimatedTimeOfServices}
              error={errors.servicesRequired}
            /> */}

              {/* Why we allow to make on change all those calls to get the suggested visits ?? - Its logical that its happening cause it depends on the services ids but let the user select the services and the make a final call ? Or even a debounce ? */}
              <ServicesSelectGeneric
                size="large"
                allowClear={true}
                value={serviceRequestExtraDetails.value.services.map((s) => s.id) as number[]}
                mode="multiple"
                placeholder="Select services..."
                maxTagCount="responsive"
                maxTagPlaceholder={(omittedValues) => (
                  <Tooltip
                    styles={{ root: { pointerEvents: 'none' } }}
                    title={omittedValues.map(({ label }) => label).join(', ')}
                  >
                    <div className="flex items-center justify-center h-full">
                      <div className="text-[10px]">+{omittedValues.length}</div>
                      <div>
                        <AiOutlineMore />
                      </div>
                    </div>
                  </Tooltip>
                )}
                onServiceChange={(services) => {
                  console.log('selected services', services);
                  // We should not cast with typescript - if we are enforced to cast something else is wrong. Investigate a bit further
                  if ((services as Service[]).length === 0) {
                    serviceRequestExtraDetails.updateValueByKey('services', []);
                    return;
                  }
                  serviceRequestExtraDetails.updateValueByKey('services', [
                    // same casting
                    ...(services as Service[]).map((s) => ({
                      id: s.serviceId,
                      estimatedTimeMinutes: s.estimatedTimeMinutes as number,
                    })),
                  ]);
                  setErrors((prev) => ({ ...prev, servicesRequired: '' }));
                }}
              />

              <div className="w-full flex items-center gap-2 justify-between">
                <div className="ml-1 text-red-500 text-xs">{errors.servicesRequired || '\u00A0'}</div>

                <div className="flex items-center justify-end gap-2 my-2">
                  <GiAlarmClock className="text-blue-600 text-xl" />
                  <span className="text-gray-900 ">Total services time:</span>
                  <span className="text-blue-400">{formatMinutesToHours(totalEstimatedTimeOfServices)}</span>
                </div>
              </div>
            </div>
          </div>
        </SimpleBar>

        <Button
          type="primary"
          className="min-h-[42px]"
          onClick={() => {
            // First check for errors - if ok continue
            if (!isValidToCreateANewAAServiceRequest) {
              setErrors({
                patientIdRequired: !patient.value ? 'Patient is required' : '',
                servicesRequired:
                  serviceRequestExtraDetails.value.services.length === 0 ? 'At least one service is required' : '',
              });
              return;
            }

            const hasAtLeastOnUnavailableVisit = availableVisits.some((visit) => visit.caregivers.length === 0);

            if (hasAtLeastOnUnavailableVisit) {
              setIsOpen(true);
              return;
            }

            // If everything ok it will create the service request and whatever it follows
            actions.handleOnCreateButtonClick();
          }}
          loading={isActionInProgress}
          disabled={isActionInProgress}
        >
          Create
        </Button>
      </div>

      {/* Right panel - Available Visits */}
      <AvailableVisits
        availableVisits={availableVisits}
        areDisabled={isActionInProgress}
        isFetching={areVisitsFetching}
        isValidToFetch={isValidToFetchAvailableVisits}
      />

      <AlertDialog
        isOpen={isOpen}
        title="Are you sure you want to create this service request?"
        description="Not all visits have a caregiver assigned. Do you still wish to continue?"
        cancelText="Cancel"
        confirmationText="Proceed"
        isLoading={isActionInProgress}
        isDisabled={isActionInProgress}
        onCancel={() => setIsOpen(false)}
        onConfirm={() => {
          actions.handleOnCreateButtonClick();
        }}
      />
    </div>
  );
};

export default CreateNewServiceRequestPanel;
