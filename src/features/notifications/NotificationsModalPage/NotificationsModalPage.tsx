import { Modal } from 'antd';

type NotificationsModalPageProps = {
  isOpen: boolean;
  onClose: () => void;
};

const NotificationsModalPage = ({ isOpen, onClose }: NotificationsModalPageProps) => {
  // const navigate = useNavigate();

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={900}
      maskClosable
      destroyOnHidden
      title="All notifications"
    >
      {/* Replace with your real content */}
      <p className="text-slate-600">Full list goes here…</p>
    </Modal>
  );
};

export default NotificationsModalPage;
