import { ApiClient } from '@api/api-configuration';
import QUERY_KEYS from '@app/common/queryKeys';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

export const useMinifiedNotifications = () => {
  const queryClient = useQueryClient();
  const userId = 13;

  const { data, isFetching, isLoading } = useQuery({
    queryFn: () => ApiClient.notificationsApi.notifications.listNotificationsMinifiedNotificationsMinifiedGet(),
    queryKey: [QUERY_KEYS.NOTIFICATIONS_LIST_MINIFIED, userId],
    // enabled: isValidToFetchAvailableVisits,
  });

  const invalidateMinifiedNotifications = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.NOTIFICATIONS_LIST_MINIFIED, userId],
    });
  }, [queryClient, userId]);

  return {
    notifications: data?.data || [],
    areNotificationsFetching: isFetching,
    areNotificationsLoading: isLoading,
    invalidateMinifiedNotifications,
  };
};

export default useMinifiedNotifications;
