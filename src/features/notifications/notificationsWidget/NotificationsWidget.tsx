/* eslint-disable @typescript-eslint/no-unused-vars */
import { Button } from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>heck, FiX } from 'react-icons/fi';
import { useLocation, useNavigate } from 'react-router-dom';
import SimpleBar from 'simplebar-react';

const NOTIFICATIONS_PANEL_ANIMATIONS = {
  initial: { opacity: 0, y: -8, scale: 0.98 },
  animate: { opacity: 1, y: 0, scale: 1 },
  exit: { opacity: 0, y: -8, scale: 0.98 },
  transition: { type: 'spring', stiffness: 320, damping: 26, duration: 0.18 },
} as const;

const BACKDROP_ANIMATIONS = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: { type: 'spring', stiffness: 320, damping: 26, duration: 0.18 },
} as const;

type NotificationItemProps = {
  notificationId: number;
  createdAt: string;
  title: string;
  message: string;
  isRead: boolean;
  actionValue?: 'accept' | 'reject' | null;
  caregiverFirstName?: string;
  caregiverLastName?: string;
  onClick?: (id: number) => void;
  onAction?: (id: number) => void;
  onAccept?: (id: number) => void;
  onReject?: (id: number) => void;
};

const initials = (f?: string, l?: string) => [f?.[0], l?.[0]].filter(Boolean).join('').toUpperCase() || '•';

const timeAgo = (iso: string) => {
  const diff = Date.now() - new Date(iso).getTime();
  const mins = Math.max(1, Math.round(diff / 60000));
  if (mins < 60) return `${mins}m`;
  const hrs = Math.round(mins / 60);
  if (hrs < 24) return `${hrs}h`;
  const days = Math.round(hrs / 24);
  return `${days}d`;
};

function NotificationListItem({
  notificationId,
  createdAt,
  title,
  message,
  isRead,
  caregiverFirstName,
  caregiverLastName,
  actionValue,
  onClick,
  onAction,
  onAccept,
  onReject,
}: NotificationItemProps) {
  return (
    <div className="group flex flex-col px-4 py-3 hover:bg-slate-50 cursor-pointer">
      {/* Top row */}
      <button onClick={() => onClick?.(notificationId)} className="flex w-full items-start gap-3 text-left">
        {/* Avatar */}
        <div className="mt-0.5 flex h-10 w-10 items-center justify-center rounded-full bg-slate-200 text-sm font-semibold text-slate-700">
          {initials(caregiverFirstName, caregiverLastName)}
        </div>

        {/* Text */}
        <div className="min-w-0 flex-1">
          <div className="flex items-baseline justify-between">
            <div className="truncate pr-2 text-[15px] font-semibold text-slate-900">{title}</div>
            <div className="ml-2 shrink-0 text-xs text-slate-400">{timeAgo(createdAt)}</div>
          </div>

          <div className="mt-0.5 line-clamp-2 text-sm text-slate-600">{message}</div>
        </div>

        {/* Unread dot */}
        {!isRead && <span className="mt-1 h-2.5 w-2.5 shrink-0 rounded-full bg-blue-500" />}
      </button>

      {/* Actions */}
      {actionValue && (
        <div className="mt-3 pl-14 flex gap-2">
          {/* Accept */}
          <button
            onClick={() => onAccept?.(notificationId)}
            className="
        inline-flex items-center gap-2 rounded-md border px-3 py-1.5 text-sm font-medium
        border-blue-300 text-blue-500
        hover:bg-blue-50 hover:border-blue-200
        active:bg-blue-100
        shadow-[0_1px_0_rgba(0,0,0,0.02)]
        transition-colors
        focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-400/60
      "
          >
            <FiCheck className="h-4 w-4" />
            <span>Accept</span>
          </button>

          {/* Reject */}
          <button
            onClick={() => onReject?.(notificationId)}
            className="
        inline-flex items-center gap-2 rounded-md border px-3 py-1.5 text-sm font-medium
        border-rose-200 text-rose-400
        hover:bg-rose-50 hover:border-rose-300
        active:bg-rose-100
        shadow-[0_1px_0_rgba(0,0,0,0.02)]
        transition-colors
        focus:outline-none focus-visible:ring-2 focus-visible:ring-rose-400/60
      "
          >
            <FiX className="h-4 w-4" />
            <span>Reject</span>
          </button>
        </div>
      )}
    </div>
  );
}

const data = [
  {
    notificationId: 431,
    createdAt: '2025-09-15T08:03:42.551378Z',
    userId: 13,
    title: 'New Visit Assigned',
    message: 'You have been assigned to a new visit',
    isRead: true,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 93,
  },
  {
    notificationId: 423,
    createdAt: '2025-09-14T16:33:32.123384Z',
    userId: 13,
    title: 'New Visit Assigned',
    message: 'You have been assigned to a new visit',
    isRead: true,
    actionValue: 'accept',
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 430,
    createdAt: '2025-09-14T18:43:16.792471Z',
    userId: 14,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: false,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 429,
    createdAt: '2025-09-14T18:43:16.788664Z',
    userId: 1,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: true,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 428,
    createdAt: '2025-09-14T18:43:16.788544Z',
    userId: 4,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: true,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 427,
    createdAt: '2025-09-14T16:46:11.271522Z',
    userId: 13,
    title: 'New Visit Assigned',
    message: 'You have been assigned to a new visit',
    isRead: true,
    actionValue: 'accept',
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 426,
    createdAt: '2025-09-14T16:34:04.329731Z',
    userId: 4,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: true,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 425,
    createdAt: '2025-09-14T16:34:04.326651Z',
    userId: 14,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: false,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
  {
    notificationId: 424,
    createdAt: '2025-09-14T16:34:04.326158Z',
    userId: 1,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: true,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },

  {
    notificationId: 422,
    createdAt: '2025-09-14T16:30:57.596869Z',
    userId: 14,
    title: 'Tasoscaregiver Kakouris accepted',
    message: 'Γρηγορίου λαμπράκη 2',
    isRead: false,
    actionValue: null,
    caregiverFirstName: 'Tasoscaregiver',
    caregiverLastName: 'Kakouris',
    visitId: 92,
  },
];

const NotificationsWidget = () => {
  const [open, setOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const openAllNotificationsModal = () => {
    // Strip duplicate /notifications and preserve query params
    const basePath = location.pathname.replace(/\/notifications$/, '');
    const fullPath = `${basePath}/notifications${location.search}`;

    // Navigate first so the modal starts mounting its mask
    // navigate(fullPath, { state: { backgroundLocation: location } });

    setOpen(false);

    setTimeout(() => {
      navigate(fullPath, { state: { backgroundLocation: location } });
    }, 180);

    window.setTimeout(() => setOpen(false), 40); // ~AntD modal enter duration
  };

  const onAcceptVisitRequest = (id: number) => {
    console.log('accept', id);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setOpen((v) => !v)}
        aria-haspopup="dialog"
        aria-expanded={open}
        title="Notifications"
        className="inline-flex h-10 w-10 items-center justify-center rounded-full border border-slate-200 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-slate-400"
      >
        <FiBell className="h-5 w-5" />
      </button>

      <AnimatePresence>
        {open && (
          <>
            <motion.div
              key="backdrop"
              className="fixed inset-0 z-[999] bg-black/20 backdrop-blur-[0.4px]"
              initial={BACKDROP_ANIMATIONS.initial}
              animate={BACKDROP_ANIMATIONS.animate}
              exit={BACKDROP_ANIMATIONS.exit}
              transition={BACKDROP_ANIMATIONS.transition}
              onClick={() => setOpen(false)}
            />

            <motion.div
              key="notifications-panel"
              initial={NOTIFICATIONS_PANEL_ANIMATIONS.initial}
              animate={NOTIFICATIONS_PANEL_ANIMATIONS.animate}
              exit={NOTIFICATIONS_PANEL_ANIMATIONS.exit}
              transition={NOTIFICATIONS_PANEL_ANIMATIONS.transition}
              className="fixed right-4 top-14 z-[999] flex h-[600px] w-[400px] flex-col overflow-hidden rounded-xl border border-slate-200 bg-white shadow-2xl ring-1 ring-black/5"
            >
              <div className="border-b border-slate-100 px-4 py-3">
                <strong className="text-slate-900">Notifications</strong>
              </div>

              <div className="flex flex-1 items-center justify-center text-sm text-slate-500">
                <SimpleBar forceVisible="y" autoHide style={{ height: 476, width: '100%' }}>
                  <div className="divide-y divide-slate-100">
                    {data.map((n) => (
                      <NotificationListItem
                        key={n.notificationId}
                        notificationId={n.notificationId}
                        createdAt={n.createdAt}
                        title={n.title}
                        message={n.message}
                        isRead={n.isRead}
                        caregiverFirstName={n.caregiverFirstName}
                        caregiverLastName={n.caregiverLastName}
                        actionValue={n.actionValue as 'accept' | 'reject' | null}
                        onClick={(id) => console.log('open visit', id)}
                        onAccept={(id) => console.log('accept', id)}
                        onReject={(id) => console.log('reject', id)}
                      />
                    ))}
                  </div>
                </SimpleBar>
              </div>

              <div className="border-t border-slate-100 px-6 py-4">
                {/* <button
                  onClick={openAllNotificationsModal}
                  className="w-full cursor-pointer px-4 py-3 text-center font-medium text-slate-900 hover:bg-slate-50"
                >
                  All notifications
                </button> */}

                <Button
                  type="primary"
                  className="min-h-[42px] w-full"
                  onClick={openAllNotificationsModal}
                  // loading={isActionInProgress}
                  // disabled={isActionInProgress}
                >
                  All notifications
                </Button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationsWidget;
