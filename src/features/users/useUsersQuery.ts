import { ApiClient } from '@api/api-configuration';
import { RoleResponse, UserResponse } from '@api/READ_ONLY/users_api/Api';
import { useQuery } from '@tanstack/react-query';

// Query keys
export const usersQueryKeys = {
  all: ['users'] as const,
  lists: () => [...usersQueryKeys.all, 'list'] as const,
  list: (filters: string) => [...usersQueryKeys.lists(), { filters }] as const,
  details: () => [...usersQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...usersQueryKeys.details(), id] as const,
  roles: () => [...usersQueryKeys.all, 'roles'] as const,
};

// Hook to fetch all users
export const useUsersQuery = (params?: { limit?: number; offset?: number; query?: string }) => {
  return useQuery({
    queryKey: usersQueryKeys.list(JSON.stringify(params || {})),
    queryFn: async () => {
      const response = await ApiClient.usersApi.users.getUsersUsersGet({
        limit: params?.limit || 100,
        offset: params?.offset || 0,
        ...(params?.query ? { query: params.query } : {}),
      });
      return response.data;
    },
  });
};

// Hook to fetch a single user by ID
export const useUserQuery = (userId: number, enabled = true) => {
  return useQuery({
    queryKey: usersQueryKeys.detail(userId),
    queryFn: async () => {
      const response = await ApiClient.usersApi.users.getUserUsersUserIdGet(userId);
      return response.data;
    },
    enabled: enabled && !!userId,
  });
};

// Hook to fetch all available roles
export const useRolesQuery = () => {
  return useQuery({
    queryKey: usersQueryKeys.roles(),
    queryFn: async () => {
      const response = await ApiClient.usersApi.users.getRolesUsersRolesGet();
      return response.data;
    },
  });
};

// Types for convenience
export type UsersListResponse = {
  total: number;
  offset: number;
  limit: number;
  data: UserResponse[];
};

export type { RoleResponse, UserResponse };
