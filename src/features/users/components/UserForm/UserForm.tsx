import { RoleResponse, UserResponse } from '@api/READ_ONLY/users_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { FieldGroup } from '@app/types/form.types';
import { useEffect, useState } from 'react';
import { UserFormData } from '../../types';
import { userFormConfig as baseUserFormConfig } from './form.config';

type Props = {
  data?: UserResponse;
  roles?: RoleResponse[];
  onSubmit: (formData: UserFormData) => void;
};

export function UserForm({ data, roles = [], onSubmit }: Props) {
  const [userFormConfig, setUserFormConfig] = useState<FieldGroup[]>(baseUserFormConfig);

  // Update form config with roles options when roles change
  useEffect(() => {
    const updatedConfig = baseUserFormConfig.map((group) => ({
      ...group,
      fields: group.fields.map((field) => {
        if (field.name === 'roles') {
          return {
            ...field,
            options: roles.map((role: RoleResponse) => ({
              label: role.name,
              value: role.code,
            })),
          };
        }
        return field;
      }),
    }));
    setUserFormConfig(updatedConfig);
  }, [roles]);

  // Transform user data for form default values
  const defaultValues = data
    ? {
        email: data.email,
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        avatarUrl: data.avatarUrl || '',
        roles: data.roles.map((role) => role.code as 'adm' | 'cgv' | 'clt'),
      }
    : undefined;

  return <FormWrapper defaultValues={defaultValues} fields={userFormConfig} onSubmit={onSubmit} />;
}
