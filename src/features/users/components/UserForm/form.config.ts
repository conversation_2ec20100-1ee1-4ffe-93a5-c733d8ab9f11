import { FieldGroup } from '@app/types/form.types';

export const userFormConfig: FieldGroup[] = [
  {
    title: 'User Information',
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        width: 'half',
        rules: { required: 'First name is required' },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        width: 'half',
        rules: { required: 'Last name is required' },
      },
      {
        name: 'email',
        label: 'Email',
        type: 'email',
        width: 'half',
        rules: { required: 'Email is required' },
      },
      {
        name: 'avatarUrl',
        label: 'Avatar URL',
        type: 'text',
        width: 'half',
      },
      {
        name: 'roles',
        label: 'Roles',
        type: 'multiple-select',
        width: 'full',
        rules: { required: 'At least one role is required' },
        options: [], // Will be populated dynamically
      },
    ],
  },
];
