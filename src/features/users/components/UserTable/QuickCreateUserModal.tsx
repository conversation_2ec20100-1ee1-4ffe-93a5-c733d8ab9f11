import { ApiClient } from '@api/api-configuration';
import { RoleResponse, UserCreateReq, UserResponse } from '@api/READ_ONLY/users_api/Api';
import { Button, Form, Input, message, Modal, Select } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

type QuickCreateUserModalProps = {
  open: boolean;
  onCancel: () => void;
  onCreated: (user: UserResponse) => void;
  onNavigateToEdit?: (userId: number) => void; // Optional navigation callback
};

const QuickCreateUserModal: React.FC<QuickCreateUserModalProps> = ({ open, onCancel, onCreated, onNavigateToEdit }) => {
  const [form] = Form.useForm<UserCreateReq>();
  const [submitting, setSubmitting] = useState(false);
  const [roles, setRoles] = useState<RoleResponse[]>([]);

  // Fetch roles when modal opens
  useEffect(() => {
    if (open) {
      const fetchRoles = async () => {
        try {
          const response = await ApiClient.usersApi.users.getRolesUsersRolesGet();
          setRoles(response.data);
        } catch (error) {
          console.error('Failed to fetch roles:', error);
          message.error('Failed to load roles');
        }
      };
      fetchRoles();
    }
  }, [open]);

  const handleOk = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      const payload: UserCreateReq = {
        email: values.email.trim(),
        firstName: values.firstName?.trim() || null,
        lastName: values.lastName?.trim() || null,
        avatarUrl: values.avatarUrl?.trim() || null,
        roles: values.roles || [],
      };

      const res = await ApiClient.usersApi.users.createUserUsersPost(payload);
      message.success('User created');
      form.resetFields();
      onCreated(res.data);

      // If navigation callback is provided, navigate to edit page
      if (onNavigateToEdit) {
        onNavigateToEdit(res.data.userId);
      }
    } catch (err: unknown) {
      if (typeof err === 'object' && err !== null && 'errorFields' in err) {
        return;
      }
      console.error('Failed to create user', err);
      message.error('Failed to create user');
    } finally {
      setSubmitting(false);
    }
  }, [form, onCreated, onNavigateToEdit]);

  return (
    <Modal
      open={open}
      title="Create New User"
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={submitting}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={submitting} onClick={handleOk}>
          Save
        </Button>,
      ]}
      destroyOnHidden
      maskClosable={!submitting}
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label="Email"
          name="email"
          validateTrigger={['onBlur', 'onSubmit']}
          rules={[
            { required: true, message: 'Email is required' },
            { type: 'email', message: 'Please enter a valid email' },
          ]}
        >
          <Input placeholder="Enter email" autoComplete="off" size="large" />
        </Form.Item>

        <div className="grid grid-cols-2 gap-2">
          <Form.Item
            label="First Name"
            name="firstName"
            rules={[{ required: true, message: 'First name is required' }]}
          >
            <Input placeholder="Enter first name" autoComplete="off" size="large" />
          </Form.Item>
          <Form.Item label="Last Name" name="lastName" rules={[{ required: true, message: 'Last name is required' }]}>
            <Input placeholder="Enter last name" autoComplete="off" size="large" />
          </Form.Item>
        </div>
        <Form.Item label="Roles" name="roles" rules={[{ required: true, message: 'At least one role is required' }]}>
          <Select
            size="large"
            mode="multiple"
            placeholder="Select roles"
            options={roles.map((role) => ({
              label: role.name,
              value: role.code,
            }))}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuickCreateUserModal;
