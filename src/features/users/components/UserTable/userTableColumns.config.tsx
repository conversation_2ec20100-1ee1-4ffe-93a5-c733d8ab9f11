import { UserResponse } from '@api/READ_ONLY/users_api/Api';
import DateTimeDisplay from '@app/components/common/DateTimeDisplay';
import TableLink from '@app/components/common/TableLink';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';

// Extend UserResponse to work with TableRow requirements
export interface UserRow extends Omit<UserResponse, 'userId'>, TableRow {
  id: string | number; // Map userId to id
  userId: number; // Keep original field for compatibility
}

export const getUserTableColumns = (editNavigation: (userId: number) => void): BaseColumn<UserRow>[] => [
  {
    id: 'name',
    header: 'Full Name',
    accessor: (row) => `${row.firstName || ''} ${row.lastName || ''}`.trim(),
    width: 200,
    sortable: true,
    filterable: true,
    cellRenderer: (_, row) => (
      <TableLink onClick={() => editNavigation(row.userId)}>
        <ClientDisplay firstName={row.firstName || ''} lastName={row.lastName || ''} />
      </TableLink>
    ),
  },
  {
    id: 'email',
    header: 'Email',
    accessor: 'email',
    width: 250,
    sortable: true,
    filterable: true,
    type: 'email',
    cellRenderer: (value) => <>{String(value || '-')}</>,
  },
  {
    id: 'roles',
    header: 'Roles',
    accessor: (row) => row.roles.map((role) => role.name).join(', '),
    width: 200,
    sortable: false,
    filterable: true,
    cellRenderer: (_, row) => (
      <div className="flex flex-wrap gap-1">
        {row.roles.map((role) => (
          <span
            key={role.roleId}
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            {role.name}
          </span>
        ))}
      </div>
    ),
  },
  {
    id: 'createdAt',
    header: 'Created At',
    accessor: 'createdAt',
    width: 150,
    sortable: true,
    filterable: false,
    cellRenderer: (value) => (
      <>
        {value ? (
          <DateTimeDisplay
            startTime={new Date(value as string).toLocaleDateString()}
            showDateSeparately={false}
            dateFormat="MMM DD, YYYY"
            timeFormat=""
          />
        ) : (
          '-'
        )}
      </>
    ),
  },
  {
    id: 'updatedAt',
    header: 'Last Updated',
    accessor: 'updatedAt',
    width: 150,
    sortable: true,
    filterable: false,
    cellRenderer: (value) => (
      <>
        {value ? (
          <DateTimeDisplay
            startTime={new Date(value as string).toLocaleDateString()}
            showDateSeparately={false}
            dateFormat="MMM DD, YYYY"
            timeFormat=""
          />
        ) : (
          'Never'
        )}
      </>
    ),
  },
];
