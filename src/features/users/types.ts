import { RoleResponse, UserCreateReq, UserResponse } from '@api/READ_ONLY/users_api/Api';

export interface Role {
  name: string;
  code: string;
  description: string | null | undefined;
  roleId: number;
}

export interface User {
  email: string;
  firstName: string | null | undefined;
  lastName: string | null | undefined;
  avatarUrl: string | null | undefined;
  userId: number;
  createdAt: string;
  updatedAt: string | null;
  roles: Role[];
}

export type CreateUserPayload = UserCreateReq;

export interface UserFormData {
  email: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  roles: ('adm' | 'cgv' | 'clt')[];
}

// Helper type to work with API responses
export type ApiRole = RoleResponse;
export type ApiUser = UserResponse;
