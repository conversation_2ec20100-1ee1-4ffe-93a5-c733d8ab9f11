import { CaregiverShift } from '@api/READ_ONLY/caregiver_api/Api';

export type ShiftMap = {
  [caregiverId: number]: {
    [date: string]: CaregiverShift[];
  };
};

export type UpdateAction =
  | {
      type: 'create' | 'update';
      caregiverId: number;
      date: string;
      shifts: CaregiverShift[];
    }
  | {
      type: 'delete';
      caregiverId: number;
      date: string;
      shiftId: number;
    };

export type ModalMode = 'create' | 'edit';

export type DayShifts = {
  date: string;
  shifts: { from: string; to: string }[];
};

export type BatchExportPayload = {
  caregiverId: number;
  days: DayShifts[];
};

export type RecurringMode = 'day' | 'thisWeek' | 'weekly';
export type RecurringMeta = {
  mode: RecurringMode;
  selectedWeekdays?: number[]; // 0..6
  weeks?: number; // for weekly
};
