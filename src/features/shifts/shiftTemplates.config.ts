import dayjs from 'dayjs';

export type SlotTemplate = { from: string; to: string };

export type ShiftTemplatesConfig = {
  defaultByWeekday?: Partial<Record<number, SlotTemplate>>; // 0..6 (Sun..Sat)
  templatesByWeekday?: Partial<Record<number, SlotTemplate[]>>; // 0..6
  // Future: per-date overrides
};

export const defaultShiftTemplatesConfig: ShiftTemplatesConfig = {
  defaultByWeekday: {
    1: { from: '09:00', to: '17:00' }, // Monday
    2: { from: '09:00', to: '17:00' },
    3: { from: '09:00', to: '17:00' },
    4: { from: '09:00', to: '17:00' },
    5: { from: '09:00', to: '17:00' },
  },
  templatesByWeekday: {
    1: [
      { from: '09:00', to: '14:00' },
      { from: '15:00', to: '19:00' },
    ],
  },
};

export function makeGetDefaultSlotForDate(cfg: ShiftTemplatesConfig) {
  return (date: string): SlotTemplate | undefined => {
    const wd = dayjs(date).day();
    return cfg.defaultByWeekday?.[wd];
  };
}

export function makeGetTemplatesForDate(cfg: ShiftTemplatesConfig) {
  return (date: string): SlotTemplate[] => {
    const wd = dayjs(date).day();
    return cfg.templatesByWeekday?.[wd] ?? [];
  };
}
