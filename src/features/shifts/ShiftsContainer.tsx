import { ApiClient } from '@api/api-configuration';
import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import ShiftsBoard from '@feat-shifts/components/ShiftsBoard';
import dayjs from 'dayjs';

export const ShiftsContainer = () => {
  const onCreate = async (selectedCaregiver: number, selectedDate: string, shifts: ShiftCreate[]) => {
    return Promise.all(
      shifts.map(async (shift) => {
        const periodFrom = dayjs(`${selectedDate}T${shift.periodFrom}`).toISOString();
        const periodTo = dayjs(`${selectedDate}T${shift.periodTo}`).toISOString();

        const data = { notes: shift.notes, periodFrom, periodTo };

        const res = await ApiClient.caregiverApi.caregivers.addShiftCaregiversCaregiverIdShiftsPost(
          selectedCaregiver,
          data
        );
        return res;
      })
    );
  };

  const onEdit = async (selectedCaregiver: number, selectedDate: string, shifts: CaregiverShift[]) => {
    return Promise.all(
      shifts.map(async (shift) => {
        const periodFrom = dayjs(`${selectedDate}T${shift.periodFrom}`).toISOString();
        const periodTo = dayjs(`${selectedDate}T${shift.periodTo}`).toISOString();

        const data = { notes: shift.notes || null, periodFrom, periodTo };

        const res = await ApiClient.caregiverApi.caregivers.updateShiftCaregiversCaregiverIdShiftsShiftIdPut(
          selectedCaregiver,
          shift.caregiverShiftId,
          data
        );
        return res;
      })
    );
  };

  const onDelete = async (selectedCaregiver: number, shift_id: number) => {
    const res = await ApiClient.caregiverApi.caregivers.deleteShiftCaregiversCaregiverIdShiftsShiftIdDelete(
      selectedCaregiver,
      shift_id
    );
    return res;
  };
  return <ShiftsBoard onCreate={onCreate} onEdit={onEdit} onDelete={onDelete} />;
};
