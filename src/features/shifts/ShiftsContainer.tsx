import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';
import ShiftsBoard from '@feat-shifts/components/ShiftsBoard';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { useCaregivers } from './hooks/useCaregivers';
import { useWeek, useWeekShifts } from './hooks/useWeekShifts';
import { createShifts, deleteShift, fetchShiftsByDates, updateShifts } from './shifts.api';
import {
  defaultShiftTemplatesConfig,
  makeGetDefaultSlotForDate,
  makeGetTemplatesForDate,
} from './shiftTemplates.config';
import { BatchExportPayload, RecurringMeta, UpdateAction } from './types';
import { expandRecurringDates } from './utils/repeatEngine';

export const ShiftsContainer = () => {
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());
  const { caregivers: caregiversList } = useCaregivers();
  const { daysOfWeek, dateStrings } = useWeek(currentDate);
  const { shiftMap, setShiftMap, refetch } = useWeekShifts(dateStrings, { autoFetch: false });

  const refreshWeek = useCallback(async () => {
    console.log('Refreshing week shifts for', currentDate.format('YYYY-MM-DD'));
    const weekStart = currentDate.startOf('week');
    const days = Array.from({ length: 7 }, (_, i) => weekStart.add(i, 'day'));
    const dates = days.map((d) => d.format('YYYY-MM-DD'));
    try {
      const shiftData = await fetchShiftsByDates(dates);
      const newMap: typeof shiftMap = {} as typeof shiftMap;
      for (const shift of shiftData) {
        const caregiverId = shift.caregiverId!;
        const shiftDate = dayjs(shift.periodFrom).format('YYYY-MM-DD');
        if (!newMap[caregiverId]) newMap[caregiverId] = {};
        if (!newMap[caregiverId][shiftDate]) newMap[caregiverId][shiftDate] = [];
        const start = dayjs(shift.periodFrom).format('HH:mm');
        const end = dayjs(shift.periodTo).format('HH:mm');
        const enriched = { ...shift, periodFrom: start, periodTo: end } as CaregiverShift;
        newMap[caregiverId][shiftDate].push(enriched);
      }
      setShiftMap(newMap);
    } catch (e) {
      console.error('Refresh week failed', e);
    }
  }, [currentDate, setShiftMap]);

  const onCreate = useCallback(
    async (
      selectedCaregiver: number,
      selectedDate: string,
      shifts: ShiftCreate[],
      recurring?: RecurringMeta | null
    ) => {
      const extraDates = recurring ? expandRecurringDates(selectedDate, recurring) : [];
      const dateSet = new Set<string>([selectedDate, ...extraDates]);
      const dates = Array.from(dateSet);
      const days = dates.map((date) => ({
        date,
        shifts: shifts.map((s) => ({ from: s.periodFrom, to: s.periodTo })),
      }));
      const payload: BatchExportPayload = { caregiverId: selectedCaregiver, days };
      console.log('Repeat batch payload', payload, recurring);
      const results = await Promise.all(dates.map((date) => createShifts(selectedCaregiver, date, shifts)));
      return results.flat();
    },
    []
  );

  const onEdit = useCallback(
    async (
      selectedCaregiver: number,
      selectedDate: string,
      shifts: CaregiverShift[],
      recurring?: RecurringMeta | null
    ) => {
      // Edits typically require exact shift IDs per date; by default, apply only to the selected date
      console.log({ selectedCaregiver, selectedDate, shifts, recurring });
      return updateShifts(selectedCaregiver, selectedDate, shifts);
    },
    []
  );

  const onDelete = useCallback(async (selectedCaregiver: number, shift_id: number) => {
    return deleteShift(selectedCaregiver, shift_id);
  }, []);

  const updateShiftMap = (action: UpdateAction) => {
    setShiftMap((prev) => {
      const updated = { ...prev };

      switch (action.type) {
        case 'create':
        case 'update': {
          if (!updated[action.caregiverId]) updated[action.caregiverId] = {};
          updated[action.caregiverId][action.date] = action.shifts;
          break;
        }
        case 'delete': {
          if (!updated[action.caregiverId]) return prev;
          updated[action.caregiverId][action.date] = updated[action.caregiverId][action.date].filter(
            (shift) => shift.caregiverShiftId !== action.shiftId
          );
          break;
        }
      }

      return updated;
    });

    // After creates, refetch to get server-generated IDs for shifts
    if (action.type === 'create') {
      refetch();
    }
  };

  const getDefaultSlot = makeGetDefaultSlotForDate(defaultShiftTemplatesConfig);
  const getTemplates = makeGetTemplatesForDate(defaultShiftTemplatesConfig);

  // Intercept navigation to prefetch shifts for the target week, then navigate.
  const navigateAndFetch = useCallback(
    (newDate: Dayjs) => {
      (async () => {
        console.log('Navigating to', newDate.format('YYYY-MM-DD'));
        const weekStart = newDate.startOf('week');
        const days = Array.from({ length: 7 }, (_, i) => weekStart.add(i, 'day'));
        const dates = days.map((d) => d.format('YYYY-MM-DD'));
        try {
          const shiftData = await fetchShiftsByDates(dates);
          const newMap: typeof shiftMap = {} as typeof shiftMap;
          for (const shift of shiftData) {
            const caregiverId = shift.caregiverId!;
            const shiftDate = dayjs(shift.periodFrom).format('YYYY-MM-DD');
            if (!newMap[caregiverId]) newMap[caregiverId] = {};
            if (!newMap[caregiverId][shiftDate]) newMap[caregiverId][shiftDate] = [];
            const start = dayjs(shift.periodFrom).format('HH:mm');
            const end = dayjs(shift.periodTo).format('HH:mm');
            const enriched = { ...shift, periodFrom: start, periodTo: end } as CaregiverShift;
            newMap[caregiverId][shiftDate].push(enriched);
          }
          setShiftMap(newMap);
        } catch (e) {
          console.error('Prefetch shifts failed', e);
        } finally {
          setCurrentDate(newDate);
        }
      })();
    },
    [setShiftMap]
  );

  useEffect(() => {
    refreshWeek();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ShiftsBoard
      shifts={shiftMap}
      caregivers={caregiversList}
      daysOfWeek={daysOfWeek}
      dateNavigator={<DateNavigator mode="week" currentDate={currentDate} setCurrentDate={navigateAndFetch} />}
      getDefaultSlotForDate={(date) => getDefaultSlot(date) || { from: '09:00', to: '17:00' }}
      getTemplatesForDate={(date) => getTemplates(date)}
      onCreate={onCreate}
      onEdit={onEdit}
      onDelete={onDelete}
      onLocalUpdate={updateShiftMap}
      refreshWeek={refreshWeek}
    />
  );
};
