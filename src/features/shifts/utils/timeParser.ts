import { TimeSlot, pad2 } from './time';

// Accepts formats like: 4-10, 04-10, 4:00-10, 04:00-10:00, 400-1000, and multiple separated by , or ;
export function parseTimeInput(input: string): { slots: TimeSlot[]; errors: string[] } {
  const errors: string[] = [];
  const slots: TimeSlot[] = [];
  const raw = (input || '').trim();
  if (!raw) return { slots: [], errors: ['Empty input'] };

  const parts = raw
    .split(/[;,]/)
    .map((s) => s.trim())
    .filter(Boolean);
  for (const part of parts) {
    const range = part.split(/\s*-\s*/);
    if (range.length !== 2) {
      errors.push(`Malformed range: "${part}"`);
      continue;
    }
    const from = normalizeToken(range[0]);
    const to = normalizeToken(range[1]);
    if (!from || !to) {
      errors.push(`Invalid time in: "${part}"`);
      continue;
    }
    slots.push({ from, to });
  }

  return { slots, errors };
}

// Token normalization: 4 -> 04:00, 400 -> 04:00, 4:5 -> 04:05, 04:00 -> 04:00
function normalizeToken(t: string): string | null {
  const s = t.trim();
  // HH:MM
  const m1 = s.match(/^(\d{1,2}):(\d{1,2})$/);
  if (m1) {
    const h = Number(m1[1]);
    const m = Number(m1[2]);
    if (h > 23 || m > 59) return null;
    return `${pad2(h)}:${pad2(m)}`;
  }
  // H or HH -> H:00
  const m2 = s.match(/^(\d{1,2})$/);
  if (m2) {
    const h = Number(m2[1]);
    if (h > 23) return null;
    return `${pad2(h)}:00`;
  }
  // HMM or HHMM (e.g., 400, 0730, 1530)
  const m3 = s.match(/^(\d{3,4})$/);
  if (m3) {
    const num = m3[1];
    const h = Number(num.slice(0, num.length - 2));
    const m = Number(num.slice(-2));
    if (h > 23 || m > 59) return null;
    return `${pad2(h)}:${pad2(m)}`;
  }
  return null;
}
