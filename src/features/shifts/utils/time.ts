export type TimeSlot = { from: string; to: string };

export function pad2(n: number): string {
  return n.toString().padStart(2, '0');
}

export function minutesToHHMM(total: number): string {
  const m = ((total % (24 * 60)) + 24 * 60) % (24 * 60);
  const h = Math.floor(m / 60);
  const min = m % 60;
  return `${pad2(h)}:${pad2(min)}`;
}

export function hhmmToMinutes(hhmm: string): number | null {
  if (!/^\d{1,2}:\d{2}$/.test(hhmm)) return null;
  const [h, m] = hhmm.split(':').map(Number);
  if (h < 0 || h > 23 || m < 0 || m > 59) return null;
  return h * 60 + m;
}

export function addMinutes(hhmm: string, delta: number): string {
  const m = hhmmToMinutes(hhmm);
  if (m == null) return hhmm;
  return minutesToHHMM(m + delta);
}

export function normalizeSlot(slot: TimeSlot): TimeSlot {
  return { from: slot.from, to: slot.to };
}
