import { CaregiverShift } from '@api/READ_ONLY/caregiver_api/Api';
import { hhmmToMinutes } from './time';

export type ValidationResult = {
  errors: string[];
  warnings: string[];
};

export function validateCellSlots(
  existing: CaregiverShift[],
  newSlots: { from: string; to: string }[]
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Hard error: start >= end for same day
  for (const s of newSlots) {
    const a = hhmmToMinutes(s.from);
    const b = hhmmToMinutes(s.to);
    if (a == null || b == null) {
      errors.push(`Invalid time ${s.from}-${s.to}`);
      continue;
    }
    if (a >= b) errors.push(`Start must be before end (${s.from}-${s.to})`);
  }

  // Overlap detection with existing
  const existingWindows = existing
    .map((e) => ({ a: hhmmToMinutes(e.periodFrom as string) ?? -1, b: hhmmToMinutes(e.periodTo as string) ?? -1 }))
    .filter((w) => w.a >= 0 && w.b >= 0);

  for (const s of newSlots) {
    const a = hhmmToMinutes(s.from)!;
    const b = hhmmToMinutes(s.to)!;
    const overlaps = existingWindows.some((w) => a < w.b && b > w.a);
    if (overlaps) {
      errors.push(`Overlaps existing shift (${s.from}-${s.to})`);
    }
  }

  // Additional rules (soft) can be appended here and push to warnings

  return { errors, warnings };
}
