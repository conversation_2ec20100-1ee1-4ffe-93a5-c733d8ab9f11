import dayjs, { Dayjs } from 'dayjs';
import type { RecurringMeta } from '../types';

export type RepeatMode = 'day' | 'thisWeek' | 'weekly'; // Monthly can be added later

export type RepeatOptions = {
  mode: RepeatMode;
  startDate: string; // YYYY-MM-DD
  selectedWeekdays?: number[]; // 0-6 (Mon-Sun matching dayjs ISO? we will use dayjs().day())
  weeks?: number; // for weekly mode
};

export type TargetCell = { caregiverId: number; date: string };

export function computeTargets(caregiverId: number, options: RepeatOptions): TargetCell[] {
  const { mode, startDate } = options;
  const base = dayjs(startDate);
  const set = new Set<string>();

  const add = (d: Dayjs) => set.add(`${caregiverId}|${d.format('YYYY-MM-DD')}`);

  if (mode === 'day') {
    add(base);
  }

  if (mode === 'thisWeek') {
    // Week of the base date; include selected weekdays (default to base day)
    const weekStart = base.startOf('week');
    const days = options.selectedWeekdays?.length ? options.selectedWeekdays : [base.day()];
    for (let i = 0; i < 7; i++) {
      const d = weekStart.add(i, 'day');
      if (days.includes(d.day())) add(d);
    }
  }

  if (mode === 'weekly') {
    const weeks = Math.max(1, options.weeks ?? 4);
    const days = options.selectedWeekdays?.length ? options.selectedWeekdays : [base.day()];
    for (let w = 0; w < weeks; w++) {
      const weekStart = base.add(w, 'week').startOf('week');
      for (let i = 0; i < 7; i++) {
        const d = weekStart.add(i, 'day');
        if (days.includes(d.day())) add(d);
      }
    }
  }

  const out: TargetCell[] = [];
  for (const key of set) {
    const [cg, date] = key.split('|');
    out.push({ caregiverId: Number(cg), date });
  }
  return out;
}

export function expandRecurringDates(baseDate: string, recurring: RecurringMeta): string[] {
  const cg = 0; // dummy id; we only return dates
  const targets = computeTargets(cg, {
    mode: recurring.mode,
    startDate: baseDate,
    selectedWeekdays: recurring.selectedWeekdays,
    weeks: recurring.weeks,
  });
  const dates = Array.from(new Set(targets.map((t) => t.date)));
  return dates;
}
