import { create } from 'zustand';
import { DayShifts } from '../types';

export type RowWeekSnapshot = {
  caregiverId: number;
  weekStart: string; // YYYY-MM-DD
  days: DayShifts[];
};

export type WeekSnapshot = {
  weekStart: string; // YYYY-MM-DD
  caregivers: Array<{
    caregiverId: number;
    days: DayShifts[];
  }>;
};

type ClipboardState = {
  rowSnapshot: RowWeekSnapshot | null;
  weekSnapshot: WeekSnapshot | null;
  setRowSnapshot: (snap: RowWeekSnapshot | null) => void;
  setWeekSnapshot: (snap: WeekSnapshot | null) => void;
};

export const useClipboardStore = create<ClipboardState>((set) => ({
  rowSnapshot: null,
  weekSnapshot: null,
  setRowSnapshot: (rowSnapshot) => set({ rowSnapshot }),
  setWeekSnapshot: (weekSnapshot) => set({ weekSnapshot }),
}));
