import { ApiClient } from '@api/api-configuration';
import { CaregiverShift, CaregiverWithServices, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import qs from 'qs';

export async function fetchCaregivers(): Promise<CaregiverWithServices[]> {
  const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet();
  return response.data.data;
}

export async function fetchShiftsByDates(dates: string[]): Promise<CaregiverShift[]> {
  console.log('Fetching shifts for dates:', dates);
  const response = await ApiClient.caregiverApi.caregivers.getShiftsByDatesCaregiversShiftsByDatesGet(
    { dates },
    { paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }) }
  );
  return response.data.data;
}

export async function createShifts(caregiverId: number, date: string, shifts: ShiftCreate[]) {
  return Promise.all(
    shifts.map(async (shift) => {
      const periodFrom = new Date(`${date}T${shift.periodFrom}:00`).toISOString();
      const periodTo = new Date(`${date}T${shift.periodTo}:00`).toISOString();
      const data = { notes: shift.notes, periodFrom, periodTo };
      return ApiClient.caregiverApi.caregivers.addShiftCaregiversCaregiverIdShiftsPost(caregiverId, data);
    })
  );
}

export async function updateShifts(caregiverId: number, date: string, shifts: CaregiverShift[]) {
  return Promise.all(
    shifts.map(async (shift) => {
      const periodFrom = new Date(`${date}T${shift.periodFrom}:00`).toISOString();
      const periodTo = new Date(`${date}T${shift.periodTo}:00`).toISOString();
      const data = { notes: shift.notes || null, periodFrom, periodTo };
      return ApiClient.caregiverApi.caregivers.updateShiftCaregiversCaregiverIdShiftsShiftIdPut(
        caregiverId,
        shift.caregiverShiftId,
        data
      );
    })
  );
}

export async function deleteShift(caregiverId: number, shiftId: number) {
  return ApiClient.caregiverApi.caregivers.deleteShiftCaregiversCaregiverIdShiftsShiftIdDelete(caregiverId, shiftId);
}
