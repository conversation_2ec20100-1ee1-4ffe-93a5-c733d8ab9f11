import { memo } from 'react';
import { Dayjs } from 'dayjs';

const ShiftsTableHeader = ({ daysOfWeek }: { daysOfWeek: Dayjs[] }) => {
  return (
    <thead className="bg-app-gray-light sticky top-0 z-10">
      <tr className="text-center text-app-text-dark font-semibold">
        <th className="w-64 py-2 border-b border-r border-app-gray">Caregivers</th>
        {daysOfWeek.map((day) => (
          <th key={day.format('YYYY-MM-DD')} className="py-2 border-b border-app-gray border-r last:border-r-0">
            <span>{day.format('dddd')} </span>
            <span className="text-app-text-light">{day.format('DD MMM')}</span>
          </th>
        ))}
      </tr>
    </thead>
  );
};

export default memo(ShiftsTableHeader);
