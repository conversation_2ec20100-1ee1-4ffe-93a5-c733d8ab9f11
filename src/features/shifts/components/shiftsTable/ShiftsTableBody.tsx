import { Caregiver, CaregiverShift } from '@api/READ_ONLY/caregiver_api/Api';
import VisitAvatar from '@feat-scheduling/components/Avatar/VisitAvatar';
import dayjs, { Dayjs } from 'dayjs';
import { useState } from 'react';
import { BsPlus } from 'react-icons/bs';
import { MdMoreVert } from 'react-icons/md';
import { colorMap } from '../shiftsColourMap';

type ShiftMap = {
  [caregiverId: number]: {
    [date: string]: CaregiverShift[];
  };
};

type Props = {
  caregiversList: Caregiver[];
  daysOfWeek: dayjs.Dayjs[];
  shiftMap: ShiftMap;
  handleAdd: (id: number, dateKey: string) => void;
  handleEdit: (id: number, dateKey: string, shifts: CaregiverShift[]) => void;
};

const ShiftsTableBody = ({ caregiversList, daysOfWeek, shiftMap, handleAdd, handleEdit }: Props) => {
  const [menuAnchor, setMenuAnchor] = useState<{ employeeId: string; date: string } | null>(null);

  return (
    <tbody>
      {caregiversList.map((emp: Caregiver) => (
        <tr key={emp.caregiverId} className="border-b border-app-gray align-top">
          {/* Employee Name Cell */}
          <td className="align-middle w-36 py-2 px-2 bg-gray-50 font-medium border-r border-app-gray ">
            <div className="flex items-center gap-2 h-fit">
              <VisitAvatar fullname={emp.firstName} />
              <span>{emp.firstName}</span>
            </div>
          </td>

          {/* Shifts Per Day */}
          {daysOfWeek.map((day: Dayjs) => {
            const dateKey = day.format('YYYY-MM-DD');
            const shifts = shiftMap[emp.caregiverId]?.[dateKey] || [];
            return (
              <td key={dateKey} className="relative h-28 p-2 border-r border-app-gray last:border-r-0 align-top group">
                <button
                  className={`absolute right-4 top-2 text-gray-600 hover:text-gray-800 ml-auto cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity ${shifts.length === 0 && 'hidden'}`}
                  onClick={() => {
                    const isSameCell =
                      menuAnchor?.employeeId === String(emp.caregiverId) && menuAnchor?.date === dateKey;
                    setMenuAnchor(isSameCell ? null : { employeeId: String(emp.caregiverId), date: dateKey });
                  }}
                >
                  <MdMoreVert size={16} />
                </button>

                {/* Dropdown Menu */}
                {menuAnchor?.employeeId === String(emp.caregiverId) && menuAnchor?.date === dateKey && (
                  <div className="absolute right-2 top-8 bg-white shadow-md rounded-md py-1 z-10 text-sm">
                    <button
                      className="px-4 py-1 w-full text-left cursor-pointer hover:bg-gray-100"
                      onClick={() => {
                        setMenuAnchor(null);
                        handleEdit(emp.caregiverId, dateKey, shifts);
                      }}
                    >
                      Edit
                    </button>
                  </div>
                )}

                {/* Shift display */}
                <div className="mt-2 flex flex-col items-center gap-1 justify-start h-[5.5rem] overflow-auto px-4 pt-2">
                  {shifts.length === 0 ? (
                    <button
                      className="flex items-center justify-center w-full h-full rounded-2xl cursor-pointer text-lg text-app-text-dark bg-app-gray-light hover:bg-app-gray"
                      onClick={() => {
                        handleAdd(emp.caregiverId, dateKey);
                      }}
                    >
                      <BsPlus />
                    </button>
                  ) : (
                    shifts.map((shift, index) => {
                      const colors = colorMap[index % colorMap.length];
                      return (
                        <div
                          key={shift.caregiverShiftId}
                          className={`w-fit border-2 rounded-4xl py-1 px-3 text-xs text-gray-800 ${colors.bg} ${colors.border}`}
                        >
                          {shift.periodFrom} - {shift.periodTo}
                        </div>
                      );
                    })
                  )}
                </div>
              </td>
            );
          })}
        </tr>
      ))}
    </tbody>
  );
};

export default ShiftsTableBody;
