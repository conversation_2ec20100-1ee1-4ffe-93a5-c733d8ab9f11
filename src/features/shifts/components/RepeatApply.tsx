import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { Button, Checkbox, Divider, InputNumber, Segmented, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { RecurringMeta, ShiftMap } from '../types';
import { computeTargets, RepeatMode } from '../utils/repeatEngine';
import { validateCellSlots } from '../utils/validation';

type Props = {
  caregiverId: number | null;
  baseDate: string | null; // YYYY-MM-DD
  shiftMap: ShiftMap;
  shiftList: (ShiftCreate | CaregiverShift)[];
  onRecurringChange?: (meta: RecurringMeta) => void;
};

const weekdaysLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function RepeatApply({ caregiverId, baseDate, shiftMap, shiftList, onRecurringChange }: Props) {
  const [mode, setMode] = useState<RepeatMode>('day');
  const [selectedWeekdays, setSelectedWeekdays] = useState<number[]>([]);
  const [weeks, setWeeks] = useState<number>(4);

  // initialize default weekday selection to the base date's day when switching to weekly/thisWeek
  useEffect(() => {
    if (!baseDate) return;
    const d = dayjs(baseDate).day();
    if ((mode === 'weekly' || mode === 'thisWeek') && selectedWeekdays.length === 0) {
      setSelectedWeekdays([d]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode, baseDate]);

  useEffect(() => {
    if (!onRecurringChange) return;
    if (mode === 'day') onRecurringChange({ mode: 'day' });
    else onRecurringChange({ mode, selectedWeekdays, weeks });
  }, [mode, selectedWeekdays, weeks, onRecurringChange]);

  const slots = useMemo(
    () =>
      shiftList
        .filter((s) => s.periodFrom && s.periodTo)
        .map((s) => ({ from: s.periodFrom as string, to: s.periodTo as string })),
    [shiftList]
  );

  const targets = useMemo(() => {
    if (caregiverId == null || !baseDate) return [];
    return computeTargets(caregiverId, {
      mode,
      startDate: baseDate,
      selectedWeekdays,
      weeks,
    });
  }, [caregiverId, baseDate, mode, selectedWeekdays, weeks]);

  const preview = useMemo(() => {
    let errors = 0;
    let warnings = 0;
    for (const t of targets) {
      const existing = shiftMap[t.caregiverId]?.[t.date] ?? [];
      const res = validateCellSlots(existing, slots);
      errors += res.errors.length;
      warnings += res.warnings.length;
    }
    return { errors, warnings, count: targets.length };
  }, [targets, shiftMap, slots]);

  // Application occurs via the main modal Save; this component only configures and previews recurring

  return (
    <div className="mt-4 p-3 border rounded-md border-app-gray bg-white">
      <Typography.Title level={5} style={{ marginTop: 0 }}>
        Repeat & Apply
      </Typography.Title>
      <div className="mb-2">
        <Segmented
          options={[
            { label: 'This day', value: 'day' },
            { label: 'This week', value: 'thisWeek' },
            { label: 'Every week', value: 'weekly' },
          ]}
          value={mode}
          onChange={(val) => setMode(val as RepeatMode)}
        />
      </div>
      {(mode === 'thisWeek' || mode === 'weekly') && (
        <div className="mb-2">
          <Space wrap>
            {weekdaysLabels.map((label, idx) => (
              <Checkbox
                key={label}
                checked={selectedWeekdays.includes(idx)}
                onChange={(e) => {
                  const set = new Set(selectedWeekdays);
                  if (e.target.checked) set.add(idx);
                  else set.delete(idx);
                  setSelectedWeekdays(Array.from(set).sort());
                }}
              >
                {label}
              </Checkbox>
            ))}
            <Button size="small" onClick={() => setSelectedWeekdays([1, 2, 3, 4, 5])}>
              Weekdays
            </Button>
            <Button size="small" onClick={() => setSelectedWeekdays([1, 3, 5])}>
              Mon/Wed/Fri
            </Button>
          </Space>
        </div>
      )}
      {mode === 'weekly' && (
        <div className="mb-2">
          <Space>
            For <InputNumber min={1} value={weeks} onChange={(v) => setWeeks(Number(v || 1))} /> weeks
          </Space>
        </div>
      )}
      <Divider style={{ margin: '8px 0' }} />
      <div className="text-sm">
        Applying {slots.map((s) => `${s.from}–${s.to}`).join(', ')} to {targets.length} cells.
        {preview.errors > 0 && <span className="text-red-600"> {preview.errors} errors</span>}
        {preview.warnings > 0 && <span className="text-amber-600"> {preview.warnings} warnings</span>}
      </div>
    </div>
  );
}
