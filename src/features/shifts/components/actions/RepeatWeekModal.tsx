import { Modal, Select, InputNumber, Space, Typography, Alert } from 'antd';
import { useState } from 'react';

type Props = {
  open: boolean;
  onCancel: () => void;
  onConfirm: (weeks: number) => void;
  overwriteCount?: number; // shows a warning if overwriting
};

export default function RepeatWeekModal({ open, onCancel, onConfirm, overwriteCount = 0 }: Props) {
  const [preset, setPreset] = useState<'1' | '2' | '4' | 'custom'>('2');
  const [customWeeks, setCustomWeeks] = useState<number>(4);
  const weeks = preset === 'custom' ? customWeeks : Number(preset);
  return (
    <Modal
      title="Repeat this week forward"
      open={open}
      onCancel={onCancel}
      onOk={() => onConfirm(weeks)}
      okText={`Apply (${weeks} weeks)`}
    >
      <Space direction="vertical" className="w-full">
        <Typography.Paragraph type="secondary">
          This will copy the current week’s shifts forward for the selected number of weeks. Existing shifts in future
          weeks may be overwritten depending on your choice in the next step.
        </Typography.Paragraph>
        <Space>
          <Typography.Text>Repeat for</Typography.Text>
          <Select
            value={preset}
            onChange={(v) => setPreset(v as '1' | '2' | '4' | 'custom')}
            options={[
              { value: '1', label: '1 week' },
              { value: '2', label: '2 weeks' },
              { value: '4', label: '4 weeks' },
              { value: 'custom', label: 'Custom' },
            ]}
          />
          {preset === 'custom' && (
            <InputNumber min={1} value={customWeeks} onChange={(v) => setCustomWeeks(Number(v || 1))} />
          )}
        </Space>
        {overwriteCount > 0 && (
          <Alert type="warning" showIcon message={`Warning: ${overwriteCount} shifts exist on target weeks.`} />
        )}
      </Space>
    </Modal>
  );
}
