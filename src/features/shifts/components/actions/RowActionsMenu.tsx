import { MoreOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps } from 'antd';
import { memo } from 'react';
import { CgExtensionRemove } from 'react-icons/cg';
import { FaRegPaste } from 'react-icons/fa6';
import { FiCopy } from 'react-icons/fi';
import { IoRepeatOutline } from 'react-icons/io5';
import { LuLayoutTemplate } from 'react-icons/lu';
type Props = {
  onRepeat: () => void;
  onCopy: () => void;
  onPaste: () => void;
  onClear: () => void;
  onTemplate: () => void;
};

const IconWrapper = memo(({ children }: { children: React.ReactNode }) => {
  return <div className="bg-slate-100 p-1.5 rounded-sm mr-3 my-1">{children}</div>;
});
IconWrapper.displayName = 'IconWrapper';

export default function RowActionsMenu({ onRepeat, onCopy, onPaste, onClear, onTemplate }: Props) {
  const items: MenuProps['items'] = [
    {
      key: 'repeat',
      icon: (
        <IconWrapper>
          <IoRepeatOutline />
        </IconWrapper>
      ),
      label: 'Repeat caregiver week',
      onClick: onRepeat,
    },
    {
      key: 'copy',
      icon: (
        <IconWrapper>
          <FiCopy />
        </IconWrapper>
      ),
      label: 'Copy caregiver week',
      onClick: onCopy,
    },
    {
      key: 'paste',
      icon: (
        <IconWrapper>
          <FaRegPaste />
        </IconWrapper>
      ),
      label: 'Paste caregiver week',
      onClick: onPaste,
    },
    {
      key: 'clear',
      icon: (
        <IconWrapper>
          <CgExtensionRemove />
        </IconWrapper>
      ),
      label: 'Clear caregiver week',
      onClick: onClear,
    },
    {
      key: 'template',
      disabled: true,
      icon: (
        <IconWrapper>
          <LuLayoutTemplate />
        </IconWrapper>
      ),
      label: 'Apply template',
      onClick: onTemplate,
    },
  ];

  return (
    <Dropdown menu={{ items }} trigger={['click']} placement="bottomLeft" overlayClassName="custom-dropdown-animation">
      <Button type="text" size="small" icon={<MoreOutlined />} aria-label="Row actions" />
    </Dropdown>
  );
}
