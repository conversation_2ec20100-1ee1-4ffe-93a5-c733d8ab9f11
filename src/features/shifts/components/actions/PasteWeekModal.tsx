import { DatePicker, Modal, Radio, Space } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';

type Props = {
  open: boolean;
  onCancel: () => void;
  onConfirm: (opts: { mode: 'overwrite' | 'merge'; targetWeekStart: string }) => void;
  initialWeekStart: string; // YYYY-MM-DD to seed the picker
};

export default function PasteWeekModal({ open, onCancel, onConfirm, initialWeekStart }: Props) {
  const [mode, setMode] = useState<'overwrite' | 'merge'>('overwrite');
  const [target, setTarget] = useState<Dayjs | null>(dayjs(initialWeekStart));
  // Sync picker when modal opens or the provided week changes
  useEffect(() => {
    if (open) setTarget(dayjs(initialWeekStart));
  }, [open, initialWeekStart]);
  return (
    <Modal
      title="Paste caregiver week"
      open={open}
      onCancel={onCancel}
      onOk={() => {
        if (!target) return;
        onConfirm({ mode, targetWeekStart: target.startOf('week').format('YYYY-MM-DD') });
      }}
      okText="Paste"
    >
      <Space direction="vertical" className="w-full">
        <p className="text-xs text-gray-600">
          Paste the copied week into a target week. Overwrite will remove existing shifts in the target week before
          pasting. Merge will keep existing shifts and add the copied ones. Resolve conflicts as needed.
        </p>
        <Radio.Group value={mode} onChange={(e) => setMode(e.target.value)}>
          <Radio value="overwrite">Overwrite</Radio>
          <Radio value="merge">Merge</Radio>
        </Radio.Group>
        <DatePicker picker="week" value={target} onChange={(d) => d && setTarget(d)} allowClear={false} />
      </Space>
    </Modal>
  );
}
