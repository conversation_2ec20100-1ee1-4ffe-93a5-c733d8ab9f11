import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { Form, TimePicker, Input, Button } from 'antd';
import dayjs from 'dayjs';
import React, { Dispatch, SetStateAction } from 'react';
import { MdDelete } from 'react-icons/md';

const isCaregiverShift = (shift: ShiftCreate | CaregiverShift): shift is CaregiverShift => {
  return (shift as CaregiverShift).caregiverShiftId !== undefined;
};

type Props = {
  shiftList: ShiftCreate[] | CaregiverShift[];
  setShiftList: Dispatch<SetStateAction<ShiftCreate[] | CaregiverShift[]>>;
  hideAddButton?: boolean;
  handleDeleteShift?: (id: number, index: number) => void;
};

const ShiftForm = ({ shiftList, setShiftList, hideAddButton, handleDeleteShift }: Props) => {
  const updateShift = (index: number, newShift: CaregiverShift | ShiftCreate) => {
    const updated = [...shiftList];
    updated[index] = { ...updated[index], ...newShift };
    setShiftList(updated as ShiftCreate[] | CaregiverShift[]);
  };

  const removeShift = (index: number, caregiverShiftId?: number) => {
    console.log('Shift delete', caregiverShiftId);
    if (!hideAddButton && shiftList.length <= 1) return;
    handleDeleteShift && caregiverShiftId
      ? handleDeleteShift(caregiverShiftId, index)
      : setShiftList(shiftList.filter((_, i) => i !== index) as ShiftCreate[] | CaregiverShift[]);
  };

  const addShift = () => setShiftList([...(shiftList as ShiftCreate[]), { periodFrom: '', periodTo: '', notes: '' }]);

  return (
    <Form layout="vertical">
      {shiftList.map((shift, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <div key={index} className="p-1 rounded-md">
          <div className="grid grid-cols-10 gap-4 items-end">
            <Form.Item label="Start Time" required className="col-span-3">
              <TimePicker
                format="HH:mm"
                className="w-full"
                value={shift.periodFrom ? dayjs(shift.periodFrom, 'HH:mm') : null}
                onChange={(time) => updateShift(index, { ...shift, periodFrom: time?.format('HH:mm') || '' })}
              />
            </Form.Item>
            <Form.Item label="End Time" required className="col-span-3">
              <TimePicker
                format="HH:mm"
                className="w-full"
                value={shift.periodTo ? dayjs(shift.periodTo, 'HH:mm') : null}
                onChange={(time) => updateShift(index, { ...shift, periodTo: time?.format('HH:mm') || '' })}
              />
            </Form.Item>
            <Form.Item label="Notes" required className="col-span-3">
              <Input
                value={shift.notes || ''}
                onChange={(e) => updateShift(index, { ...shift, notes: e.target.value })}
              />
            </Form.Item>
            <Form.Item label="" className="col-span-1">
              <Button
                type="text"
                danger
                size="small"
                onClick={() => removeShift(index, isCaregiverShift(shift) ? shift.caregiverShiftId : undefined)}
                disabled={!hideAddButton && shiftList.length <= 1}
              >
                <MdDelete className="text-lg" />
              </Button>
            </Form.Item>
          </div>
        </div>
      ))}
      {!hideAddButton && (
        <Button onClick={addShift} type="default" className="m-1">
          + Add Another Shift
        </Button>
      )}
    </Form>
  );
};

export default ShiftForm;
