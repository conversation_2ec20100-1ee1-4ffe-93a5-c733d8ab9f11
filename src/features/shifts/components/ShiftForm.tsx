import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import TimePickerCounter from '@app/components/ui/TimePickerCounter/TimePickerCounter';
import { ModalMode } from '@feat-shifts/types';
import { hhmmToMinutes, minutesToHHMM } from '@feat-shifts/utils/time';
import { Button, Form, Input } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useMemo } from 'react';
import { MdDelete } from 'react-icons/md';

type Props = {
  shiftList: ShiftCreate[] | CaregiverShift[];
  setShiftList: Dispatch<SetStateAction<ShiftCreate[] | CaregiverShift[]>>;
  hideAddButton?: boolean;
  handleDeleteShift?: (id: number, index: number) => void;
  mode: ModalMode;
  templates?: { from: string; to: string }[];
};

const isCaregiverShift = (shift: ShiftCreate | CaregiverShift): shift is CaregiverShift => {
  return (shift as CaregiverShift).caregiverShiftId !== undefined;
};

const timeToMinutes = (time?: string | null) => {
  if (!time) return null;
  const [h, m] = time.split(':').map(Number);
  return h * 60 + m;
};

const MINUTE_STEP = 15;

const ShiftForm = ({ shiftList, setShiftList, hideAddButton, handleDeleteShift, mode, templates }: Props) => {
  const updateShift = (index: number, newShift: CaregiverShift | ShiftCreate) => {
    const updated = [...shiftList];
    updated[index] = { ...updated[index], ...newShift };
    setShiftList(updated as ShiftCreate[] | CaregiverShift[]);
  };

  const removeShift = (index: number, caregiverShiftId?: number) => {
    console.log('Shift delete', caregiverShiftId);
    // if (!hideAddButton && shiftList.length <= 1) return;
    handleDeleteShift && caregiverShiftId
      ? handleDeleteShift(caregiverShiftId, index)
      : setShiftList(shiftList.filter((_, i) => i !== index) as ShiftCreate[] | CaregiverShift[]);
  };

  const addShift = () => setShiftList([...(shiftList as ShiftCreate[]), { periodFrom: '', periodTo: '', notes: '' }]);

  // templates are applied inline; no dedicated helper needed

  const overlapsWithOthers = (index: number, from: string, to: string) => {
    const a = hhmmToMinutes(from);
    const b = hhmmToMinutes(to);
    if (a == null || b == null) return true;
    return shiftList.some((s, i) => {
      if (i === index) return false;
      const sA = hhmmToMinutes(s.periodFrom || '');
      const sB = hhmmToMinutes(s.periodTo || '');
      if (sA == null || sB == null) return false;
      return a < sB && b > sA;
    });
  };

  const tryNudge = (index: number, which: 'from' | 'to', delta: number) => {
    const current = shiftList[index];
    const fromM = hhmmToMinutes(current.periodFrom || '');
    const toM = hhmmToMinutes(current.periodTo || '');
    if (fromM == null || toM == null) return;
    let newFrom = fromM;
    let newTo = toM;
    if (which === 'from') newFrom = Math.max(0, Math.min(toM - 1, fromM + delta));
    if (which === 'to') newTo = Math.min(24 * 60 - 1, Math.max(fromM + 1, toM + delta));

    const fromStr = minutesToHHMM(newFrom);
    const toStr = minutesToHHMM(newTo);
    if (overlapsWithOthers(index, fromStr, toStr)) return;
    updateShift(index, { ...current, periodFrom: fromStr, periodTo: toStr });
  };

  // without memo the function is called even on hover
  const disabledTimesList = useMemo(() => {
    return shiftList.map((shift, index) => {
      // convert “other” shifts to minute tuples for simpmicity in checks
      const otherShifts = shiftList
        .filter((_, i) => i !== index)
        .map((s) => [timeToMinutes(s.periodFrom), timeToMinutes(s.periodTo)] as [number | null, number | null]);

      // ---------- START TIME  ----------
      const getDisabledStartMinutes = (hour: number) => {
        const minutes: number[] = [];
        for (let m = 0; m < 60; m += MINUTE_STEP) {
          const candidate = hour * 60 + m;
          // if this minute overlaps with any other shift we push it to the disabled list
          const overlaps = otherShifts.some(
            ([sStart, sEnd]) => sStart !== null && sEnd !== null && candidate >= sStart && candidate < sEnd
          );
          if (overlaps) minutes.push(m);
        }
        return minutes;
      };

      const getDisabledStartHours = () => {
        const hours: number[] = [];
        for (let h = 0; h < 24; h++) {
          // if all minutes in an hour are disabled then the hour is disabled
          if (getDisabledStartMinutes(h).length === 60 / MINUTE_STEP) hours.push(h);
        }
        return hours;
      };

      // ---------- END TIME ----------
      const selectedStart = timeToMinutes(shift.periodFrom);

      // closest future shift start after selectedStart
      // if no future shift exists -> end of day
      const nextShiftStart =
        otherShifts
          .map(([sStart]) => sStart)
          .filter((s): s is number => s != null && (selectedStart == null || s > selectedStart))
          .sort((a, b) => a - b)[0] ?? 24 * 60;

      const getDisabledEndMinutes = (hour: number) => {
        const minutes: number[] = [];
        for (let m = 0; m < 60; m += MINUTE_STEP) {
          const candidate = hour * 60 + m;

          // A minute is disabled if:
          const disableBecauseNoStart = selectedStart == null; // Start time isn’t selected
          const disableBeforeOrAtStart = selectedStart != null && candidate <= selectedStart; // Minute is before or equal to start
          const disablePastNextShift = candidate > nextShiftStart; // Minute is after the next shift starts

          if (disableBecauseNoStart || disableBeforeOrAtStart || disablePastNextShift) {
            minutes.push(m);
          }
        }

        return minutes;
      };

      const getDisabledEndHours = () => {
        const hours: number[] = [];
        for (let h = 0; h < 24; h++) {
          if (getDisabledEndMinutes(h).length === 60 / MINUTE_STEP) hours.push(h);
        }
        // console.log(`[shift ${index}] disabled hours:`, hours);
        return hours;
      };

      return {
        start: {
          disabledHours: getDisabledStartHours,
          disabledMinutes: getDisabledStartMinutes,
        },
        end: {
          disabledHours: getDisabledEndHours,
          disabledMinutes: getDisabledEndMinutes,
        },
      };
    });
  }, [shiftList]);

  return (
    <Form layout="vertical">
      <div className="p-4 rounded-md border border-app-gray bg-white space-y-3">
        <div className="mb-2 rounded-md border border-app-gray bg-app-gray-light/40 p-2">
          <div className="flex flex-wrap items-center gap-2 text-xs">
            <span className="font-semibold text-app-text-light whitespace-nowrap">Suggested templates:</span>
            {(templates && templates.length > 0
              ? templates
              : [
                  { from: '09:00', to: '14:00' },
                  { from: '15:00', to: '19:00' },
                ]
            ).map((t) => {
              const disabled = shiftList.length > 1;
              return (
                <div
                  key={`${t.from}-${t.to}`}
                  className={`rounded-full px-3 py-1 border border-app-gray bg-white text-app-text-dark cursor-pointer hover:bg-app-gray-light ${disabled ? 'opacity-60 pointer-events-none' : ''}`}
                  onClick={() => {
                    if (disabled) return;
                    if (shiftList.length === 0) {
                      setShiftList([{ periodFrom: t.from, periodTo: t.to, notes: '' }] as ShiftCreate[]);
                      return;
                    }
                    if (shiftList.length === 1) {
                      const first = shiftList[0];
                      updateShift(0, { ...first, periodFrom: t.from, periodTo: t.to });
                      return;
                    }
                  }}
                  aria-label={`Template ${t.from}-${t.to}`}
                >
                  {t.from}–{t.to}
                </div>
              );
            })}
          </div>
        </div>
        {shiftList.map((shift, index) => {
          const key =
            isCaregiverShift(shift) && shift.caregiverShiftId
              ? String(shift.caregiverShiftId)
              : `${shift.periodFrom || 'from'}-${shift.periodTo || 'to'}-${shift.notes || ''}`;

          return (
            <div key={key} className="grid grid-cols-10 gap-2 items-end rounded-md">
              {/* Start time with per-field hover +/- */}
              <div className="col-span-2 !m-0">
                <Form.Item label={index === 0 ? 'Start Time' : undefined} required className="!m-0">
                  <TimePickerCounter
                    value={shift.periodFrom ? dayjs(shift.periodFrom, 'HH:mm') : null}
                    onChange={(time) => updateShift(index, { ...shift, periodFrom: time?.format('HH:mm') || '' })}
                    onNudge={(delta) => tryNudge(index, 'from', delta)}
                    disabledTime={() => disabledTimesList[index].start}
                    disabled={!shift.periodFrom || !shift.periodTo}
                  />
                </Form.Item>
              </div>

              {/* End time with per-field hover +/- */}
              <div className="col-span-2 !m-0">
                <Form.Item label={index === 0 ? 'End Time' : undefined} required className="!m-0">
                  <TimePickerCounter
                    value={shift.periodTo ? dayjs(shift.periodTo, 'HH:mm') : null}
                    onChange={(time) => updateShift(index, { ...shift, periodTo: time?.format('HH:mm') || '' })}
                    onNudge={(delta) => tryNudge(index, 'to', delta)}
                    disabledTime={() => disabledTimesList[index].end}
                    disabled={!shift.periodFrom || !shift.periodTo}
                  />
                </Form.Item>
              </div>

              <Form.Item label={index === 0 ? 'Notes' : undefined} className="col-span-5 !m-0">
                <Input
                  value={shift.notes || ''}
                  onChange={(e) => updateShift(index, { ...shift, notes: e.target.value })}
                />
              </Form.Item>

              <Form.Item label="" className="col-span-1 !m-0 flex justify-end">
                <Button
                  type="text"
                  danger
                  size="small"
                  onClick={() => removeShift(index, isCaregiverShift(shift) ? shift.caregiverShiftId : undefined)}
                  disabled={!hideAddButton && mode === 'create' && shiftList.length <= 1}
                >
                  <MdDelete className="text-lg" />
                </Button>
              </Form.Item>
            </div>
          );
        })}
        {!hideAddButton && (
          <Button onClick={addShift} type="default" className="m-1">
            + Add Another Shift
          </Button>
        )}
      </div>
    </Form>
  );
};

export default ShiftForm;
