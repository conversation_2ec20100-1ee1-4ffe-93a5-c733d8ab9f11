import { CaregiverShift, CaregiverWithServices, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { ShiftMap, UpdateAction } from '@feat-shifts/types';
import { expandRecurringDates } from '@feat-shifts/utils/repeatEngine';
import { Button, Modal, Tooltip } from 'antd';
import { AxiosResponse } from 'axios';
import dayjs, { Dayjs } from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { memo, ReactNode, useCallback, useState } from 'react';
import { CgExtensionRemove } from 'react-icons/cg';
import { PiFileCsvLight } from 'react-icons/pi';

import { AiOutlineQuestionCircle } from 'react-icons/ai';
import { FaRegPaste } from 'react-icons/fa6';
import { FiCopy } from 'react-icons/fi';
import { IoRepeatOutline } from 'react-icons/io5';
import { fetchShiftsByDates } from '../shifts.api';
import { useClipboardStore } from '../state/clipboard';
import RepeatApply from './RepeatApply';
import ShiftForm from './ShiftForm';
import PasteWeekModal from './actions/PasteWeekModal';
import RepeatWeekModal from './actions/RepeatWeekModal';
import ShiftsTableBody from './shiftsTable/ShiftsTableBody';
import ShiftsTableHeader from './shiftsTable/ShiftsTableHeader';
dayjs.extend(isoWeek);

import { ModalMode, RecurringMeta } from '@feat-shifts/types';

type Props = {
  shifts: ShiftMap;
  caregivers: CaregiverWithServices[];
  daysOfWeek: Dayjs[];
  dateNavigator: ReactNode;
  getTemplatesForDate?: (date: string) => { from: string; to: string }[];
  getDefaultSlotForDate?: (date: string) => { from: string; to: string };
  targetsLabel?: string;
  refreshWeek: () => Promise<void>;
  onCreate: (
    selectedCaregiver: number,
    selectedDate: string,
    shifts: ShiftCreate[],
    recurring?: RecurringMeta | null
  ) => Promise<AxiosResponse<number>[]>;
  onEdit: (
    selectedCaregiver: number,
    selectedDate: string,
    shifts: CaregiverShift[],
    recurring?: RecurringMeta | null
  ) => Promise<AxiosResponse<CaregiverShift>[]>;
  onDelete: (selectedCaregiver: number, shiftId: number) => Promise<AxiosResponse<void>>;
  onLocalUpdate: (action: UpdateAction) => void;
};

const ShiftsBoard = ({
  shifts,
  caregivers,
  daysOfWeek,
  dateNavigator,
  getTemplatesForDate,
  getDefaultSlotForDate,
  targetsLabel,
  onCreate,
  onEdit,
  onDelete,
  onLocalUpdate,
  refreshWeek,
}: Props) => {
  const { openNotification } = useNotifications();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mode, setMode] = useState<ModalMode>('create');

  const [selectedCaregiver, setSelectedCaregiver] = useState<null | number>(null);
  const [selectedDate, setSelectedDate] = useState<null | string>(null);
  const [shiftList, setShiftList] = useState<ShiftCreate[] | CaregiverShift[]>([
    { periodFrom: '', periodTo: '', notes: '' },
  ]);
  const [recurringMeta, setRecurringMeta] = useState<RecurringMeta>({ mode: 'day' });
  const [showFormHelp, setShowFormHelp] = useState(false);
  const [showRepeatHelp, setShowRepeatHelp] = useState(false);
  const [rowActionCaregiver, setRowActionCaregiver] = useState<number | null>(null);
  const [repeatRowOpen, setRepeatRowOpen] = useState(false);
  const [pasteRowOpen, setPasteRowOpen] = useState(false);
  const [clearRowOpen, setClearRowOpen] = useState(false);
  const [repeatWeekOpen, setRepeatWeekOpen] = useState(false);
  const [pasteWeekOpen, setPasteWeekOpen] = useState(false);
  const [clearWeekOpen, setClearWeekOpen] = useState(false);
  const { rowSnapshot, setRowSnapshot } = useClipboardStore();

  const closeModal = useCallback(() => {
    setIsModalOpen(false);
    setShiftList([{ periodFrom: '', periodTo: '', notes: '' }]);
    setSelectedCaregiver(null);
    setSelectedDate(null);
  }, []);

  const handleDeleteShift = useCallback(
    async (id: number, index: number) => {
      if (!selectedCaregiver || !selectedDate) return;
      onDelete(selectedCaregiver, id)
        .then(() => {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shift deleted successfully.',
            type: 'Success',
          });
          onLocalUpdate({
            type: 'delete',
            caregiverId: selectedCaregiver,
            date: selectedDate,
            shiftId: id,
          });
          const updateShiftList = shiftList.filter((_, i) => i !== index) as ShiftCreate[] | CaregiverShift[];
          setShiftList(updateShiftList);
          updateShiftList.length === 0 && setIsModalOpen(false);
        })
        .catch((error) => {
          console.log('error', isErrorWithDetail(error));
          const errorData = extractErrorData(error);
          if (isErrorWithDetail(errorData)) {
            openNotification('topRight', {
              title: `Shifts`,
              description: 'Shift deletion failed. ' + errorData.detail,
              type: 'Warning',
            });
          }
        });
    },
    [onDelete, onLocalUpdate, openNotification, selectedCaregiver, selectedDate, shiftList]
  );

  const handleSave = useCallback(async () => {
    if (!selectedCaregiver || !selectedDate) return;
    const validShifts = shiftList.filter((s) => s.periodFrom && s.periodTo);

    if (mode === 'create') {
      onCreate(selectedCaregiver, selectedDate, validShifts as ShiftCreate[], recurringMeta)
        .then(() => {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shifts created successfully.',
            type: 'Success',
          });
          onLocalUpdate({
            type: 'create',
            caregiverId: selectedCaregiver,
            date: selectedDate,
            shifts: validShifts as CaregiverShift[],
          });

          closeModal();
        })
        .catch((error) => {
          const errorData = extractErrorData(error);
          if (isErrorWithDetail(errorData)) {
            openNotification('topRight', {
              title: `Shifts`,
              description: 'Shifts creation failed. ' + errorData.detail,
              type: 'Warning',
            });
          }
        });
    }

    if (mode === 'edit') {
      const shiftsToUpdate = validShifts.filter((s): s is CaregiverShift => 'caregiverShiftId' in s);
      const shiftsToCreate = validShifts.filter((s): s is ShiftCreate => !('caregiverShiftId' in s));
      const promises: (Promise<AxiosResponse<CaregiverShift>[]> | Promise<AxiosResponse<number>[]>)[] = [];

      if (shiftsToUpdate.length > 0) {
        promises.push(onEdit(selectedCaregiver, selectedDate, shiftsToUpdate, recurringMeta));
      }
      if (shiftsToCreate.length > 0) {
        promises.push(onCreate(selectedCaregiver, selectedDate, shiftsToCreate, recurringMeta));
      }

      Promise.all(promises)
        .then(() => {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shifts saved successfully.',
            type: 'Success',
          });

          if (shiftsToUpdate.length > 0) {
            onLocalUpdate({
              type: 'update',
              caregiverId: selectedCaregiver,
              date: selectedDate,
              shifts: shiftsToUpdate,
            });
          }

          if (shiftsToCreate.length > 0) {
            onLocalUpdate({
              type: 'create',
              caregiverId: selectedCaregiver,
              date: selectedDate,
              shifts: shiftsToCreate as CaregiverShift[], // backend returns ids usually
            });
          }

          closeModal();
        })
        .catch((error) => {
          const errorData = extractErrorData(error);
          if (isErrorWithDetail(errorData)) {
            openNotification('topRight', {
              title: `Shifts`,
              description: 'Shifts save failed. ' + errorData.detail,
              type: 'Warning',
            });
          }
        });
    }
  }, [
    closeModal,
    mode,
    onCreate,
    onEdit,
    onLocalUpdate,
    openNotification,
    selectedCaregiver,
    selectedDate,
    shiftList,
    recurringMeta,
  ]);

  const handleClickAdd = useCallback(
    (id: number, dateKey: string) => {
      setSelectedCaregiver(id);
      setSelectedDate(dateKey);
      const def = getDefaultSlotForDate?.(dateKey) || { from: '09:00', to: '17:00' };
      setShiftList([{ periodFrom: def.from, periodTo: def.to, notes: '' }]);
      setMode('create');
      setRecurringMeta({ mode: 'day' });
      setIsModalOpen(true);
    },
    [getDefaultSlotForDate]
  );

  const handleClickEdit = useCallback((id: number, dateKey: string, shifts: CaregiverShift[]) => {
    setSelectedCaregiver(id);
    setSelectedDate(dateKey);
    setShiftList(shifts);
    setMode('edit');
    setRecurringMeta({ mode: 'day' });
    setIsModalOpen(true);
  }, []);

  // Quick add panel removed per request; keep original modal-based flow

  // UI/UX helpers for modal context and save button
  const validShifts = shiftList.filter((s) => s.periodFrom && s.periodTo);
  const hasOverlap = (() => {
    const toMin = (t?: string) => (t ? Number(t.split(':')[0]) * 60 + Number(t.split(':')[1]) : null);
    const arr = validShifts.map((s) => ({ a: toMin(s.periodFrom)!, b: toMin(s.periodTo)! })).sort((x, y) => x.a - y.a);
    for (let i = 1; i < arr.length; i++) if (arr[i].a < arr[i - 1].b) return true;
    return false;
  })();
  const targetDates = (() => {
    if (!selectedDate) return [] as string[];
    const extra = expandRecurringDates(selectedDate, recurringMeta);
    return Array.from(new Set([selectedDate, ...extra]));
  })();
  const saveDisabled = validShifts.length === 0 || hasOverlap;

  const okText = (() => {
    if (mode === 'create') {
      const n = targetDates.length;
      return `Save (${n} ${n === 1 ? 'day' : 'days'})`;
    }
    return 'Save';
  })();

  const scopeLabel = (() => {
    switch (recurringMeta.mode) {
      case 'day':
        return 'This day';
      case 'thisWeek':
        return 'This week';
      default:
        return 'Every week';
    }
  })();

  const targetsLabelText = targetsLabel ?? 'Days Affected';

  // ---------- Row actions handlers ----------
  const weekStartStr = daysOfWeek[0]?.format('YYYY-MM-DD');

  const openRepeatRow = (cgId: number) => {
    setRowActionCaregiver(cgId);
    setRepeatRowOpen(true);
  };
  const confirmRepeatRow = async (weeks: number) => {
    if (!rowActionCaregiver) return setRepeatRowOpen(false);
    await Promise.all(
      daysOfWeek.map(async (d) => {
        const sourceDate = d.format('YYYY-MM-DD');
        const targetDate = d.add(1, 'week').format('YYYY-MM-DD');
        const slots = (shifts[rowActionCaregiver!]?.[sourceDate] ?? []).map((s) => ({
          periodFrom: s.periodFrom as string,
          periodTo: s.periodTo as string,
          notes: s.notes || '',
        }));
        if (slots.length > 0)
          await onCreate(rowActionCaregiver!, targetDate, slots, { mode: 'weekly', weeks, selectedWeekdays: [] });
      })
    );
    setRepeatRowOpen(false);
    openNotification('topRight', { title: 'Shifts', description: 'Week repeated forward.', type: 'Success' });
    await refreshWeek();
  };

  const copyRowWeek = (cgId: number) => {
    if (!weekStartStr) return;
    const days = daysOfWeek.map((d) => {
      const date = d.format('YYYY-MM-DD');
      const items = (shifts[cgId]?.[date] ?? []).map((s) => ({
        from: s.periodFrom as string,
        to: s.periodTo as string,
      }));
      return { date, shifts: items };
    });
    setRowSnapshot({ caregiverId: cgId, weekStart: weekStartStr, days });
    // Copy row-only table to OS clipboard for convenience
    try {
      const headers = ['Caregiver', ...daysOfWeek.map((d) => d.format('ddd DD/MM'))];
      const name = `${caregivers.find((c) => c.caregiverId === cgId)?.firstName || ''} ${
        caregivers.find((c) => c.caregiverId === cgId)?.lastName || ''
      }`.trim();
      const cols = daysOfWeek.map((d) => {
        const date = d.format('YYYY-MM-DD');
        const items = (shifts[cgId]?.[date] ?? []).map((s) => `${s.periodFrom}-${s.periodTo}`);
        return items.join(' | ');
      });
      const text = [headers.join('\t'), [name, ...cols].join('\t')].join('\n');
      if (navigator.clipboard?.writeText) void navigator.clipboard.writeText(text);
    } catch (e) {
      // ignore clipboard errors in non-secure contexts
    }
    openNotification('topRight', { title: 'Clipboard', description: 'Caregiver week copied.', type: 'Success' });
  };

  const openPasteRow = (cgId: number) => {
    setRowActionCaregiver(cgId);
    setPasteRowOpen(true);
  };
  const confirmPasteRow = async ({
    mode,
    targetWeekStart,
  }: {
    mode: 'overwrite' | 'merge';
    targetWeekStart: string;
  }) => {
    if (!rowActionCaregiver) {
      setPasteRowOpen(false);
      return;
    }

    const caregiverId = rowActionCaregiver;
    const baseWeek = dayjs(targetWeekStart).startOf('week');
    const targetDates = Array.from({ length: 7 }, (_, i) => baseWeek.add(i, 'day').format('YYYY-MM-DD'));

    // Overwrite: fetch target week from server for this caregiver and delete existing shifts first
    if (mode === 'overwrite') {
      const targetWeekData = await fetchShiftsByDates(targetDates);
      const caregiverTargets = targetWeekData.filter((s) => s.caregiverId === caregiverId);
      if (caregiverTargets.length > 0) {
        await Promise.all(caregiverTargets.map((s) => onDelete(caregiverId, s.caregiverShiftId!)));
      }
    }

    // Create from copied snapshot aligned by day index to the chosen base week
    if (rowSnapshot) {
      await Promise.all(
        rowSnapshot.days.map(async (ds, idx) => {
          const targetDate = baseWeek.add(idx, 'day').format('YYYY-MM-DD');
          const payload = ds.shifts.map((s) => ({ periodFrom: s.from, periodTo: s.to, notes: '' }));
          if (payload.length > 0) await onCreate(caregiverId, targetDate, payload);
        })
      );
    }

    setPasteRowOpen(false);
    openNotification('topRight', { title: 'Shifts', description: 'Caregiver week pasted.', type: 'Success' });
    await refreshWeek();
  };

  const openClearRow = (cgId: number) => {
    setRowActionCaregiver(cgId);
    setClearRowOpen(true);
  };
  const confirmClearRow = async () => {
    console.log('clear row confirmed', rowActionCaregiver);
    if (!rowActionCaregiver) return setClearRowOpen(false);
    await Promise.all(
      daysOfWeek.map(async (d) => {
        const date = d.format('YYYY-MM-DD');
        const existing = shifts[rowActionCaregiver!]?.[date] ?? [];
        await Promise.all(existing.map((s) => onDelete(rowActionCaregiver!, s.caregiverShiftId!)));
      })
    );
    setClearRowOpen(false);
    openNotification('topRight', { title: 'Shifts', description: 'Week cleared.', type: 'Success' });
    await refreshWeek();
  };

  const openTemplateRow = () => {
    openNotification('topRight', { title: 'Templates', description: 'Template picker coming soon.', type: 'Info' });
  };

  // ---------- Week-level actions (entire roster) ----------
  const confirmRepeatWeek = async (weeks: number) => {
    await Promise.all(
      caregivers.map(async (cg) => {
        await Promise.all(
          daysOfWeek.map(async (d) => {
            const date = d.format('YYYY-MM-DD');
            const slots = (shifts[cg.caregiverId!]?.[date] ?? []).map((s) => ({
              periodFrom: s.periodFrom as string,
              periodTo: s.periodTo as string,
              notes: s.notes || '',
            }));
            if (slots.length > 0)
              await onCreate(cg.caregiverId!, date, slots, { mode: 'weekly', weeks, selectedWeekdays: [] });
          })
        );
      })
    );
    setRepeatWeekOpen(false);
    openNotification('topRight', {
      title: 'Shifts',
      description: 'Week repeated for all caregivers.',
      type: 'Success',
    });
    await refreshWeek();
  };

  const confirmPasteWeek = async ({
    mode,
    targetWeekStart,
  }: {
    mode: 'overwrite' | 'merge';
    targetWeekStart: string;
  }) => {
    const ws = useClipboardStore.getState().weekSnapshot;
    const baseWeek = dayjs(targetWeekStart).startOf('week');
    if (!ws) {
      setPasteWeekOpen(false);
      openNotification('topRight', { title: 'Clipboard', description: 'No copied week found.', type: 'Warning' });
      return;
    }
    console.log('confirmPasteWeek', { mode });
    if (mode === 'overwrite') {
      // 1) Fetch the current data for the target week from the server
      const targetDates = Array.from({ length: 7 }, (_, i) => baseWeek.add(i, 'day').format('YYYY-MM-DD'));
      const targetWeekData = await fetchShiftsByDates(targetDates);
      // 2) Remove everything in the target week (for all caregivers) before creating
      await Promise.all(targetWeekData.map((s) => onDelete(s.caregiverId!, s.caregiverShiftId!)));
    }
    await Promise.all(
      ws.caregivers.map(async (cg) => {
        await Promise.all(
          cg.days.map(async (ds, idx) => {
            const targetDate = baseWeek.add(idx, 'day').format('YYYY-MM-DD');
            const payload = ds.shifts.map((s) => ({ periodFrom: s.from, periodTo: s.to, notes: '' }));
            if (payload.length > 0) await onCreate(cg.caregiverId!, targetDate, payload);
          })
        );
      })
    );
    setPasteWeekOpen(false);
    openNotification('topRight', { title: 'Shifts', description: 'Week pasted for all caregivers.', type: 'Success' });
    await refreshWeek();
  };

  const confirmClearWeek = async () => {
    await Promise.all(
      caregivers.map(async (cg) => {
        await Promise.all(
          daysOfWeek.map(async (d) => {
            const date = d.format('YYYY-MM-DD');
            const existing = shifts[cg.caregiverId!]?.[date] ?? [];
            await Promise.all(existing.map((s) => onDelete(cg.caregiverId!, s.caregiverShiftId!)));
          })
        );
      })
    );
    setClearWeekOpen(false);
    openNotification('topRight', { title: 'Shifts', description: 'Week cleared for all caregivers.', type: 'Success' });
    await refreshWeek();
  };

  return (
    <div>
      {/* Row-level modals */}
      <RepeatWeekModal open={repeatRowOpen} onCancel={() => setRepeatRowOpen(false)} onConfirm={confirmRepeatRow} />
      <PasteWeekModal
        open={pasteRowOpen}
        onCancel={() => setPasteRowOpen(false)}
        onConfirm={confirmPasteRow}
        initialWeekStart={daysOfWeek[0]?.format('YYYY-MM-DD')}
      />
      <Modal
        title="Clear caregiver week"
        open={clearRowOpen}
        onCancel={() => setClearRowOpen(false)}
        onOk={confirmClearRow}
        okText="Clear"
        okButtonProps={{ danger: true }}
      >
        This will remove all shifts for this caregiver in the current week.
      </Modal>
      {/* Week-level modals */}
      <RepeatWeekModal open={repeatWeekOpen} onCancel={() => setRepeatWeekOpen(false)} onConfirm={confirmRepeatWeek} />
      <PasteWeekModal
        open={pasteWeekOpen}
        onCancel={() => setPasteWeekOpen(false)}
        onConfirm={confirmPasteWeek}
        initialWeekStart={daysOfWeek[0]?.format('YYYY-MM-DD')}
      />
      <Modal
        title="Clear entire week"
        open={clearWeekOpen}
        onCancel={() => setClearWeekOpen(false)}
        onOk={confirmClearWeek}
        okText="Clear week"
        okButtonProps={{ danger: true }}
      >
        This will remove all shifts for all caregivers in the current week.
      </Modal>

      {isModalOpen && (
        <Modal
          title={mode === 'create' ? 'Schedule Shifts' : 'Update Shifts'}
          open={isModalOpen}
          onCancel={closeModal}
          onOk={handleSave}
          okText={okText}
          okButtonProps={{ disabled: saveDisabled }}
          cancelText="Cancel"
          width={600}
        >
          <div className="mb-3 space-y-2">
            <div className="rounded-md border border-app-gray bg-white px-3 py-2 text-sm">
              {selectedCaregiver != null ? (
                <span>
                  Caregiver: {caregivers.find((c) => c.caregiverId === selectedCaregiver)?.firstName || ''}{' '}
                  {caregivers.find((c) => c.caregiverId === selectedCaregiver)?.lastName || ''}
                </span>
              ) : (
                <span>Caregiver: —</span>
              )}
            </div>
            <div className="grid grid-cols-3 gap-2 text-sm">
              <div className="rounded-md border border-app-gray bg-white px-3 py-2">
                Day: {selectedDate ? dayjs(selectedDate).format('ddd, DD MMM YYYY') : '—'}
              </div>
              <div className="rounded-md border border-app-gray bg-white px-3 py-2">Scope: {scopeLabel}</div>
              <div className="rounded-md border border-app-gray bg-white px-3 py-2">
                {targetsLabelText}: {targetDates.length} {targetDates.length === 1 ? 'day' : 'days'}
              </div>
            </div>
            {/* Slots & Templates help toggle */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-semibold">Slots & Templates</span>
              <Tooltip title={showFormHelp ? 'Hide help' : 'Show help'}>
                <Button
                  type="link"
                  size="small"
                  onClick={() => setShowFormHelp((v) => !v)}
                  icon={<AiOutlineQuestionCircle />}
                >
                  {showFormHelp ? 'Hide help' : 'Help'}
                </Button>
              </Tooltip>
            </div>
          </div>
          {/* Help just for Slots & Templates */}
          {showFormHelp && (
            <div className="mb-3 text-xs text-app-text-light space-y-2">
              <div>
                <span className="font-semibold">Slots:</span> Pick Start and End. Hover each time field to use the +/-
                buttons (±1h). Overlaps are blocked; add multiple slots if needed. Notes are optional.
              </div>
              <div>
                <span className="font-semibold">Templates:</span> Click a template to auto-fill a slot. If one slot
                exists it will be replaced; if many exist, templates are disabled.
              </div>
            </div>
          )}
          <ShiftForm
            shiftList={shiftList}
            setShiftList={setShiftList}
            handleDeleteShift={mode === 'edit' ? handleDeleteShift : undefined}
            mode={mode}
            templates={selectedDate ? getTemplatesForDate?.(selectedDate) : undefined}
          />
          {/* Repeat application section lives after the regular shift form; uses current slots */}
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm font-semibold">Repeat & Scope</span>
            <Tooltip title={showRepeatHelp ? 'Hide help' : 'Show help'}>
              <Button
                type="link"
                size="small"
                onClick={() => setShowRepeatHelp((v) => !v)}
                icon={<AiOutlineQuestionCircle />}
              >
                {showRepeatHelp ? 'Hide help' : 'Help'}
              </Button>
            </Tooltip>
          </div>
          {showRepeatHelp && (
            <div className="mb-3 text-xs text-app-text-light space-y-2">
              <div>
                Choose This day, This week, or Every week. Select weekdays and number of weeks to control how many days
                are updated. The “Days Affected” box shows the total.
              </div>
              <div>Saving applies changes in batch and refreshes the current week.</div>
            </div>
          )}
          <RepeatApply
            caregiverId={selectedCaregiver}
            baseDate={selectedDate}
            shiftMap={shifts}
            shiftList={shiftList}
            onRecurringChange={setRecurringMeta}
          />
        </Modal>
      )}
      {dateNavigator}
      {/* Week-level toolbar */}
      <div className="flex items-center gap-2 px-2 py-2">
        <Button
          className="!px-2 !py-1"
          onClick={() => setRepeatWeekOpen(true)}
          icon={
            <div className="bg-slate-100 p-1 rounded-sm">
              <IoRepeatOutline />
            </div>
          }
        >
          Repeat week (all)
        </Button>
        <Button
          className="!px-2 !py-1"
          icon={
            <div className="bg-slate-100 p-1 rounded-sm">
              <FiCopy />
            </div>
          }
          onClick={() => {
            // Copy full week
            const weekStartStr = daysOfWeek[0]?.format('YYYY-MM-DD');
            if (!weekStartStr) return;
            const caregiversSnapshot = caregivers.map((cg) => {
              const days = daysOfWeek.map((d) => {
                const date = d.format('YYYY-MM-DD');
                const items = (shifts[cg.caregiverId!]?.[date] ?? []).map((s) => ({
                  from: s.periodFrom as string,
                  to: s.periodTo as string,
                }));
                return { date, shifts: items };
              });
              return { caregiverId: cg.caregiverId!, days };
            });
            // Store in clipboard week snapshot
            useClipboardStore.getState().setWeekSnapshot({ weekStart: weekStartStr, caregivers: caregiversSnapshot });
            // Copy plain text table to OS clipboard
            try {
              const headers = ['Caregiver', ...daysOfWeek.map((d) => d.format('ddd DD/MM'))];
              const rows = caregivers.map((cg) => {
                const name = `${cg.firstName || ''} ${cg.lastName || ''}`.trim();
                const cols = daysOfWeek.map((d) => {
                  const date = d.format('YYYY-MM-DD');
                  const items = (shifts[cg.caregiverId!]?.[date] ?? []).map((s) => `${s.periodFrom}-${s.periodTo}`);
                  return items.join(' | ');
                });
                return [name, ...cols].join('\t');
              });
              const text = [headers.join('\t'), ...rows].join('\n');
              if (navigator.clipboard?.writeText) {
                void navigator.clipboard.writeText(text);
              }
            } catch (e) {
              // ignore clipboard errors (non-secure context, permissions, etc.)
            }
            openNotification('topRight', { title: 'Clipboard', description: 'Week copied.', type: 'Success' });
          }}
        >
          Copy week
        </Button>
        <Button
          className="!px-2 !py-1"
          onClick={() => setPasteWeekOpen(true)}
          icon={
            <div className="bg-slate-100 p-1 rounded-sm">
              <FaRegPaste />
            </div>
          }
        >
          Paste week (all)
        </Button>
        <Button
          className="!px-2 !py-1 rounded-md border border-red-300 bg-white hover:bg-red-50 text-red-600"
          onClick={() => setClearWeekOpen(true)}
          icon={
            <div className="bg-slate-100 p-1 rounded-sm">
              <CgExtensionRemove />
            </div>
          }
        >
          Clear week (all)
        </Button>
        <Button
          className="ml-auto !px-2 !py-1"
          icon={
            <div className="bg-slate-100 p-1 rounded-sm">
              <PiFileCsvLight />
            </div>
          }
          onClick={() => {
            // Export CSV
            const headers = ['Caregiver', ...daysOfWeek.map((d) => d.format('ddd DD/MM'))];
            const rows = caregivers.map((cg) => {
              const name = `${cg.firstName || ''} ${cg.lastName || ''}`.trim();
              const cols = daysOfWeek.map((d) => {
                const date = d.format('YYYY-MM-DD');
                const items = (shifts[cg.caregiverId!]?.[date] ?? []).map((s) => `${s.periodFrom}-${s.periodTo}`);
                return `"${items.join(' | ')}"`;
              });
              return [name, ...cols].join(',');
            });
            const csv = [headers.join(','), ...rows].join('\n');
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `shifts_week_${daysOfWeek[0]?.format('YYYY_MM_DD')}.csv`;
            a.click();
            URL.revokeObjectURL(url);
          }}
        >
          Export CSV
        </Button>
      </div>
      <div className="rounded-md overflow-hidden border border-app-gray max-h-[80vh] flex flex-col">
        <div className="overflow-auto w-full h-full">
          <table className="table-fixed border-collapse w-full">
            <ShiftsTableHeader daysOfWeek={daysOfWeek} />
            <ShiftsTableBody
              caregiversList={caregivers}
              daysOfWeek={daysOfWeek}
              shiftMap={shifts}
              handleAdd={handleClickAdd}
              handleEdit={handleClickEdit}
              onRowRepeat={openRepeatRow}
              onRowCopy={copyRowWeek}
              onRowPaste={openPasteRow}
              onRowClear={openClearRow}
              onRowTemplate={openTemplateRow}
            />
          </table>
        </div>
      </div>
    </div>
  );
};

export default memo(ShiftsBoard);
