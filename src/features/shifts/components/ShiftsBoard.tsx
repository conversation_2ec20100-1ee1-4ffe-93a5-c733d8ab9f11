import { But<PERSON>, Modal } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { memo, useEffect, useState } from 'react';
import { Caregiver, CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { ApiClient } from '@api/api-configuration';
import ShiftsTableBody from './shiftsTable/ShiftsTableBody';
import ShiftsTableHeader from './shiftsTable/ShiftsTableHeader';
import ShiftForm from './ShiftForm';
import { AxiosResponse } from 'axios';
import useNotifications from '@context/notifications/useNotificationContext';
import { isErrorWithDetail, extractErrorData } from '@app/utils/isErrorwithErrors';
import qs from 'qs';

dayjs.extend(isoWeek);

type ShiftMap = {
  [caregiverId: number]: {
    [date: string]: CaregiverShift[];
  };
};

type Props = {
  onCreate: (
    selectedCaregiver: number,
    selectedDate: string,
    shifts: ShiftCreate[]
  ) => Promise<AxiosResponse<number>[]>;
  onEdit: (
    selectedCaregiver: number,
    selectedDate: string,
    shifts: CaregiverShift[]
  ) => Promise<AxiosResponse<CaregiverShift>[]>;
  onDelete: (selectedCaregiver: number, shiftId: number) => Promise<AxiosResponse<void>>;
};

const ShiftsBoard = ({ onCreate, onEdit, onDelete }: Props) => {
  const { openNotification } = useNotifications();

  const [weekStart, setWeekStart] = useState<Dayjs>(dayjs().startOf('week'));
  const [caregiversList, setCaregiversList] = useState<Caregiver[]>([]);
  const [shiftMap, setShiftMap] = useState<ShiftMap>({});

  const daysOfWeek = Array.from({ length: 7 }, (_, i) => weekStart.add(i, 'day'));
  const dateStrings = daysOfWeek.map((d) => d.format('YYYY-MM-DD'));

  const prevWeek = () => setWeekStart((w) => w.subtract(1, 'week'));
  const nextWeek = () => setWeekStart((w) => w.add(1, 'week'));

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const [selectedCaregiver, setSelectedCaregiver] = useState<null | number>(null);
  const [selectedDate, setSelectedDate] = useState<null | string>(null);
  const [shiftList, setShiftList] = useState<ShiftCreate[] | CaregiverShift[]>([
    { periodFrom: '', periodTo: '', notes: '' },
  ]);

  // Fetch caregivers on mount
  useEffect(() => {
    const fetchCaregivers = async () => {
      try {
        const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet();
        setCaregiversList(response.data);
      } catch (error) {
        console.error('Failed to fetch caregivers:', error);
      }
    };
    fetchCaregivers();
  }, []);

  // Fetch shifts when weekStart changes
  const fetchShiftsForWeek = async () => {
    console.log('Fetching', dateStrings);

    const newShiftMap: ShiftMap = {};
    try {
      const response = await ApiClient.caregiverApi.caregivers.getShiftsByDatesCaregiversShiftsByDatesGet(
        {
          dates: dateStrings,
        },
        {
          paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
        }
      );

      console.log('RESPONSE', response);

      const shiftData = await response.data;
      for (const shift of shiftData) {
        const caregiverId = shift.caregiverId!;
        const shiftDate = dayjs(shift.periodFrom).format('YYYY-MM-DD');

        if (!newShiftMap[caregiverId]) {
          newShiftMap[caregiverId] = {};
        }
        if (!newShiftMap[caregiverId][shiftDate]) {
          newShiftMap[caregiverId][shiftDate] = [];
        }

        const start = dayjs(shift.periodFrom).format('HH:mm');
        const end = dayjs(shift.periodTo).format('HH:mm');
        const enrichedShift = {
          ...shift,
          periodFrom: start,
          periodTo: end,
        };

        newShiftMap[caregiverId][shiftDate].push(enrichedShift);
      }
      setShiftMap(newShiftMap);
    } catch (error) {
      console.error('Failed to fetch shifts:', error);
    }
  };

  useEffect(() => {
    fetchShiftsForWeek();
  }, [weekStart]);

  const handleDeleteShift = async (id: number, index: number) => {
    if (!selectedCaregiver || !selectedDate) return;
    onDelete(selectedCaregiver, id)
      .then((res) => {
        console.log('Deleting Shift res', res);
        openNotification('topRight', {
          title: `Shifts`,
          description: 'Shift deleted successfully.',
          type: 'Success',
        });
        const updated = { ...shiftMap };
        if (!updated[selectedCaregiver]) return updated;

        updated[selectedCaregiver][selectedDate] = updated[selectedCaregiver][selectedDate].filter(
          (shift) => shift.caregiverShiftId !== id
        );
        setShiftMap(updated);
        const updateShiftList = shiftList.filter((_, i) => i !== index) as ShiftCreate[] | CaregiverShift[];
        setShiftList(updateShiftList);
        updateShiftList.length === 0 && setIsEditModalOpen(false);
      })
      .catch((error) => {
        console.log('error', isErrorWithDetail(error));
        const errorData = extractErrorData(error);
        if (isErrorWithDetail(errorData)) {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shift deletion failed. ' + errorData.detail,
            type: 'Warning',
          });
        }
      });
  };

  const handleUpdateShifts = async () => {
    if (!selectedCaregiver || !selectedDate) return;
    const validShifts = shiftList.filter((s) => s.periodFrom && s.periodTo);
    console.log('Editing Shifts', selectedCaregiver, validShifts);

    onEdit(selectedCaregiver, selectedDate, validShifts as CaregiverShift[])
      .then((res) => {
        console.log('Adding Shifts res', res);
        openNotification('topRight', {
          title: `Shifts`,
          description: 'Shifts updated successfully.',
          type: 'Success',
        });
        const updated = { ...shiftMap };
        if (!updated[selectedCaregiver]) updated[selectedCaregiver] = {};
        updated[selectedCaregiver][selectedDate] = validShifts as CaregiverShift[];
        setShiftMap(updated);

        setIsEditModalOpen(false);
      })
      .catch((error) => {
        console.log('error', isErrorWithDetail(error));
        const errorData = extractErrorData(error);
        if (isErrorWithDetail(errorData)) {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shifts update failed. ' + errorData.detail,
            type: 'Warning',
          });
        }
      });
  };

  const handleAddShift = async () => {
    if (!selectedCaregiver || !selectedDate) return;
    const validShifts = shiftList.filter((s) => s.periodFrom && s.periodTo);
    console.log('Adding Shifts', selectedCaregiver, validShifts);

    onCreate(selectedCaregiver, selectedDate, validShifts as ShiftCreate[])
      .then((res) => {
        console.log('Adding Shifts res', res);
        openNotification('topRight', {
          title: `Shifts`,
          description: 'Shifts created successfully.',
          type: 'Success',
        });
        const updated = { ...shiftMap };
        if (!updated[selectedCaregiver]) updated[selectedCaregiver] = {};
        updated[selectedCaregiver][selectedDate] = validShifts as CaregiverShift[];
        setShiftMap(updated);

        setIsCreateModalOpen(false);
        setShiftList([{ periodFrom: '', periodTo: '', notes: '' }]);
        fetchShiftsForWeek();
      })
      .catch((error) => {
        console.log('error', isErrorWithDetail(error));
        const errorData = extractErrorData(error);
        if (isErrorWithDetail(errorData)) {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shifts creation failed. ' + errorData.detail,
            type: 'Warning',
          });
        }
      });
  };

  const handleClickAdd = (id: number, dateKey: string) => {
    setSelectedCaregiver(id);
    setSelectedDate(dateKey);
    setShiftList([{ periodFrom: '', periodTo: '', notes: '' }]);
    setIsCreateModalOpen(true);
  };

  const handleClickEdit = (id: number, dateKey: string, shifts: CaregiverShift[]) => {
    setSelectedCaregiver(id);
    setSelectedDate(dateKey);
    setShiftList(shifts);
    setIsEditModalOpen(true);
    console.log('EDIT');
  };

  return (
    <div className="p-6">
      {isCreateModalOpen && (
        <Modal
          title="Add Shifts"
          open={isCreateModalOpen}
          onCancel={() => setIsCreateModalOpen(false)}
          onOk={handleAddShift}
          okText="Save"
          cancelText="Cancel"
          width={600}
        >
          <ShiftForm shiftList={shiftList} setShiftList={setShiftList} />
        </Modal>
      )}
      {isEditModalOpen && (
        <Modal
          title="Edit Shifts"
          open={isEditModalOpen}
          onCancel={() => setIsEditModalOpen(false)}
          onOk={handleUpdateShifts}
          okText="Save"
          width={600}
        >
          <ShiftForm
            shiftList={shiftList}
            setShiftList={setShiftList}
            hideAddButton={true}
            handleDeleteShift={handleDeleteShift}
          />
        </Modal>
      )}
      <div className="flex justify-center items-center mb-4">
        <div className="flex items-center space-x-4 select-none">
          <Button type="text" size="small" onClick={prevWeek}>
            &lt;
          </Button>
          <span className="font-medium">
            {weekStart.format('D MMM')} - {weekStart.add(6, 'day').format('D MMM')}
          </span>
          <Button type="text" size="small" onClick={nextWeek}>
            &gt;
          </Button>
        </div>
      </div>

      <div className="rounded-md overflow-hidden border border-app-gray max-h-[80vh] flex flex-col">
        <div className="overflow-auto w-full h-full">
          <table className="table-fixed border-collapse w-full">
            <ShiftsTableHeader daysOfWeek={daysOfWeek} />
            <ShiftsTableBody
              caregiversList={caregiversList}
              daysOfWeek={daysOfWeek}
              shiftMap={shiftMap}
              handleAdd={handleClickAdd}
              handleEdit={handleClickEdit}
            />
          </table>
        </div>
      </div>
    </div>
  );
};

export default memo(ShiftsBoard);
