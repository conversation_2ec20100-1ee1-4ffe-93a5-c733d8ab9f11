import { useEffect, useState } from 'react';
import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import { fetchCaregivers } from '../shifts.api';

export function useCaregivers() {
  const [caregivers, setCaregivers] = useState<CaregiverWithServices[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<unknown>(null);

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    fetchCaregivers()
      .then((data) => {
        if (mounted) setCaregivers(data);
      })
      .catch((err) => mounted && setError(err))
      .finally(() => mounted && setLoading(false));
    return () => {
      mounted = false;
    };
  }, []);

  return { caregivers, loading, error };
}
