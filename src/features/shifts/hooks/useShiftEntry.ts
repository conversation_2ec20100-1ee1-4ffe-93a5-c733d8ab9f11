import { useMemo, useState } from 'react';
import { TimeSlot, addMinutes } from '../utils/time';
import { parseTimeInput } from '../utils/timeParser';
import { computeTargets, RepeatMode } from '../utils/repeatEngine';
import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { ShiftMap } from '../types';
import { validateCellSlots } from '../utils/validation';

export type RepeatState = {
  mode: RepeatMode;
  selectedWeekdays: number[]; // day() values 0..6
  weeks: number; // for weekly
};

export function useShiftEntry(caregiverId: number | null, date: string | null, shiftMap: ShiftMap) {
  const [rawInput, setRawInput] = useState('');
  const [slots, setSlots] = useState<TimeSlot[]>([]);
  const [focusedIndex, setFocusedIndex] = useState(0);
  const [repeat, setRepeat] = useState<RepeatState>({ mode: 'day', selectedWeekdays: [], weeks: 4 });

  const parse = () => {
    const { slots: parsed, errors } = parseTimeInput(rawInput);
    if (errors.length === 0) setSlots(parsed);
    return { parsed, errors };
  };

  const nudge = (minutes: number) => {
    setSlots((prev) =>
      prev.map((s, i) =>
        i === focusedIndex ? { from: addMinutes(s.from, minutes), to: addMinutes(s.to, minutes) } : s
      )
    );
  };

  const targets = useMemo(() => {
    if (caregiverId == null || !date) return [];
    return computeTargets(caregiverId, {
      mode: repeat.mode,
      startDate: date,
      selectedWeekdays: repeat.selectedWeekdays,
      weeks: repeat.weeks,
    });
  }, [caregiverId, date, repeat]);

  const preview = useMemo(() => {
    let errorCount = 0;
    let warningCount = 0;
    const perCell: { key: string; errors: string[]; warnings: string[] }[] = [];
    for (const t of targets) {
      const existing: CaregiverShift[] = shiftMap[t.caregiverId]?.[t.date] ?? [];
      const res = validateCellSlots(existing, slots);
      if (res.errors.length) errorCount += res.errors.length;
      if (res.warnings.length) warningCount += res.warnings.length;
      perCell.push({ key: `${t.caregiverId}-${t.date}`, errors: res.errors, warnings: res.warnings });
    }
    return { count: targets.length, errorCount, warningCount, perCell };
  }, [targets, slots, shiftMap]);

  const toShiftCreate = (s: TimeSlot): ShiftCreate => ({ periodFrom: s.from, periodTo: s.to, notes: '' });

  return {
    rawInput,
    setRawInput,
    slots,
    setSlots,
    focusedIndex,
    setFocusedIndex,
    repeat,
    setRepeat,
    parse,
    nudge,
    targets,
    preview,
    toShiftCreate,
  };
}
