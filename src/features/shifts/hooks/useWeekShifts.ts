import { CaregiverShift } from '@api/READ_ONLY/caregiver_api/Api';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { fetchShiftsByDates } from '../shifts.api';
import { ShiftMap } from '../types';

export function useWeek(currentDate: Dayjs) {
  const weekStart = useMemo(() => currentDate.startOf('week'), [currentDate]);
  const daysOfWeek = useMemo(() => Array.from({ length: 7 }, (_, i) => weekStart.add(i, 'day')), [weekStart]);
  const dateStrings = useMemo(() => daysOfWeek.map((d) => d.format('YYYY-MM-DD')), [daysOfWeek]);
  return { weekStart, daysOfWeek, dateStrings };
}

export function useWeekShifts(
  dateStrings: string[],
  options?: {
    autoFetch?: boolean;
  }
) {
  const [shiftMap, setShiftMap] = useState<ShiftMap>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<unknown>(null);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    if (options?.autoFetch === false) return;
    let mounted = true;
    async function load() {
      try {
        setLoading(true);
        console.log('useWeekShifts Loading shifts for dates:', dateStrings);
        const shiftData: CaregiverShift[] = await fetchShiftsByDates(dateStrings);
        if (!mounted) return;
        const newShiftMap: ShiftMap = {};
        for (const shift of shiftData) {
          const caregiverId = shift.caregiverId!;
          const shiftDate = dayjs(shift.periodFrom).format('YYYY-MM-DD');
          if (!newShiftMap[caregiverId]) newShiftMap[caregiverId] = {};
          if (!newShiftMap[caregiverId][shiftDate]) newShiftMap[caregiverId][shiftDate] = [];

          const start = dayjs(shift.periodFrom).format('HH:mm');
          const end = dayjs(shift.periodTo).format('HH:mm');
          const enriched: CaregiverShift = { ...shift, periodFrom: start, periodTo: end } as CaregiverShift;
          newShiftMap[caregiverId][shiftDate].push(enriched);
        }
        setShiftMap(newShiftMap);
      } catch (e) {
        if (mounted) setError(e);
      } finally {
        if (mounted) setLoading(false);
      }
    }
    if (dateStrings.length) load();
    return () => {
      mounted = false;
    };
  }, [dateStrings, refreshIndex, options?.autoFetch]);

  const refetch = () => setRefreshIndex((x) => x + 1);

  return { shiftMap, setShiftMap, loading, error, refetch };
}
