import { MenuOutlined } from '@ant-design/icons';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { applyDefaultWidthToColumns } from '@app/utils/applyWidthColumns';
import { useTable } from '@context/table/TableProvider';
import { Button, Checkbox, Divider, Input, Tooltip } from 'antd';
import { memo, useEffect, useRef, useState } from 'react';
import { DragDropContext, Draggable, Droppable, DropResult } from 'react-beautiful-dnd';

const TableColumnsDropDown = () => {
  //next step-> abstract columns type. parent handles everything (not columns type but label,value,checked)
  const { columns, handleColumnsCustomisation } = useTable();
  const colsRef = useRef<ExtendedTableColumnType<unknown>[]>(columns);

  const [checkedList, setCheckedList] = useState<string[]>([]);
  const [orderedKeys, setOrderedKeys] = useState<string[]>([]);
  const [options, setOptions] = useState<{ label: string; value: string; defaultVal: boolean }[]>([]);

  useEffect(() => {
    colsRef.current = applyDefaultWidthToColumns(columns);

    const all = columns.map((col) => ({
      label: String(col.title),
      value: String(col.key),
      defaultVal: !!col.default,
    }));

    const visible = all.filter((col) => !columns.find((c) => c.key === col.value)?.hidden).map((col) => col.value);

    setOptions(all);
    setOrderedKeys(all.map((col) => col.value));
    setCheckedList(visible);
  }, []);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const reordered = Array.from(orderedKeys);
    const [moved] = reordered.splice(result.source.index, 1);
    reordered.splice(result.destination.index, 0, moved);
    setOrderedKeys(reordered);

    const newOrderedColumns = reordered
      .map((key) => columns.find((col) => col.key === key))
      .filter(Boolean) as ExtendedTableColumnType<unknown>[];

    handleColumnsCustomisation(newOrderedColumns);
  };

  const updateCheckedList = (newList: string[]) => {
    setCheckedList(newList);

    const updatedColumns = columns.map((item) => ({
      ...item,
      hidden: !newList.includes(String(item.key)),
    }));

    handleColumnsCustomisation(updatedColumns);
  };

  const handleHideAll = () => {
    const nonDefault = options.filter((opt) => !opt.defaultVal).map((opt) => opt.value);
    const newList = checkedList.filter((key) => !nonDefault.includes(key));
    updateCheckedList(newList);
  };

  const handleShowAll = () => {
    const nonDefault = options.filter((opt) => !opt.defaultVal).map((opt) => opt.value);
    const newList = [...new Set([...checkedList, ...nonDefault])];
    updateCheckedList(newList);
  };

  const handleCheckToggle = (value: string) => {
    const opt = options.find((o) => o.value === value);
    if (opt?.defaultVal) return;

    const newList = checkedList.includes(value) ? checkedList.filter((v) => v !== value) : [...checkedList, value];

    updateCheckedList(newList);
  };

  const [searchText, setSearchText] = useState('');
  const filteredOptions = options.filter((opt) => opt.label.toLowerCase().includes(searchText.toLowerCase()));

  return (
    <div className="min-w-[300px] p-2 flex flex-col gap-4 max-h-96 overflow-y-auto">
      <Input placeholder="Search..." allowClear value={searchText} onChange={(e) => setSearchText(e.target.value)} />

      <div className="flex-1 overflow-auto">
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="columns">
            {(provided) => (
              <div ref={provided.innerRef} {...provided.droppableProps} className="flex flex-col gap-2">
                {orderedKeys
                  .filter((key) => filteredOptions.find((opt) => opt.value === key))
                  .map((key, index) => {
                    const opt = options.find((opt) => opt.value === key);
                    if (!opt) return null;

                    return (
                      <Draggable key={key} draggableId={key} index={index}>
                        {(dragProvided) => (
                          <div
                            ref={dragProvided.innerRef}
                            {...dragProvided.draggableProps}
                            className="flex items-center justify-between px-2 py-1 bg-app-white rounded-md"
                          >
                            <Checkbox
                              checked={checkedList.includes(key)}
                              disabled={opt.defaultVal}
                              onChange={() => handleCheckToggle(key)}
                            >
                              {opt.label}
                            </Checkbox>
                            <Tooltip title="Drag to reorder">
                              <div {...dragProvided.dragHandleProps} className="cursor-grab">
                                <MenuOutlined className="text-app-danger" />
                              </div>
                            </Tooltip>
                          </div>
                        )}
                      </Draggable>
                    );
                  })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>

      <Divider className="!m-0 bg-app-gray-light" />

      <div className="flex justify-between gap-2 ">
        <Button type="default" onClick={handleHideAll} style={{ flex: 1 }}>
          Hide All
        </Button>
        <Button type="default" onClick={handleShowAll} style={{ flex: 1 }}>
          Show All
        </Button>
      </div>
    </div>
  );
};

export default memo(TableColumnsDropDown);
