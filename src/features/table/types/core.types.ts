/**
 * Core TypeScript interfaces for the Advanced Table System
 *
 * This file defines all the fundamental types used throughout the table system.
 * Designed for maximum type safety and extensibility.
 */

import { ReactNode } from 'react';

// ============================================================================
// ROW AND DATA TYPES
// ============================================================================

export interface TableRow {
  id: string | number;
  [key: string]: unknown;
}

export type CellValue = string | number | boolean | Date | null | undefined;

// ============================================================================
// COLUMN DEFINITION TYPES
// ============================================================================

export interface BaseColumn<T extends TableRow = TableRow> {
  /** Unique identifier for the column */
  id: string;

  /** Header label displayed to users */
  header: string;

  /** Accessor path (e.g., 'user.name') or function to get cell value */
  accessor: string | ((row: T) => CellValue);

  /** Whether column is visible by default */
  isVisible?: boolean;

  /** Default columns cannot be hidden */
  isDefault?: boolean;

  /** Column width in pixels or CSS units */
  width?: number | string;

  /** Minimum column width */
  minWidth?: number;

  /** Maximum column width */
  maxWidth?: number;

  /** Whether column can be sorted */
  sortable?: boolean;

  /** Whether column can be filtered */
  filterable?: boolean;

  /** Data type for better filtering/sorting */
  dataType?: 'string' | 'number' | 'date' | 'boolean' | 'enum';

  /** Custom cell renderer */
  cellRenderer?: (value: CellValue, row: T) => ReactNode;

  /** Custom header renderer */
  headerRenderer?: (column: BaseColumn<T>) => ReactNode;

  /** Alignment for cell content */
  align?: 'left' | 'center' | 'right';

  /** Whether column should be sticky */
  sticky?: 'left' | 'right';

  /** Optional semantic type for the cell (overrides auto-detect) */
  type?: 'email' | 'phone' | 'address' | 'text';

  /** Whether to show copy affordance (default true) */
  copy?: boolean;

  /** Additional metadata */
  meta?: Record<string, unknown>;
}

export type TableColumn<T extends TableRow = TableRow> = BaseColumn<T>;

// ============================================================================
// ROW ACTION TYPES (for Actions column)
// ============================================================================

export interface RowAction<T extends TableRow = TableRow> {
  /** Unique key for the action */
  key: string;
  /** Label displayed for the action */
  label: ReactNode;
  /** Optional icon displayed before the label */
  icon?: ReactNode;
  /** Whether action is destructive (used for styling) */
  danger?: boolean;
  /** Disable this action */
  disabled?: boolean;
  /** Called when action is invoked */
  onClick?: (row: T) => void;
}

export interface RowActionsColumnOptions {
  /** Column id (default: 'actions') */
  id?: string;
  /** Column header text (default: 'Actions') */
  header?: string;
  /** Column width */
  width?: number | string;
  /** Sticky align (default: 'right') */
  sticky?: 'left' | 'right';
  /** Horizontal align (default: 'center') */
  align?: 'left' | 'center' | 'right';
}

export interface RowActionsConfig<T extends TableRow = TableRow> {
  /**
   * Provide custom actions for a specific row. If provided along with onEdit/onDelete,
   * all actions are combined (defaults first, then custom).
   */
  getActions?: (row: T) => RowAction<T>[];

  /** Default Edit action handler */
  onEdit?: (row: T) => void;
  /** Default Delete action handler */
  onDelete?: (row: T) => void;

  /** Optional column options (id/header/width/sticky/align) */
  column?: RowActionsColumnOptions;

  /** Render mode – currently only 'menu' is supported */
  mode?: 'menu';
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export type FilterOperator =
  | 'equals'
  | 'contains'
  | 'startsWith'
  | 'endsWith'
  | 'in'
  | 'notIn'
  | 'lt'
  | 'lte'
  | 'gt'
  | 'gte'
  | 'between'
  | 'before'
  | 'after'
  | 'isEmpty'
  | 'isNotEmpty';

export interface Filter {
  id: string;
  field: string;
  operator: FilterOperator;
  value: unknown;
  dataType?: 'string' | 'number' | 'date' | 'boolean' | 'enum';
}

// ============================================================================
// SORT TYPES
// ============================================================================

export type SortDirection = 'asc' | 'desc';

export interface SortColumn {
  columnId: string;
  direction: SortDirection;
}

// ============================================================================
// PAGINATION TYPES
// ============================================================================

export interface PaginationState {
  pageIndex: number; // 0-based internally
  pageSize: number;
  totalCount: number;
}

// ============================================================================
// COLUMN MANAGEMENT TYPES
// ============================================================================

export interface ColumnVisibility {
  [columnId: string]: boolean;
}

export interface ColumnOrder {
  orderedIds: string[];
}

export interface ColumnWidths {
  [columnId: string]: number;
}

// ============================================================================
// FEATURE FLAGS
// ============================================================================

export interface FeatureFlags {
  filtering: boolean;
  sorting: boolean;
  pagination: boolean;
  columnReordering: boolean;
  columnResizing: boolean;
  columnVisibility: boolean;
  export: boolean;
  rowSelection: boolean;
  virtualScrolling: boolean;
  mobileResponsive: boolean;
}

// ============================================================================
// URL PARAMETER CONFIGURATION
// ============================================================================

/**
 * Configuration for URL parameter names
 * Only contains core table functionality that should be in URLs
 */
export interface UrlParamConfig {
  /** Page parameter name (default: 'page') */
  page?: string;
  /** Page size parameter name (default: 'pageSize') */
  pageSize?: string;
  /** Sorting parameter name (default: 'sort') */
  sorting?: string;
  /** Custom external parameters (e.g., searchTerm, status, etc.) */
  external?: Record<string, string>;
}

/**
 * External filter definition for integration with external controls
 */
export interface ExternalFilter {
  /** Unique key for this filter */
  key: string;
  /** URL parameter name */
  urlParam: string;
  /** Current value of the filter */
  value: unknown;
  /** Whether this filter should be included in server requests */
  includeInServerRequest?: boolean;
}

/**
 * Type-safe parameter update function
 * Provides strongly typed setters for each external parameter
 */
export type TypedParamUpdaters<T extends UrlParamConfig> = T extends {
  external: infer E;
}
  ? E extends Record<string, string>
    ? {
        [K in keyof E as `set${Capitalize<string & K>}`]: (value: unknown) => void;
      } & {
        /** Update multiple parameters at once */
        updateParams: (params: Partial<ExtractExternalParams<T>>) => void;
      }
    : Record<string, never>
  : Record<string, never>;

// ============================================================================
// MAIN TABLE STATE
// ============================================================================

export interface TableState {
  // Core Filtering
  filters: Filter[];
  externalFilters: ExternalFilter[];

  // Core Table Operations
  sortColumns: SortColumn[];
  pagination: PaginationState;

  // Core UI State
  columnVisibility: ColumnVisibility;
  columnOrder: ColumnOrder;
  columnWidths: ColumnWidths;

  // Configuration
  featureFlags: FeatureFlags;

  // UI Status
  loading: boolean;
  error: string | null;
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

export interface TableConfig<T extends TableRow = TableRow> {
  /** Unique identifier for table instance (for storage keys) */
  tableId: string;

  /** Row key field name (default: 'id') */
  rowKey?: keyof T | ((record: T) => string | number);

  /** Column definitions */
  columns: TableColumn<T>[];

  /** Initial state overrides */
  initialState?: Partial<TableState>;

  /** Feature flags - enable/disable functionality */
  features?: Partial<FeatureFlags>;

  /** URL parameter configuration */
  urlParamConfig?: UrlParamConfig;

  /** External filters from outside controls */
  externalFilters?: ExternalFilter[];

  /** Storage configuration */
  storage?: {
    enableUrlSync?: boolean;
    enableLocalStorage?: boolean;
    storageVersion?: string;
  };

  /** Virtualization settings */
  virtualization?: {
    enabled?: boolean;
    threshold?: number;
    itemHeight?: number;
  };
}

// ============================================================================
// ACTION TYPES FOR STATE MANAGEMENT
// ============================================================================

export type TableAction =
  | { type: 'ADD_FILTER'; payload: Filter }
  | { type: 'REMOVE_FILTER'; payload: string }
  | { type: 'UPDATE_FILTER'; payload: Filter }
  | { type: 'CLEAR_FILTERS' }
  | { type: 'SET_SORT'; payload: SortColumn[] }
  | { type: 'ADD_SORT'; payload: SortColumn }
  | { type: 'REMOVE_SORT'; payload: string }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_PAGE_SIZE'; payload: number }
  | { type: 'SET_PAGINATION'; payload: { pageIndex: number; pageSize: number } }
  | { type: 'SET_TOTAL_COUNT'; payload: number }
  | { type: 'SET_COLUMN_VISIBILITY'; payload: ColumnVisibility }
  | { type: 'TOGGLE_COLUMN_VISIBILITY'; payload: string }
  | { type: 'SET_COLUMN_ORDER'; payload: string[] }
  | { type: 'SET_COLUMN_WIDTH'; payload: { columnId: string; width: number } }
  | { type: 'RESET_COLUMNS' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET_STATE' }
  | { type: 'HYDRATE_STATE'; payload: Partial<TableState> };

// ============================================================================
// EVENT HANDLER TYPES
// ============================================================================

export interface TableEventHandlers<T extends TableRow = TableRow> {
  onStateChange?: (state: TableState) => void;
  onFiltersChange?: (filters: Filter[]) => void;
  onSortChange?: (sortColumns: SortColumn[]) => void;
  onPaginationChange?: (pagination: PaginationState) => void;
  onColumnVisibilityChange?: (visibility: ColumnVisibility) => void;
  onColumnOrderChange?: (order: string[]) => void;
  onColumnWidthChange?: (widths: ColumnWidths) => void;
  onRowClick?: (row: T) => void;
  onRowSelect?: (selectedRows: T[]) => void;
  onExport?: (data: T[]) => void;
}

// ============================================================================
// URL SERIALIZATION TYPES
// ============================================================================

export interface UrlParams {
  f?: string; // filters (compressed)
  sort?: string; // sorting
  page?: string; // 1-based for UX
  size?: string; // page size
  cols?: string; // visible columns
  order?: string; // column order
  widths?: string; // column widths
}

// ============================================================================
// API DATA FETCH TYPES
// ============================================================================

/**
 * Standardized parameters for server-side data fetching
 * These are static parameters that all tables use
 */
export interface DataFetchParams {
  /** Current page (0-based internally, but may be 1-based in API) */
  page: number;
  /** Number of items per page */
  pageSize: number;
  /** Sorting configuration */
  sorting: Array<{ field: string; direction: 'asc' | 'desc' }>;
  /** External filters (search, status, etc.) spread as direct properties */
  [key: string]: unknown;
}

/**
 * Typed external filter configuration for specific table implementations
 * Use this to create type-safe external filters for your tables
 */
export interface TypedExternalFilters {
  /** Search term filter */
  search?: string;
  /** Status filter (active/inactive) */
  status?: boolean;
  /** Add more external filters as needed */
  [key: string]: unknown;
}

// ============================================================================
// ADVANCED TYPE UTILITIES FOR TYPE-SAFE EXTERNAL FILTERS
// ============================================================================

/**
 * Extract external parameter keys from UrlParamConfig
 * This creates a type that includes only the keys defined in urlParamConfig.external
 */
export type ExtractExternalParams<T extends UrlParamConfig> = T extends {
  external: infer E;
}
  ? E extends Record<string, string>
    ? {
        [K in keyof E]?: unknown;
      }
    : Record<string, never>
  : Record<string, never>;

/**
 * Create typed DataFetchParams based on urlParamConfig
 * This combines the base DataFetchParams with the external parameters
 */
export type TypedDataFetchParams<T extends UrlParamConfig> = DataFetchParams & ExtractExternalParams<T>;

/**
 * Helper type to create strongly typed parameter change handler
 * Usage: onParamsChange?: TypedOnParamsChange<typeof urlParamConfig>
 */
export type TypedOnParamsChange<T extends UrlParamConfig> = (params: TypedDataFetchParams<T>) => Promise<void> | void;

/**
 * @deprecated Use TypedOnParamsChange instead
 */
export type TypedOnDataFetch<T extends UrlParamConfig> = TypedOnParamsChange<T>;

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type TableHookReturn<T extends TableRow = TableRow> = {
  // State
  state: TableState;

  // Data (filtered, sorted, paginated)
  data: T[];
  filteredData: T[];

  // Actions
  actions: {
    addFilter: (filter: Filter) => void;
    removeFilter: (filterId: string) => void;
    updateFilter: (filter: Filter) => void;
    clearFilters: () => void;
    setSorting: (sortColumns: SortColumn[]) => void;
    addSort: (sort: SortColumn) => void;
    removeSort: (columnId: string) => void;
    setPage: (page: number) => void;
    setPageSize: (size: number) => void;
    setTotalCount: (count: number) => void;
    setColumnVisibility: (visibility: ColumnVisibility) => void;
    toggleColumnVisibility: (columnId: string) => void;
    setColumnOrder: (order: string[]) => void;
    setColumnWidth: (columnId: string, width: number) => void;
    resetColumns: () => void;
    reset: () => void;
  };

  // Computed properties
  visibleColumns: TableColumn<T>[];
  totalPages: number;
  canGoNext: boolean;
  canGoPrev: boolean;

  // Meta information
  isLoading: boolean;
  error: string | null;
};

export default TableState;
