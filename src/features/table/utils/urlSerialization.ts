/**
 * URL parameter serialization utilities for table state
 *
 * Handles core table functionality URL parameters only:
 * - page, pageSize (pagination)
 * - sort (sorting)
 *
 * Visual state (columnWidths, columnVisibility) and business logic
 * (search, filters) are excluded from URL and handled separately.
 */

import { ExternalFilter, TableState, UrlParamConfig } from '../types/core.types';

/**
 * Core table URL parameter keys
 * These represent the essential table functionality that should be in URL
 */
const CORE_TABLE_PARAMS = {
  PAGE: 'page',
  PAGE_SIZE: 'pageSize',
  SORT: 'sort',
} as const;

/**
 * Registry for tracking which parameters are currently active based on features
 */
class URLParamRegistry {
  private activeParams = new Set<string>();

  register(paramKey: string): void {
    this.activeParams.add(paramKey);
  }

  unregister(paramKey: string): void {
    this.activeParams.delete(paramKey);
  }

  isActive(paramKey: string): boolean {
    return this.activeParams.has(paramKey);
  }

  getActiveParams(): string[] {
    return Array.from(this.activeParams);
  }

  clear(): void {
    this.activeParams.clear();
  }
}

// Global registry instance
export const urlParamRegistry = new URLParamRegistry();

/**
 * Pre-register external URL parameters to prevent dynamic registration during renders
 * This should be called early in the component lifecycle to establish the complete URL structure
 */
export function initializeUrlParams(
  features: { pagination?: boolean; sorting?: boolean } = {},
  urlConfig: UrlParamConfig = {}
): void {
  // Register core table parameters if features are enabled
  if (features.pagination) {
    const pageParam = urlConfig.page || CORE_TABLE_PARAMS.PAGE;
    const pageSizeParam = urlConfig.pageSize || CORE_TABLE_PARAMS.PAGE_SIZE;
    urlParamRegistry.register(pageParam);
    urlParamRegistry.register(pageSizeParam);
  }

  if (features.sorting) {
    const sortParam = urlConfig.sorting || CORE_TABLE_PARAMS.SORT;
    urlParamRegistry.register(sortParam);
  }

  // Register external parameters (business logic URL params)
  if (urlConfig.external) {
    Object.values(urlConfig.external).forEach((paramKey) => {
      if (paramKey) {
        urlParamRegistry.register(paramKey);
      }
    });
  }
}

/**
 * Converts table state to URL search parameters
 * Only includes core table functionality: pagination and sorting
 * Features are checked to determine if parameters should be included
 */
export function stateToUrlParams(
  state: Partial<TableState>,
  features: { pagination?: boolean; sorting?: boolean } = {},
  urlConfig: UrlParamConfig = {}
): URLSearchParams {
  const params = new URLSearchParams();

  // Get parameter names (use custom config or defaults)
  const pageParam = urlConfig.page || CORE_TABLE_PARAMS.PAGE;
  const pageSizeParam = urlConfig.pageSize || CORE_TABLE_PARAMS.PAGE_SIZE;
  const sortParam = urlConfig.sorting || CORE_TABLE_PARAMS.SORT;

  // Include pagination only if feature is enabled
  if (features.pagination && state.pagination) {
    // Always include page (convert 0-based to 1-based for URL)
    params.set(pageParam, (state.pagination.pageIndex + 1).toString());

    // Always include pageSize
    params.set(pageSizeParam, state.pagination.pageSize.toString());
  }

  // Include sorting only if feature is enabled and there are active sorts
  if (features.sorting && state.sortColumns && state.sortColumns.length > 0) {
    const sortString = state.sortColumns.map((sort) => `${sort.columnId}:${sort.direction}`).join(',');
    params.set(sortParam, sortString);
  }

  // Include external filters in URL if they have values
  if (state.externalFilters && urlConfig.external) {
    state.externalFilters.forEach((filter) => {
      // Find the URL parameter name for this filter
      const urlParamKey = Object.entries(urlConfig.external || {}).find(
        ([internalKey]) => internalKey === filter.key
      )?.[1];

      // Only add to URL if the filter has a value and a URL parameter mapping
      if (urlParamKey && filter.value != null && filter.value !== '') {
        params.set(urlParamKey, String(filter.value));
      }
    });
  }

  return params;
}

/**
 * Converts URL search parameters to table state
 * Only handles core table functionality: pagination and sorting
 * Features are checked to determine which parameters to process
 */
export function urlParamsToState(
  params: URLSearchParams,
  features: { pagination?: boolean; sorting?: boolean } = {},
  urlConfig: UrlParamConfig = {}
): Partial<TableState> {
  const state: Partial<TableState> = {};

  // Get parameter names (use custom config or defaults)
  const pageParam = urlConfig.page || CORE_TABLE_PARAMS.PAGE;
  const pageSizeParam = urlConfig.pageSize || CORE_TABLE_PARAMS.PAGE_SIZE;
  const sortParam = urlConfig.sorting || CORE_TABLE_PARAMS.SORT;

  // Process pagination if feature is enabled
  if (features.pagination) {
    const pageSizeStr = params.get(pageSizeParam);
    const pageStr = params.get(pageParam);

    if (pageSizeStr) {
      const pageSize = parseInt(pageSizeStr, 10);
      if (!isNaN(pageSize) && pageSize > 0) {
        state.pagination = {
          pageIndex: state.pagination?.pageIndex ?? 0,
          pageSize,
          totalCount: state.pagination?.totalCount ?? 0,
        };
      }
    }

    if (pageStr) {
      const pageIndex = parseInt(pageStr, 10) - 1; // Convert 1-based to 0-based
      if (!isNaN(pageIndex) && pageIndex >= 0) {
        state.pagination = {
          pageIndex,
          pageSize: state.pagination?.pageSize ?? 25,
          totalCount: state.pagination?.totalCount ?? 0,
        };
      }
    }
  }

  // Process sorting if feature is enabled
  if (features.sorting) {
    const sortParam_value = params.get(sortParam);
    if (sortParam_value) {
      state.sortColumns = sortParam_value.split(',').map((sortString) => {
        const [columnId, direction] = sortString.split(':');
        return {
          columnId,
          direction: direction as 'asc' | 'desc',
        };
      });
    }
  }

  // Process external filters from URL parameters
  if (urlConfig.external) {
    const externalFilters: ExternalFilter[] = [];

    Object.entries(urlConfig.external).forEach(([internalKey, urlParamKey]) => {
      const value = params.get(urlParamKey);

      // Only create filter if there's a value in the URL
      if (value != null && value !== '') {
        // Try to convert to appropriate type
        let parsedValue: unknown = value;

        // Handle boolean values
        if (value === 'true') parsedValue = true;
        else if (value === 'false') parsedValue = false;
        // Handle numbers
        else if (!isNaN(Number(value)) && value !== '') parsedValue = Number(value);

        externalFilters.push({
          key: internalKey,
          urlParam: urlParamKey,
          value: parsedValue,
          includeInServerRequest: true,
        });
      }
    });

    if (externalFilters.length > 0) {
      state.externalFilters = externalFilters;
    }
  }

  return state;
}

/**
 * Gets the current URL parameters that should be preserved during navigation
 */
/**
 * Preserves core table parameters from URL search params
 * Only preserves pagination and sorting parameters that are currently active
 */
export function preserveTableParams(searchParams: URLSearchParams): URLSearchParams {
  const preservedParams = new URLSearchParams();

  // Preserve core table parameters that are active in the registry
  Object.values(CORE_TABLE_PARAMS).forEach((paramKey) => {
    if (urlParamRegistry.isActive(paramKey)) {
      const value = searchParams.get(paramKey);
      if (value) {
        preservedParams.set(paramKey, value);
      }
    }
  });

  return preservedParams;
}

/**
 * Removes default values from URL parameters to keep URLs clean
 * Only applies to core table parameters
 */
export function cleanUrlParams(
  searchParams: URLSearchParams,
  features: { pagination?: boolean; sorting?: boolean } = {},
  urlConfig: UrlParamConfig = {}
): URLSearchParams {
  const cleanParams = new URLSearchParams();

  // Get parameter names (use custom config or defaults)
  const pageParam = urlConfig.page || CORE_TABLE_PARAMS.PAGE;
  const pageSizeParam = urlConfig.pageSize || CORE_TABLE_PARAMS.PAGE_SIZE;
  const sortParam = urlConfig.sorting || CORE_TABLE_PARAMS.SORT;

  // Include pagination parameters if feature is enabled
  if (features.pagination) {
    const pageSize = searchParams.get(pageSizeParam) || '25';
    const page = searchParams.get(pageParam) || '1';

    // Always include pagination parameters when feature is enabled
    cleanParams.set(pageSizeParam, pageSize);
    cleanParams.set(pageParam, page);
  }

  // Include sorting if feature is enabled and there are sort values
  if (features.sorting) {
    const sort = searchParams.get(sortParam);
    if (sort) {
      cleanParams.set(sortParam, sort);
    }
  }

  return cleanParams;
}

/**
 * Checks if a parameter key is a core table parameter
 */
export function isCoreTableParam(paramKey: string): boolean {
  return (Object.values(CORE_TABLE_PARAMS) as string[]).includes(paramKey);
}

/**
 * Gets all core table parameter keys that are currently active
 */
export function getActiveCoreParams(): string[] {
  return Object.values(CORE_TABLE_PARAMS).filter((param) => urlParamRegistry.isActive(param));
}
