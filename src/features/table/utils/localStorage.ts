/**
 * Local Storage utilities for table state persistence
 *
 * Handles versioned storage with migration support and type safety.
 */

import { ColumnOrder, ColumnVisibility, TableState } from '../types/core.types';

interface StorageItem<T> {
  version: string;
  timestamp: number;
  data: T;
}

interface TableStorageData {
  columnVisibility?: ColumnVisibility;
  columnOrder?: ColumnOrder;
  columnWidths?: Record<string, number>;
  pageSize?: number;
  lastUsedFilters?: TableState['filters'];
  featureFlags?: TableState['featureFlags'];
}

/**
 * Current storage version for migration purposes
 */
const CURRENT_STORAGE_VERSION = '1.0.0';

/**
 * Storage key prefixes for different table data
 */
const STORAGE_KEYS = {
  TABLE_STATE: 'table.v1',
  COLUMN_VISIBILITY: 'table.v1.columnVisibility',
  COLUMN_ORDER: 'table.v1.columnOrder',
  COLUMN_WIDTHS: 'table.v1.columnWidths',
  PAGE_SIZE: 'table.v1.pageSize',
  LAST_FILTERS: 'table.v1.lastUsedFilters',
  FEATURE_FLAGS: 'table.v1.featureFlags',
} as const;

/**
 * Creates a namespaced storage key for a specific table instance
 */
function createStorageKey(tableId: string, keyType: keyof typeof STORAGE_KEYS): string {
  return `${STORAGE_KEYS[keyType]}.${tableId}`;
}

/**
 * Safely writes data to localStorage with error handling
 */
function safeLocalStorageWrite<T>(key: string, data: T, version: string = CURRENT_STORAGE_VERSION): boolean {
  try {
    const storageItem: StorageItem<T> = {
      version,
      timestamp: Date.now(),
      data,
    };
    localStorage.setItem(key, JSON.stringify(storageItem));
    return true;
  } catch (error) {
    console.warn(`Failed to write to localStorage for key: ${key}`, error);
    return false;
  }
}

/**
 * Safely reads data from localStorage with error handling and validation
 */
function safeLocalStorageRead<T>(key: string): T | null {
  try {
    const stored = localStorage.getItem(key);
    if (!stored) return null;

    const storageItem: StorageItem<T> = JSON.parse(stored);

    // Basic validation
    if (!storageItem || typeof storageItem !== 'object') {
      console.warn(`Invalid storage format for key: ${key}`);
      return null;
    }

    if (!storageItem.version || !storageItem.data) {
      console.warn(`Missing required fields in storage for key: ${key}`);
      return null;
    }

    // Version migration would go here if needed
    if (storageItem.version !== CURRENT_STORAGE_VERSION) {
      console.info(
        `Storage version mismatch for key: ${key}. Current: ${CURRENT_STORAGE_VERSION}, Stored: ${storageItem.version}`
      );
      // For now, we'll accept older versions, but this is where migration logic would go
    }

    return storageItem.data;
  } catch (error) {
    console.warn(`Failed to read from localStorage for key: ${key}`, error);
    return null;
  }
}

/**
 * Removes an item from localStorage safely
 */
function safeLocalStorageRemove(key: string): boolean {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.warn(`Failed to remove from localStorage for key: ${key}`, error);
    return false;
  }
}

/**
 * Stores column visibility state
 */
export function storeColumnVisibility(tableId: string, visibility: ColumnVisibility): boolean {
  const key = createStorageKey(tableId, 'COLUMN_VISIBILITY');
  return safeLocalStorageWrite(key, visibility);
}

/**
 * Retrieves column visibility state
 */
export function getStoredColumnVisibility(tableId: string): ColumnVisibility | null {
  const key = createStorageKey(tableId, 'COLUMN_VISIBILITY');
  return safeLocalStorageRead<ColumnVisibility>(key);
}

/**
 * Stores column order state
 */
export function storeColumnOrder(tableId: string, order: ColumnOrder): boolean {
  const key = createStorageKey(tableId, 'COLUMN_ORDER');
  return safeLocalStorageWrite(key, order);
}

/**
 * Retrieves column order state
 */
export function getStoredColumnOrder(tableId: string): ColumnOrder | null {
  const key = createStorageKey(tableId, 'COLUMN_ORDER');
  return safeLocalStorageRead<ColumnOrder>(key);
}

/**
 * Stores column widths state
 */
export function storeColumnWidths(tableId: string, widths: Record<string, number>): boolean {
  const key = createStorageKey(tableId, 'COLUMN_WIDTHS');
  return safeLocalStorageWrite(key, widths);
}

/**
 * Retrieves column widths state
 */
export function getStoredColumnWidths(tableId: string): Record<string, number> | null {
  const key = createStorageKey(tableId, 'COLUMN_WIDTHS');
  return safeLocalStorageRead<Record<string, number>>(key);
}

/**
 * Stores preferred page size
 */
export function storePageSize(tableId: string, pageSize: number): boolean {
  const key = createStorageKey(tableId, 'PAGE_SIZE');
  return safeLocalStorageWrite(key, pageSize);
}

/**
 * Retrieves preferred page size
 */
export function getStoredPageSize(tableId: string): number | null {
  const key = createStorageKey(tableId, 'PAGE_SIZE');
  return safeLocalStorageRead<number>(key);
}

/**
 * Stores last used filters for quick access
 */
export function storeLastUsedFilters(tableId: string, filters: TableState['filters']): boolean {
  const key = createStorageKey(tableId, 'LAST_FILTERS');
  return safeLocalStorageWrite(key, filters);
}

/**
 * Retrieves last used filters
 */
export function getStoredLastUsedFilters(tableId: string): TableState['filters'] | null {
  const key = createStorageKey(tableId, 'LAST_FILTERS');
  return safeLocalStorageRead<TableState['filters']>(key);
}

/**
 * Stores feature flags preferences
 */
export function storeFeatureFlags(tableId: string, featureFlags: TableState['featureFlags']): boolean {
  const key = createStorageKey(tableId, 'FEATURE_FLAGS');
  return safeLocalStorageWrite(key, featureFlags);
}

/**
 * Retrieves feature flags preferences
 */
export function getStoredFeatureFlags(tableId: string): TableState['featureFlags'] | null {
  const key = createStorageKey(tableId, 'FEATURE_FLAGS');
  return safeLocalStorageRead<TableState['featureFlags']>(key);
}

/**
 * Stores complete table state (bulk operation)
 */
export function storeTableState(tableId: string, state: Partial<TableState>): boolean {
  const key = createStorageKey(tableId, 'TABLE_STATE');

  // Extract only the persistable parts of the state
  const persistableState: TableStorageData = {
    columnVisibility: state.columnVisibility,
    columnOrder: state.columnOrder,
    columnWidths: state.columnWidths,
    pageSize: state.pagination?.pageSize,
    lastUsedFilters: state.filters,
    featureFlags: state.featureFlags,
  };

  return safeLocalStorageWrite(key, persistableState);
}

/**
 * Retrieves complete table state (bulk operation)
 */
export function getStoredTableState(tableId: string): Partial<TableState> | null {
  const key = createStorageKey(tableId, 'TABLE_STATE');
  const stored = safeLocalStorageRead<TableStorageData>(key);

  if (!stored) return null;

  // Convert back to partial table state format
  const state: Partial<TableState> = {};

  if (stored.columnVisibility) {
    state.columnVisibility = stored.columnVisibility;
  }

  if (stored.columnOrder) {
    state.columnOrder = stored.columnOrder;
  }

  if (stored.columnWidths) {
    state.columnWidths = stored.columnWidths;
  }

  if (stored.pageSize) {
    state.pagination = {
      pageSize: stored.pageSize,
      pageIndex: 0, // Always reset page index
      totalCount: 0,
    };
  }

  if (stored.lastUsedFilters) {
    state.filters = stored.lastUsedFilters;
  }

  if (stored.featureFlags) {
    state.featureFlags = stored.featureFlags;
  }

  return state;
}

/**
 * Clears all stored data for a specific table
 */
export function clearTableStorage(tableId: string): boolean {
  const keys = Object.values(STORAGE_KEYS).map((keyType) =>
    createStorageKey(tableId, keyType as keyof typeof STORAGE_KEYS)
  );

  let success = true;
  keys.forEach((key) => {
    const removed = safeLocalStorageRemove(key);
    if (!removed) success = false;
  });

  return success;
}

/**
 * Gets storage information for debugging
 */
export function getStorageInfo(tableId: string): {
  keys: string[];
  totalSize: number;
  itemCount: number;
  lastModified: number | null;
} {
  const keys = Object.values(STORAGE_KEYS).map((keyType) =>
    createStorageKey(tableId, keyType as keyof typeof STORAGE_KEYS)
  );

  let totalSize = 0;
  let itemCount = 0;
  let lastModified: number | null = null;

  keys.forEach((key) => {
    try {
      const item = localStorage.getItem(key);
      if (item) {
        totalSize += item.length;
        itemCount++;

        // Try to parse and get timestamp
        try {
          const parsed = JSON.parse(item) as StorageItem<unknown>;
          if (parsed.timestamp && (!lastModified || parsed.timestamp > lastModified)) {
            lastModified = parsed.timestamp;
          }
        } catch {
          // Ignore parse errors for timestamp extraction
        }
      }
    } catch {
      // Ignore access errors
    }
  });

  return {
    keys,
    totalSize,
    itemCount,
    lastModified,
  };
}

/**
 * Migrates old storage format to new format (if needed)
 */
export function migrateStorage(
  tableId: string,
  fromVersion: string,
  toVersion: string = CURRENT_STORAGE_VERSION
): boolean {
  console.info(`Migrating storage for table ${tableId} from ${fromVersion} to ${toVersion}`);

  // For now, we don't have any migrations, but this is where they would go
  // Example migration logic:
  /*
  if (fromVersion === '0.9.0' && toVersion === '1.0.0') {
    // Migration logic here
    const oldData = getOldFormatData(tableId);
    const newData = transformToNewFormat(oldData);
    storeTableState(tableId, newData);
    clearOldFormatData(tableId);
    return true;
  }
  */

  return true;
}

/**
 * Checks if localStorage is available and functional
 */
export function isStorageAvailable(): boolean {
  try {
    const testKey = '__storage_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch {
    return false;
  }
}

/**
 * Gets available storage space (estimate)
 */
export function getStorageQuota(): { used: number; available: number; total: number } | null {
  if (!isStorageAvailable()) return null;

  try {
    // This is an estimate since we can't get exact localStorage quota
    let used = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        if (value) {
          used += key.length + value.length;
        }
      }
    }

    // Most browsers have 5-10MB limit for localStorage
    const estimatedTotal = 5 * 1024 * 1024; // 5MB
    const available = Math.max(0, estimatedTotal - used);

    return {
      used,
      available,
      total: estimatedTotal,
    };
  } catch {
    return null;
  }
}
