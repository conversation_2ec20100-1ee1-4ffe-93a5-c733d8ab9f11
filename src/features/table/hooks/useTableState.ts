import { useCallback, useMemo, useReducer } from 'react';
import { TABLE_DEFAULTS } from '../index';
import type {
  ColumnVisibility,
  Filter,
  SortColumn,
  TableAction,
  TableColumn,
  TableRow,
  TableState,
} from '../types/core.types';

/**
 * Default initial state for the table
 */
const createInitialState = (): TableState => ({
  filters: [],
  externalFilters: [],
  sortColumns: [],
  pagination: {
    pageIndex: TABLE_DEFAULTS.PAGE_INDEX,
    pageSize: TABLE_DEFAULTS.PAGE_SIZE,
    totalCount: 0,
  },
  columnVisibility: {},
  columnOrder: { orderedIds: [] },
  columnWidths: {},
  featureFlags: {
    filtering: true,
    sorting: true,
    pagination: true,
    columnReordering: true,
    columnResizing: true,
    columnVisibility: true,
    export: true,
    rowSelection: false,
    virtualScrolling: false,
    mobileResponsive: true,
  },
  loading: false,
  error: null,
});

/**
 * Table state reducer with all actions
 */
function tableReducer(state: TableState, action: TableAction): TableState {
  switch (action.type) {
    case 'ADD_FILTER':
      return {
        ...state,
        filters: [...state.filters, action.payload],
        pagination: { ...state.pagination, pageIndex: 0 },
      };

    case 'REMOVE_FILTER':
      return {
        ...state,
        filters: state.filters.filter((filter) => filter.id !== action.payload),
        pagination: { ...state.pagination, pageIndex: 0 },
      };

    case 'UPDATE_FILTER': {
      const updatedFilters = state.filters.map((filter) => (filter.id === action.payload.id ? action.payload : filter));
      return {
        ...state,
        filters: updatedFilters,
        pagination: { ...state.pagination, pageIndex: 0 },
      };
    }

    case 'CLEAR_FILTERS':
      return {
        ...state,
        filters: [],
        pagination: { ...state.pagination, pageIndex: 0 },
      };

    case 'SET_SORT':
      return {
        ...state,
        sortColumns: action.payload,
      };

    case 'ADD_SORT': {
      const existingIndex = state.sortColumns.findIndex((sort) => sort.columnId === action.payload.columnId);

      let newSortColumns: SortColumn[];
      if (existingIndex >= 0) {
        // Update existing sort
        newSortColumns = [...state.sortColumns];
        newSortColumns[existingIndex] = action.payload;
      } else {
        // Add new sort
        newSortColumns = [...state.sortColumns, action.payload];
      }

      return {
        ...state,
        sortColumns: newSortColumns,
      };
    }

    case 'REMOVE_SORT':
      return {
        ...state,
        sortColumns: state.sortColumns.filter((sort) => sort.columnId !== action.payload),
      };

    case 'SET_PAGE':
      return {
        ...state,
        pagination: { ...state.pagination, pageIndex: action.payload },
      };

    case 'SET_PAGE_SIZE':
      return {
        ...state,
        pagination: {
          ...state.pagination,
          pageSize: action.payload,
          pageIndex: 0, // Reset to first page when changing page size
        },
      };

    case 'SET_PAGINATION':
      return {
        ...state,
        pagination: {
          ...state.pagination,
          pageIndex: action.payload.pageIndex,
          pageSize: action.payload.pageSize,
        },
      };

    case 'SET_TOTAL_COUNT':
      return {
        ...state,
        pagination: { ...state.pagination, totalCount: action.payload },
      };

    case 'SET_COLUMN_VISIBILITY':
      return {
        ...state,
        columnVisibility: action.payload,
      };

    case 'TOGGLE_COLUMN_VISIBILITY': {
      const currentVisibility = state.columnVisibility[action.payload] ?? true;
      return {
        ...state,
        columnVisibility: {
          ...state.columnVisibility,
          [action.payload]: !currentVisibility,
        },
      };
    }

    case 'SET_COLUMN_ORDER':
      return {
        ...state,
        columnOrder: { orderedIds: action.payload },
      };

    case 'SET_COLUMN_WIDTH':
      return {
        ...state,
        columnWidths: {
          ...state.columnWidths,
          [action.payload.columnId]: action.payload.width,
        },
      };

    case 'RESET_COLUMNS': {
      return {
        ...state,
        columnVisibility: {},
        columnOrder: { orderedIds: [] },
        columnWidths: {},
      };
    }

    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case 'RESET_STATE':
      return createInitialState();

    case 'HYDRATE_STATE':
      return {
        ...state,
        ...action.payload,
      };

    default:
      return state;
  }
}

/**
 * Hook for managing table state with reducer pattern
 */
export function useTableState<T extends TableRow = TableRow>(
  columns: TableColumn<T>[],
  initialState?: Partial<TableState>
) {
  // Initialize state with defaults and overrides
  const fullInitialState = useMemo(() => {
    const defaultState = createInitialState();

    // Initialize column visibility and order from column definitions
    const columnVisibility: ColumnVisibility = {};
    const orderedIds: string[] = [];

    columns.forEach((column) => {
      columnVisibility[column.id] = column.isVisible ?? true;
      orderedIds.push(column.id);
    });

    return {
      ...defaultState,
      columnVisibility,
      columnOrder: { orderedIds },
      ...initialState,
    };
  }, [columns, initialState]);

  const [state, dispatch] = useReducer(tableReducer, fullInitialState);

  // Action creators with useCallback for performance

  const addFilter = useCallback((filter: Filter) => {
    dispatch({ type: 'ADD_FILTER', payload: filter });
  }, []);

  const removeFilter = useCallback((filterId: string) => {
    dispatch({ type: 'REMOVE_FILTER', payload: filterId });
  }, []);

  const updateFilter = useCallback((filter: Filter) => {
    dispatch({ type: 'UPDATE_FILTER', payload: filter });
  }, []);

  const clearFilters = useCallback(() => {
    dispatch({ type: 'CLEAR_FILTERS' });
  }, []);

  const setSorting = useCallback((sortColumns: SortColumn[]) => {
    dispatch({ type: 'SET_SORT', payload: sortColumns });
  }, []);

  const addSort = useCallback((sort: SortColumn) => {
    dispatch({ type: 'ADD_SORT', payload: sort });
  }, []);

  const removeSort = useCallback((columnId: string) => {
    dispatch({ type: 'REMOVE_SORT', payload: columnId });
  }, []);

  const setPage = useCallback((page: number) => {
    dispatch({ type: 'SET_PAGE', payload: page });
  }, []);

  const setPageSize = useCallback((size: number) => {
    dispatch({ type: 'SET_PAGE_SIZE', payload: size });
  }, []);

  const setPagination = useCallback((pageIndex: number, pageSize: number) => {
    dispatch({ type: 'SET_PAGINATION', payload: { pageIndex, pageSize } });
  }, []);

  const setTotalCount = useCallback((count: number) => {
    dispatch({ type: 'SET_TOTAL_COUNT', payload: count });
  }, []);

  const setColumnVisibility = useCallback((visibility: ColumnVisibility) => {
    dispatch({ type: 'SET_COLUMN_VISIBILITY', payload: visibility });
  }, []);

  const toggleColumnVisibility = useCallback((columnId: string) => {
    dispatch({ type: 'TOGGLE_COLUMN_VISIBILITY', payload: columnId });
  }, []);

  const setColumnOrder = useCallback((order: string[]) => {
    dispatch({ type: 'SET_COLUMN_ORDER', payload: order });
  }, []);

  const setColumnWidth = useCallback((columnId: string, width: number) => {
    dispatch({ type: 'SET_COLUMN_WIDTH', payload: { columnId, width } });
  }, []);

  const resetColumns = useCallback(() => {
    dispatch({ type: 'RESET_COLUMNS' });
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const reset = useCallback(() => {
    dispatch({ type: 'RESET_STATE' });
  }, []);

  const hydrateState = useCallback((newState: Partial<TableState>) => {
    dispatch({ type: 'HYDRATE_STATE', payload: newState });
  }, []);

  // Combine all actions
  const actions = useMemo(
    () => ({
      addFilter,
      removeFilter,
      updateFilter,
      clearFilters,
      setSorting,
      addSort,
      removeSort,
      setPage,
      setPageSize,
      setPagination,
      setTotalCount,
      setColumnVisibility,
      toggleColumnVisibility,
      setColumnOrder,
      setColumnWidth,
      resetColumns,
      setLoading,
      setError,
      reset,
      hydrateState,
    }),
    [
      addFilter,
      removeFilter,
      updateFilter,
      clearFilters,
      setSorting,
      addSort,
      removeSort,
      setPage,
      setPageSize,
      setPagination,
      setTotalCount,
      setColumnVisibility,
      toggleColumnVisibility,
      setColumnOrder,
      setColumnWidth,
      resetColumns,
      setLoading,
      setError,
      reset,
      hydrateState,
    ]
  );

  // Computed values
  const visibleColumns = useMemo(() => {
    // Get columns in the correct order
    const orderedColumns = state.columnOrder.orderedIds
      .map((id) => columns.find((col) => col.id === id))
      .filter((col): col is TableColumn<T> => Boolean(col));

    // Add any new columns that aren't in the order yet
    const existingIds = new Set(state.columnOrder.orderedIds);
    const newColumns = columns.filter((col) => !existingIds.has(col.id));
    const allOrderedColumns = [...orderedColumns, ...newColumns];

    // Filter by visibility
    return allOrderedColumns.filter((column) => {
      // Default columns are always visible
      if (column.isDefault) return true;

      // Check explicit visibility setting, default to column definition
      const isVisible = state.columnVisibility[column.id];
      return isVisible !== undefined ? isVisible : (column.isVisible ?? true);
    });
  }, [columns, state.columnVisibility, state.columnOrder.orderedIds]);

  const totalPages = useMemo(() => {
    return Math.ceil(state.pagination.totalCount / state.pagination.pageSize);
  }, [state.pagination.totalCount, state.pagination.pageSize]);

  const canGoNext = useMemo(() => {
    return state.pagination.pageIndex < totalPages - 1;
  }, [state.pagination.pageIndex, totalPages]);

  const canGoPrev = useMemo(() => {
    return state.pagination.pageIndex > 0;
  }, [state.pagination.pageIndex]);

  return {
    state,
    actions,
    visibleColumns,
    totalPages,
    canGoNext,
    canGoPrev,
  };
}

export default useTableState;
