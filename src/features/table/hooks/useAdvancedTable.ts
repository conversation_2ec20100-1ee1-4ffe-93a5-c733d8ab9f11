/**
 * Advanced table hook that combines state management, URL synchronization, and localStorage persistence
 *
 * This is the main hook that provides a complete table solution with:
 * - Centralized state management
 * - URL parameter synchronization
 * - LocalStorage persistence
 * - Performance optimizations
 */

import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import type {
  BaseColumn,
  Filter,
  PaginationState,
  SortColumn,
  TableConfig,
  TableRow,
  TableState,
} from '../types/core.types';
import { getStoredTableState, isStorageAvailable, storeTableState } from '../utils/localStorage';
import { initializeUrlParams, stateToUrlParams, urlParamRegistry, urlParamsToState } from '../utils/urlSerialization';
import useTableState from './useTableState';

export interface UseAdvancedTableOptions<T extends TableRow> {
  /**
   * Unique identifier for this table instance (used for localStorage)
   */
  tableId: string;

  /**
   * Column definitions
   */
  columns: BaseColumn<T>[];

  /**
   * Initial configuration
   */
  config?: Partial<TableConfig<T>>;

  /**
   * Whether to sync state with URL parameters
   * @default true
   */
  enableUrlSync?: boolean;

  /**
   * Whether to persist state in localStorage
   * @default true
   */
  enablePersistence?: boolean;

  /**
   * Whether to restore state from URL on mount
   * @default true
   */
  restoreFromUrl?: boolean;

  /**
   * Whether to restore state from localStorage on mount
   * @default true
   */
  restoreFromStorage?: boolean;

  /**
   * Debounce delay for URL updates (ms)
   * @default 300
   */
  urlUpdateDelay?: number;

  /**
   * Debounce delay for localStorage updates (ms)
   * @default 1000
   */
  storageUpdateDelay?: number;

  /**
   * Callback when state changes
   */
  onStateChange?: (state: TableState) => void;

  /**
   * Callback when URL sync occurs
   */
  onUrlSync?: (params: URLSearchParams) => void;

  /**
   * Callback when localStorage sync occurs
   */
  onStorageSync?: (data: Partial<TableState>) => void;
}

export interface UseAdvancedTableReturn<T extends TableRow> {
  // State
  state: TableState;

  // Column management
  visibleColumns: BaseColumn<T>[];
  allColumns: BaseColumn<T>[];

  // Data management
  filteredData: T[];
  sortedData: T[];
  paginatedData: T[];

  // Computed values
  totalFilteredCount: number;
  hasActiveFilters: boolean;
  hasActiveSorting: boolean;
  hasCustomColumnOrder: boolean;
  hasCustomColumnWidths: boolean;

  // Actions
  actions: ReturnType<typeof useTableState>['actions'];

  // URL & Storage
  urlParams: URLSearchParams;
  isUrlSynced: boolean;
  isStorageSynced: boolean;

  // Utilities
  resetTable: () => void;
  exportState: () => Partial<TableState>;
  importState: (state: Partial<TableState>) => void;
  clearUrlParams: () => void;
  clearStorage: () => void;
}

/**
 * Debounce utility for performance optimization
 */
function useDebounce<T extends (...args: never[]) => void>(callback: T, delay: number): T {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;
}

export function useAdvancedTable<T extends TableRow>(
  data: T[],
  options: UseAdvancedTableOptions<T>
): UseAdvancedTableReturn<T> {
  const {
    tableId,
    columns,
    config = {},
    enableUrlSync = true,
    enablePersistence = true,
    restoreFromUrl = true,
    restoreFromStorage = true,
    urlUpdateDelay = 5,
    storageUpdateDelay = 5,
    onStateChange,
    onUrlSync,
    onStorageSync,
  } = options;

  const [searchParams, setSearchParams] = useSearchParams();
  const hasInitialized = useRef(false);
  const lastSyncedState = useRef<Partial<TableState>>({});

  // Pre-register URL parameters to prevent dynamic registration during renders
  // This must happen immediately, not in useEffect, to avoid multiple API calls
  useMemo(() => {
    initializeUrlParams(config?.features, config?.urlParamConfig);
    return null;
  }, [config?.features, config?.urlParamConfig]);

  // Create column IDs array for URL serialization
  // Column metadata

  // Initialize table state
  const initialState = useMemo(() => {
    let state: Partial<TableState> = {};

    // 1. Start with initial state from config
    if (config?.initialState) {
      state = { ...state, ...config.initialState };
    }

    // 2. Restore from localStorage if enabled
    if (restoreFromStorage && enablePersistence && isStorageAvailable()) {
      const storedState = getStoredTableState(tableId);
      if (storedState) {
        state = { ...state, ...storedState };
      }
    }

    // 3. Restore from URL if enabled (takes precedence)
    if (restoreFromUrl && enableUrlSync) {
      const urlState = urlParamsToState(searchParams, config?.features, config?.urlParamConfig);
      if (Object.keys(urlState).length > 0) {
        state = { ...state, ...urlState };
      }
    }

    return state;
  }, [tableId, config, restoreFromStorage, restoreFromUrl, enablePersistence, enableUrlSync, searchParams]);

  // Initialize table state hook
  const tableState = useTableState<T>(columns, initialState);

  // Debounced URL update
  const debouncedUrlUpdate = useDebounce((state: TableState) => {
    if (!enableUrlSync) return;

    const urlParams = stateToUrlParams(state, config?.features, config?.urlParamConfig);
    setSearchParams(urlParams, { replace: true });
    onUrlSync?.(urlParams);
  }, urlUpdateDelay);

  // Debounced storage update
  const debouncedStorageUpdate = useDebounce((state: TableState) => {
    if (!enablePersistence || !isStorageAvailable()) return;

    const success = storeTableState(tableId, state);
    if (success) {
      onStorageSync?.(state);
    }
  }, storageUpdateDelay);

  // Sync state changes to URL and storage
  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      return;
    }

    const currentState = tableState.state;

    // Check if state actually changed to avoid unnecessary updates
    const stateChanged = JSON.stringify(currentState) !== JSON.stringify(lastSyncedState.current);

    if (stateChanged) {
      lastSyncedState.current = currentState;

      // Update URL
      debouncedUrlUpdate(currentState);

      // Update storage
      debouncedStorageUpdate(currentState);

      // Notify state change
      onStateChange?.(currentState);
    }
  }, [tableState.state, debouncedUrlUpdate, debouncedStorageUpdate, onStateChange]);

  // Computed values for data processing
  const computedValues = useMemo(() => {
    const { state } = tableState;

    // Apply filters
    let filteredData = data;
    if (state.filters.length > 0) {
      filteredData = data.filter((row) => {
        return state.filters.every((filter) => {
          const value = row[filter.field as keyof T];
          const filterValue = filter.value;

          switch (filter.operator) {
            case 'equals':
              return value === filterValue;
            case 'contains':
              return String(value).toLowerCase().includes(String(filterValue).toLowerCase());
            case 'startsWith':
              return String(value).toLowerCase().startsWith(String(filterValue).toLowerCase());
            case 'endsWith':
              return String(value).toLowerCase().endsWith(String(filterValue).toLowerCase());
            case 'gt':
              return Number(value) > Number(filterValue);
            case 'lt':
              return Number(value) < Number(filterValue);
            case 'gte':
              return Number(value) >= Number(filterValue);
            case 'lte':
              return Number(value) <= Number(filterValue);
            case 'isEmpty':
              return !value || value === '';
            case 'isNotEmpty':
              return !!value && value !== '';
            default:
              return true;
          }
        });
      });
    }

    // Apply sorting
    const sortedData = [...filteredData];
    if (state.sortColumns.length > 0) {
      sortedData.sort((a, b) => {
        for (const sortCol of state.sortColumns) {
          const aValue = a[sortCol.columnId as keyof T];
          const bValue = b[sortCol.columnId as keyof T];

          let comparison = 0;

          if (aValue < bValue) comparison = -1;
          else if (aValue > bValue) comparison = 1;

          if (comparison !== 0) {
            return sortCol.direction === 'desc' ? -comparison : comparison;
          }
        }
        return 0;
      });
    }

    // Apply pagination
    const { pageIndex, pageSize } = state.pagination;
    const startIndex = pageIndex * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = sortedData.slice(startIndex, endIndex);

    return {
      filteredData,
      sortedData,
      paginatedData,
      totalFilteredCount: filteredData.length,
    };
  }, [data, tableState]);

  // Utility functions
  const resetTable = useCallback(() => {
    urlParamRegistry.clear();
    tableState.actions.reset();
    setSearchParams({}, { replace: true });
  }, [tableState.actions, setSearchParams]);

  const exportState = useCallback(() => {
    return { ...tableState.state };
  }, [tableState.state]);

  const importState = useCallback(
    (state: Partial<TableState>) => {
      Object.entries(state).forEach(([key, value]) => {
        switch (key) {
          case 'filters':
            tableState.actions.hydrateState({ filters: value as Filter[] });
            break;
          case 'sortColumns':
            tableState.actions.setSorting(value as SortColumn[]);
            break;
          case 'pagination':
            tableState.actions.hydrateState({ pagination: value as PaginationState });
            break;
          case 'columnVisibility':
            tableState.actions.hydrateState({ columnVisibility: value as Record<string, boolean> });
            break;
          case 'columnOrder':
            tableState.actions.setColumnOrder((value as { orderedIds: string[] }).orderedIds);
            break;
          case 'columnWidths':
            tableState.actions.hydrateState({ columnWidths: value as Record<string, number> });
            break;
          default:
            // Handle other keys with hydrateState
            tableState.actions.hydrateState({ [key]: value });
        }
      });
    },
    [tableState.actions]
  );

  const clearUrlParams = useCallback(() => {
    urlParamRegistry.clear();
    setSearchParams({}, { replace: true });
  }, [setSearchParams]);

  const clearStorage = useCallback(() => {
    if (isStorageAvailable()) {
      try {
        const keys = Object.keys(localStorage).filter((key) => key.includes(tableId));
        keys.forEach((key) => localStorage.removeItem(key));
      } catch (error) {
        console.warn('Failed to clear storage:', error);
      }
    }
  }, [tableId]);

  // Derived state
  const hasActiveFilters = tableState.state.filters.length > 0;
  const hasActiveSorting = tableState.state.sortColumns.length > 0;
  const hasCustomColumnOrder = tableState.state.columnOrder.orderedIds.length > 0;
  const hasCustomColumnWidths = Object.keys(tableState.state.columnWidths).length > 0;

  const urlParams = useMemo(
    () => stateToUrlParams(tableState.state, config?.features, config?.urlParamConfig),
    [tableState.state, config?.features, config?.urlParamConfig]
  );

  return {
    // State
    state: tableState.state,

    // Column management
    visibleColumns: tableState.visibleColumns,
    allColumns: columns,

    // Data management
    filteredData: computedValues.filteredData,
    sortedData: computedValues.sortedData,
    paginatedData: computedValues.paginatedData,

    // Computed values
    totalFilteredCount: computedValues.totalFilteredCount,
    hasActiveFilters,
    hasActiveSorting,
    hasCustomColumnOrder,
    hasCustomColumnWidths,

    // Actions
    actions: tableState.actions,

    // URL & Storage
    urlParams,
    isUrlSynced: enableUrlSync,
    isStorageSynced: enablePersistence && isStorageAvailable(),

    // Utilities
    resetTable,
    exportState,
    importState,
    clearUrlParams,
    clearStorage,
  };
}
