import { useCallback, useEffect, useMemo, useRef } from 'react';
import { ExternalFilter, UrlParamConfig } from '../types/core.types';

export interface ExternalFilterManagerResult {
  initializedExternalFilters: ExternalFilter[];
  updateExternalFilter: (key: string, value: unknown) => void;
}

interface ExternalFilterManagerConfig {
  externalFilters: ExternalFilter[];
  urlParamConfig: Required<UrlParamConfig>;
  stateExternalFilters: ExternalFilter[];
  actions: {
    hydrateState: (state: { externalFilters: ExternalFilter[] }) => void;
  };
}

export function useExternalFilterManager({
  externalFilters,
  urlParamConfig,
  stateExternalFilters,
  actions,
}: ExternalFilterManagerConfig): ExternalFilterManagerResult {
  // Initialize external filters from URL parameters
  const initializedExternalFilters = useMemo(() => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const filters: ExternalFilter[] = [...externalFilters];

    // If there are external filters, check if their values exist in URL
    if (externalFilters.length > 0) {
      filters.forEach((filter) => {
        // Get the URL parameter key for this filter
        const urlKey =
          typeof urlParamConfig === 'object'
            ? (urlParamConfig[filter.key as keyof typeof urlParamConfig] as string) || filter.key
            : filter.key;
        const urlValue = urlSearchParams.get(urlKey);

        if (urlValue !== null) {
          // Parse the URL value - for now treat as string, could be enhanced later
          let parsedValue: unknown = urlValue;

          // Try to parse as JSON first, fallback to string
          try {
            parsedValue = JSON.parse(urlValue);
          } catch {
            // Keep as string if JSON parsing fails
            parsedValue = urlValue;
          }

          // Update the filter with the URL value
          const filterIndex = filters.findIndex((f) => f.key === filter.key);
          if (filterIndex !== -1) {
            filters[filterIndex] = { ...filters[filterIndex], value: parsedValue };
          }
        }
      });
    }

    return filters;
  }, [externalFilters, urlParamConfig]);

  // Create a stable update function ref to avoid re-render loops
  const updateExternalFilterRef = useRef<(key: string, value: unknown) => void>(() => {});

  // Update the ref whenever actions or stateExternalFilters change
  useEffect(() => {
    updateExternalFilterRef.current = (key: string, value: unknown) => {
      const updatedFilters = stateExternalFilters.map((filter) => (filter.key === key ? { ...filter, value } : filter));
      actions.hydrateState({ externalFilters: updatedFilters });
      console.log(`External filter ${key} updated to:`, value);
    };
  }, [stateExternalFilters, actions]);

  // Create a stable wrapper function that doesn't change reference
  const updateExternalFilter = useCallback((key: string, value: unknown) => {
    updateExternalFilterRef.current?.(key, value);
  }, []);

  return {
    initializedExternalFilters,
    updateExternalFilter,
  };
}
