import { useRef } from 'react';

/**
 * Custom hook for deep comparison memoization
 * Prevents unnecessary re-renders when objects/arrays have the same content
 */
export function useDeepMemo<T>(value: T): T {
  const ref = useRef<T>(value);
  const serialized = JSON.stringify(value);
  const serializedRef = useRef(serialized);

  if (serializedRef.current !== serialized) {
    ref.current = value;
    serializedRef.current = serialized;
  }

  return ref.current;
}
