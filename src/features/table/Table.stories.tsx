import { PlusOutlined } from '@ant-design/icons';
import { Client } from '@app/types/client';
import { TableProvider } from '@context/table/TableProvider';
import { UrlParamProvider } from '@context/urlParams/UrlParamProvider';
import { getClientTableColumns } from '@feat-clients/components/ClientTable/clientTableColumns.config';
import { dummyClients } from '@feat-clients/data/dummy';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Button } from 'antd';
import { MemoryRouter } from 'react-router-dom';
import '../../styles/styles.css';
import Table from './Table';

const mockData: Client[] = [dummyClients[0], dummyClients[1]];

const initialFilters = {
  searchTerm: '',
  sorting: 'StartDate:desc',
  personId: '',
  status: '0,1,2,3',
  orgId: 0,
};

const statusFilters = [
  { Id: 0, label: 'Submitted', value: false },
  { Id: 1, label: 'Approved', value: false },
  { Id: 2, label: 'Rejected', value: false },
];

const meta: Meta<typeof Table<Client>> = {
  title: 'Components/ClientTable',
  component: Table,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    loadingDataFetch: { control: 'boolean' },
    hideActionsColumn: { control: 'boolean' },
    // You can add more controls here if needed
  },
};

export default meta;

type Story = StoryObj<typeof Table<Client>>;

export const Default: Story = {
  args: {
    data: mockData,
    totalData: mockData.length,
    loadingDataFetch: false,
    hideActionsColumn: false,
    localStorageKey: 'clientTable',
  },
  render: (args) => (
    <MemoryRouter>
      <UrlParamProvider>
        <TableProvider
          storageKey="clientTable"
          storageKeyColumns="clientTableColumns"
          initialFilters={initialFilters}
          initialStatusFilters={statusFilters}
          fetchData={() => {}}
          cols={getClientTableColumns()}
        >
          <Table<Client>
            {...args}
            addNavigate={() => alert('Navigate to Add')}
            exportComponent={<Button icon={<PlusOutlined />}>Export</Button>}
            onRowClick={(record) => alert(`Clicked ${record.firstName}`)}
          />
        </TableProvider>
      </UrlParamProvider>
    </MemoryRouter>
  ),
};
