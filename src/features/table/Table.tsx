import { ExtendedTableColumnType } from '@app/types/table.types';
import { parseSorting } from '@app/utils/parseSorting';
import { useTable } from '@context/table/TableProvider';
import { useUrlParams } from '@context/urlParams/UrlParamProvider';
import { Table as AntdTable, Empty, TableProps } from 'antd';
import { ColumnGroupType, ColumnType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import TableFilters from './TableFilters';

type Props<T> = {
  data: T[];
  totalData: number;
  loadingDataFetch: boolean;
  actions?: (record: T) => void;
  onRowClick?: (record: T) => void;
  hideActionsColumn?: boolean;
  heightDifference?: string;
  rowKey?: TableProps<T>['rowKey'];
  addNavigate: () => void;
  exportComponent: React.ReactElement;
  localStorageKey: string;
};

const Table = <T,>({
  data,
  totalData,
  loadingDataFetch = true,
  onRowClick,
  hideActionsColumn = false,
  heightDifference = '18rem',
  rowKey = 'Id',
  addNavigate,
  exportComponent,
  localStorageKey,
}: Props<T>) => {
  const { handleTableChange } = useTable();
  const [showEmpty, setShowEmpty] = useState(false);
  const { columns, setColumns } = useTable();
  const { params } = useUrlParams();
  useEffect(() => {
    setColumns((columns) =>
      columns.map((column) => {
        const sortItem = params.sorting && parseSorting(params.sorting).find((s) => s.key === column.key);

        return {
          ...column,
          sortOrder: sortItem ? (sortItem.value === 'asc' ? 'ascend' : 'descend') : undefined,
        } as ExtendedTableColumnType<unknown>;
      })
    );
  }, [params.sorting]);
  useEffect(() => {
    if (!loadingDataFetch && data.length === 0) {
      const timeout = setTimeout(() => setShowEmpty(true), 20);
      return () => clearTimeout(timeout);
    } else {
      setShowEmpty(false);
    }
  }, [loadingDataFetch, data]);
  console.log('data', data);
  // Filter out actions column if hideActionsColumn is true
  const filteredColumns = hideActionsColumn ? columns.filter((col) => col.key !== 'actions') : columns;
  console.log('cols', filteredColumns.filter((cl) => !cl.hidden).length > 5);
  return (
    <>
      <TableFilters<T>
        localStorageKey={localStorageKey}
        addNavigate={addNavigate}
        hasAdd={true}
        columns={columns as ExtendedTableColumnType<T>[]}
        Export={exportComponent}
      />
      <div className="w-full overflow-x-auto min-w-0">
        <AntdTable<T>
          columns={filteredColumns as (ColumnType<T> | ColumnGroupType<T>)[]}
          dataSource={data}
          size={'middle'}
          bordered={true}
          loading={loadingDataFetch}
          scroll={{
            x: 'max-content',
            y: `calc(100vh - ${heightDifference})`,
          }}
          locale={{
            emptyText: (
              <>
                {loadingDataFetch ? (
                  <>Loading Data...</>
                ) : (
                  showEmpty && (
                    <div className="flex justify-center items-center h-full">
                      <Empty className="text-base" description="No Data" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    </div>
                  )
                )}
              </>
            ),
          }}
          pagination={{
            size: 'default',
            position: ['bottomCenter'],
            current: params.pageIndex ? Number(params.pageIndex) + 1 : 0,
            pageSize: params.pageSize ? Number(params.pageSize) : 25,
            total: totalData,
            pageSizeOptions: ['10', '25', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
          }}
          onRow={(record) => ({
            onClick: () => {
              console.log('render onclick');
              onRowClick?.(record);
            },
            style: onRowClick ? { cursor: 'pointer' } : undefined,
          })}
          sortDirections={['ascend', 'descend']}
          onChange={handleTableChange}
          rowKey={rowKey}
          sticky={true}
        />
      </div>
    </>
  );
};

export default Table;
