/**
 * Table v2 - Performance-Optimized Table System
 *
 * Main entry point for the new table system.
 * Exports all hooks, utilities, types, and examples.
 */

// ============================================================================
// DEFAULT CONFIGURATION
// ============================================================================

/**
 * Default table configuration values
 */
export const TABLE_DEFAULTS = {
  /** Default page size for pagination */
  PAGE_SIZE: 10,
  /** Default page index (first page) */
  PAGE_INDEX: 0,
  /** Available page size options */
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100] as number[],
} as const;

// Core types
export type {
  BaseColumn,
  CellValue,
  ColumnOrder,
  ColumnVisibility,
  DataFetchParams,
  ExternalFilter,
  ExtractExternalParams,
  Filter,
  FilterOperator,
  PaginationState,
  SortColumn,
  SortDirection,
  TableAction,
  TableColumn,
  TableConfig,
  TableRow,
  TableState,
  TypedDataFetchParams,
  TypedExternalFilters,
  TypedOnDataFetch,
  TypedOnParamsChange,
  TypedParamUpdaters,
  UrlParamConfig,
} from './types/core.types';

// Hooks
export type { UseAdvancedTableOptions, UseAdvancedTableReturn } from './hooks/useAdvancedTable';
export { useDeepMemo } from './hooks/useDeepMemo';
export { useExternalFilterManager } from './hooks/useExternalFilterManager';
export { default as useTableState } from './hooks/useTableState';

// Components
export { DebugPanel } from './components/DebugPanel';
export { DragDropColumnManager } from './components/DragDropColumnManager';
export { GenericTable } from './components/GenericTable';
export { TableCore } from './components/TableCore';
export { TableToolbar } from './components/TableToolbar';
export type { AddButtonConfig } from './components/TableToolbar';

// URL Utilities
export {
  cleanUrlParams,
  getActiveCoreParams,
  isCoreTableParam,
  stateToUrlParams,
  urlParamRegistry,
  urlParamsToState,
} from './utils/urlSerialization';

// Storage Utilities
export {
  clearTableStorage,
  getStorageInfo,
  getStorageQuota,
  getStoredColumnOrder,
  getStoredColumnVisibility,
  getStoredColumnWidths,
  getStoredFeatureFlags,
  getStoredLastUsedFilters,
  getStoredPageSize,
  getStoredTableState,
  isStorageAvailable,
  migrateStorage,
  storeColumnOrder,
  storeColumnVisibility,
  storeColumnWidths,
  storeFeatureFlags,
  storeLastUsedFilters,
  storePageSize,
  storeTableState,
} from './utils/localStorage';

// Re-export useAdvancedTable when it's ready
// export { useAdvancedTable } from './hooks/useAdvancedTable';

/**
 * Version information
 */
export const TABLE_V2_VERSION = '2.0.0';
