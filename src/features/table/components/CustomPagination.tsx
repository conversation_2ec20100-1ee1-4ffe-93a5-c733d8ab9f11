import { TABLE_DEFAULTS } from '@app/features/table';
import { Pagination, Select } from 'antd';
import { memo } from 'react';

const { Option } = Select;

export interface CustomPaginationProps {
  current: number;
  pageSize: number;
  total: number;
  pageSizeOptions?: number[];
  showQuickJumper?: boolean;
  onChange: (page: number, pageSize: number) => void;
}

function _CustomPagination({
  current,
  pageSize = TABLE_DEFAULTS.PAGE_SIZE,
  total,
  pageSizeOptions = TABLE_DEFAULTS.PAGE_SIZE_OPTIONS,
  onChange,
}: CustomPaginationProps) {
  const handlePageChange = (page: number) => {
    onChange(page, pageSize);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    onChange(1, newPageSize); // Reset to first page when changing page size
  };

  // Calculate display range
  const startItem = (current - 1) * pageSize + 1;
  const endItem = Math.min(current * pageSize, total);

  return (
    <div className="flex items-center justify-between w-full py-4 px-2">
      {/* Left: Total count text */}
      <div className="flex-shrink-0 text-sm text-gray-700">
        {total > 0 ? `${startItem}-${endItem} of ${total} items` : '0 items'}
      </div>

      {/* Center: Page numbers */}
      <div className="flex-1 flex justify-center">
        <Pagination
          current={current}
          total={total}
          pageSize={pageSize}
          showSizeChanger={false}
          onChange={handlePageChange}
          size="default"
        />
      </div>

      {/* Right: Page size selector */}
      <div className="flex-shrink-0 flex items-center gap-2">
        <span className="text-sm text-gray-700">Show:</span>
        <Select value={pageSize} onChange={handlePageSizeChange} size="small" style={{ width: 70 }}>
          {pageSizeOptions.map((size) => (
            <Option key={size} value={size}>
              {size}
            </Option>
          ))}
        </Select>
      </div>
    </div>
  );
}

export const CustomPagination = memo(_CustomPagination);
