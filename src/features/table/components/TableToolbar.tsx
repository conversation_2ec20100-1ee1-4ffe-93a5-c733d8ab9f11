import { Button, Dropdown } from 'antd';
import { memo } from 'react';
import { FiColumns, FiPlus, FiX } from 'react-icons/fi';

export interface AddButtonConfig {
  text?: string;
  onClick: () => void;
}

export interface TableToolbarProps {
  // Feature flags
  showClearFilters: boolean;
  showColumnVisibility: boolean;
  showAddButton: boolean;

  // Data and state
  hasActiveFilters: boolean;
  columnMenuItems: { key: string; label: React.ReactNode }[];
  addButton?: AddButtonConfig;
  columnDropdownOpen?: boolean;
  onColumnDropdownOpenChange?: (open: boolean) => void;
  leftContent?: React.ReactNode;

  // Actions
  onClearFilters: () => void;
}

function _TableToolbar({
  showClearFilters,
  showColumnVisibility,
  showAddButton,
  hasActiveFilters,
  columnMenuItems,
  addButton,
  columnDropdownOpen,
  onColumnDropdownOpenChange,
  onClearFilters,
  leftContent,
}: TableToolbarProps) {
  return (
    <div className="mb-4 flex justify-between items-center flex-wrap gap-2">
      <div className="flex gap-2 items-center flex-wrap">
        {/* Custom Left Content */}
        {leftContent}
        {/* Clear Filters */}
        {showClearFilters && hasActiveFilters && (
          <Button icon={<FiX />} onClick={onClearFilters} className="flex items-center">
            Clear Filters
          </Button>
        )}
      </div>

      <div className="flex gap-2">
        {/* Column Visibility */}
        {showColumnVisibility && (
          <Dropdown
            menu={{ items: columnMenuItems }}
            trigger={['click']}
            placement="bottomLeft"
            open={columnDropdownOpen}
            onOpenChange={onColumnDropdownOpenChange}
          >
            <Button icon={<FiColumns />} className="flex items-center" />
          </Dropdown>
        )}
        {/* Add Button */}
        {showAddButton && addButton && (
          <Button type="primary" icon={<FiPlus />} onClick={addButton.onClick} className="flex items-center">
            {addButton.text || 'Add'}
          </Button>
        )}
      </div>
    </div>
  );
}

export const TableToolbar = memo(_TableToolbar);
