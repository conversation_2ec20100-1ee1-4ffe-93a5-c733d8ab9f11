import type { MenuProps } from 'antd';
import { Button, Dropdown } from 'antd';
import { memo, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { FiEdit2, FiMoreVertical, FiTrash2 } from 'react-icons/fi';
import { useAdvancedTable } from '../hooks/useAdvancedTable';
import { useDeepMemo } from '../hooks/useDeepMemo';
import { useExternalFilterManager } from '../hooks/useExternalFilterManager';
import { TABLE_DEFAULTS } from '../index';
import {
  ExternalFilter,
  FeatureFlags,
  RowAction,
  RowActionsConfig,
  SortColumn,
  TableColumn,
  TableRow,
  TypedDataFetchParams,
  UrlParamConfig,
} from '../types/core.types';
import { DebugPanel } from './DebugPanel';
import { DragDropColumnManager } from './DragDropColumnManager';
import { TableCore } from './TableCore';
import { AddButtonConfig, TableToolbar } from './TableToolbar';

export interface GenericTableProps<T extends TableRow, U extends UrlParamConfig = UrlParamConfig> {
  /** Unique identifier for the table (for persistence) */
  tableId: string;

  /** Data to display in the table */
  data: T[];

  /** Column definitions */
  columns: TableColumn<T>[];

  /** Loading state */
  loading?: boolean;

  /** Total count for server-side pagination */
  totalCount?: number;

  /** Whether to use server-side processing */
  serverSide?: boolean;

  /** Function key for each row */
  rowKey: keyof T | ((record: T) => string);

  /** Feature flags to enable/disable functionality */
  features?: Partial<FeatureFlags>;

  /** URL parameter configuration */
  urlParamConfig?: Partial<U>;

  /** External filters configuration */
  externalFilters?: ExternalFilter[];

  /** Add button configuration */
  addButton?: AddButtonConfig;

  /** Content to render on the left side of the toolbar */
  leftContent?: ReactNode;

  /** Additional table props */
  tableProps?: Record<string, unknown>;

  /** Row click handler */
  onRowClick?: (record: T) => void;

  /** Data fetch callback with type-safe parameters and helpers */
  onParamsChange?: (
    params: TypedDataFetchParams<U>,
    helpers: {
      /** Current external filter values */
      externalValues: Record<string, unknown>;
      /** Update a specific external filter */
      updateExternalFilter: (key: string, value: unknown) => void;
    }
  ) => Promise<void> | void;

  /** @deprecated Use onParamsChange instead */
  onDataFetch?: (params: TypedDataFetchParams<U>) => void;

  /** Row actions configuration to render an Actions column */
  rowActions?: RowActionsConfig<T>;
}

function _GenericTable<T extends TableRow, U extends UrlParamConfig = UrlParamConfig>({
  tableId,
  data,
  columns,
  loading,
  totalCount,
  serverSide = false,
  rowKey,
  features,
  urlParamConfig = {} as Partial<U>,
  externalFilters = [],
  addButton,
  leftContent,
  tableProps,
  onRowClick,
  onParamsChange,
  onDataFetch,
  rowActions,
}: GenericTableProps<T, U>) {
  // Stabilize props to prevent unnecessary re-renders
  const stableUrlParamConfig = useDeepMemo(urlParamConfig);
  const stableExternalFilters = useDeepMemo(externalFilters);
  const stableFeatures = useDeepMemo(features);
  const stableRowActions = useDeepMemo(rowActions);

  // Compute effective features with defaults
  const effectiveFeatures = useMemo(() => {
    const defaults: FeatureFlags = {
      filtering: true,
      sorting: true,
      pagination: true,
      columnVisibility: true,
      columnReordering: false,
      columnResizing: false,
      export: false,
      rowSelection: false,
      virtualScrolling: false,
      mobileResponsive: true,
    };
    return { ...defaults, ...stableFeatures };
  }, [stableFeatures]);

  // Compute effective URL parameter configuration
  const effectiveUrlParamConfig = useMemo(() => {
    const defaults: Required<UrlParamConfig> = {
      page: 'page',
      pageSize: 'pageSize',
      sorting: 'sorting',
      external: {},
    };
    return { ...defaults, ...stableUrlParamConfig } as Required<U>;
  }, [stableUrlParamConfig]);

  // Initialize external filters with URL values
  const { initializedExternalFilters } = useExternalFilterManager({
    externalFilters: stableExternalFilters,
    urlParamConfig: effectiveUrlParamConfig as Required<UrlParamConfig>,
    stateExternalFilters: [], // Will be updated after hook initialization
    actions: { hydrateState: () => {} }, // Will be updated after hook initialization
  });

  // Initialize table state and actions
  const { state, paginatedData, actions, urlParams } = useAdvancedTable<T>(data, {
    tableId,
    columns,
    config: {
      rowKey,
      initialState: {
        pagination: {
          pageIndex: TABLE_DEFAULTS.PAGE_INDEX,
          pageSize: TABLE_DEFAULTS.PAGE_SIZE,
          totalCount: totalCount || data.length,
        },
        externalFilters: initializedExternalFilters,
      },
      features: effectiveFeatures,
      urlParamConfig: effectiveUrlParamConfig,
      externalFilters: initializedExternalFilters,
    },
    enableUrlSync: true,
    enablePersistence: true,
    restoreFromUrl: true,
    restoreFromStorage: true,
  });

  // Update external filter manager with actual state and actions
  const externalFilterManager = useExternalFilterManager({
    externalFilters: stableExternalFilters,
    urlParamConfig: effectiveUrlParamConfig as Required<UrlParamConfig>,
    stateExternalFilters: state.externalFilters,
    actions,
  });

  // Determine active features with defaults
  const activeFeatures = useMemo(
    () => ({
      filtering: features?.filtering ?? true,
      sorting: features?.sorting ?? true,
      pagination: features?.pagination ?? true,
      columnVisibility: features?.columnVisibility ?? true,
      columnReordering: features?.columnReordering ?? false,
      columnResizing: features?.columnResizing ?? false,
      export: features?.export ?? false,
      rowSelection: features?.rowSelection ?? false,
      virtualScrolling: features?.virtualScrolling ?? false,
      mobileResponsive: features?.mobileResponsive ?? true,
    }),
    [features]
  );

  // Update total count when it changes
  useEffect(() => {
    if (totalCount !== undefined) {
      actions.setTotalCount(totalCount);
    }
  }, [totalCount, actions]);

  // Destructure state properties for stable references
  const { pagination, filters, sortColumns, externalFilters: stateExternalFilters } = state;

  // Call API when specific state properties change (for server-side mode)
  useEffect(() => {
    const activeCallback = onParamsChange || onDataFetch;

    console.log('GenericTableV2 useEffect for parameter changes', {
      serverSide,
      hasCallback: !!activeCallback,
      callbackType: onParamsChange ? 'onParamsChange' : 'onDataFetch',
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      filters,
      sortColumns,
      externalFilters: stateExternalFilters,
    });

    if (serverSide && activeCallback) {
      console.log('API call triggered by state change');

      // Prepare external filter values for API call and helpers
      const externalFilterValues = stateExternalFilters.reduce((acc: Record<string, unknown>, filter) => {
        if (filter.includeInServerRequest !== false) {
          acc[filter.key] = filter.value;
        }
        return acc;
      }, {});

      const params = {
        page: pagination.pageIndex,
        pageSize: pagination.pageSize,
        sorting: sortColumns.map((sort: SortColumn) => ({
          field: sort.columnId,
          direction: sort.direction,
        })),
        ...externalFilterValues, // Spread external filters as direct properties
      } as TypedDataFetchParams<U>;

      if (onParamsChange) {
        // Call the new onParamsChange with helpers
        onParamsChange(params, {
          externalValues: externalFilterValues,
          updateExternalFilter: externalFilterManager.updateExternalFilter,
        });
      } else if (onDataFetch) {
        // Fallback to deprecated onDataFetch
        onDataFetch(params);
      }
    }
  }, [
    serverSide,
    onParamsChange,
    onDataFetch,
    pagination.pageIndex,
    pagination.pageSize,
    filters,
    sortColumns,
    stateExternalFilters,
    externalFilterManager.updateExternalFilter,
  ]);

  // Get paginated data for client-side mode
  const tableData = useMemo(() => {
    if (serverSide) {
      return data; // Server handles pagination
    }
    return paginatedData; // Use the paginatedData from useAdvancedTable
  }, [serverSide, data, paginatedData]);

  // Get ordered columns for display
  const orderedColumns = useMemo(() => {
    if (state.columnOrder.orderedIds.length > 0) {
      return state.columnOrder.orderedIds
        .map((id: string) => columns.find((col) => col.id === id))
        .filter(Boolean) as typeof columns;
    }
    return columns;
  }, [state.columnOrder.orderedIds, columns]);

  // Build Actions column (appended at the end by default)
  const columnsWithActions = useMemo(() => {
    if (!stableRowActions) return columns;

    const actionColId = stableRowActions.column?.id ?? 'actions';

    // If consumer already included an actions column with same id, don't add another
    if (columns.some((c) => c.id === actionColId)) return columns;

    const ActionsCell = memo(function ActionsCell({ row }: { row: T }) {
      // Build default actions from provided handlers
      const defaults: RowAction<T>[] = [];
      if (stableRowActions.onEdit) {
        defaults.push({ key: 'edit', label: 'Edit', icon: <FiEdit2 />, onClick: stableRowActions.onEdit });
      }
      if (stableRowActions.onDelete) {
        defaults.push({
          key: 'delete',
          label: 'Delete',
          icon: <FiTrash2 />,
          danger: true,
          onClick: stableRowActions.onDelete,
        });
      }

      const custom = stableRowActions.getActions ? stableRowActions.getActions(row) : [];
      const actions = [...defaults, ...custom];

      const items: MenuProps['items'] = actions.map((a) => ({
        key: a.key,
        label: a.label,
        icon: a.icon,
        danger: a.danger,
        disabled: a.disabled,
      }));

      const onItemClick: MenuProps['onClick'] = ({ key }) => {
        const act = actions.find((a) => a.key === key);
        act?.onClick?.(row);
      };

      return (
        <Dropdown trigger={['click']} menu={{ items, onClick: onItemClick }}>
          <Button type="text" size="small" aria-label="Row actions">
            <FiMoreVertical />
          </Button>
        </Dropdown>
      );
    });

    const actionColumn: TableColumn<T> = {
      id: actionColId,
      header: stableRowActions.column?.header ?? 'Actions',
      accessor: () => undefined,
      width: stableRowActions.column?.width ?? 56,
      sortable: false,
      align: stableRowActions.column?.align ?? 'center',
      sticky: stableRowActions.column?.sticky ?? 'right',
      isDefault: true,
      cellRenderer: (_value, row) => <ActionsCell row={row} />,
    };

    return [...columns, actionColumn];
  }, [columns, stableRowActions]);

  // Handle column visibility
  const handleColumnVisibilityChange = useCallback(
    (columnId: string) => {
      actions.toggleColumnVisibility(columnId);
    },
    [actions]
  );

  // Handle column reordering - updated to accept new order directly
  const handleColumnReorder = useCallback(
    (newOrder: string[]) => {
      // Update the column order
      actions.setColumnOrder(newOrder);
    },
    [actions]
  );

  // Handle dropdown visibility
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Create column menu items for the toolbar
  const columnMenuItems = useMemo(() => {
    return [
      {
        key: 'draggable-menu',
        label: (
          <DragDropColumnManager
            columns={orderedColumns}
            columnVisibility={state.columnVisibility}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            onColumnReorder={handleColumnReorder}
            onClose={() => setIsDropdownOpen(false)}
          />
        ),
      },
    ];
  }, [orderedColumns, state.columnVisibility, handleColumnVisibilityChange, handleColumnReorder]);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Toolbar */}
      <TableToolbar
        showClearFilters={activeFeatures.filtering}
        showColumnVisibility={activeFeatures.columnVisibility}
        showAddButton={!!addButton}
        hasActiveFilters={state.filters.length > 0}
        columnMenuItems={columnMenuItems}
        addButton={addButton}
        columnDropdownOpen={isDropdownOpen}
        onColumnDropdownOpenChange={setIsDropdownOpen}
        onClearFilters={() => actions.clearFilters()}
        leftContent={leftContent}
      />

      {/* Table */}
      <TableCore
        data={tableData}
        columns={columnsWithActions}
        loading={loading}
        state={state}
        totalCount={totalCount}
        activeFeatures={activeFeatures}
        actions={{
          setPagination: actions.setPagination,
          setSorting: actions.setSorting,
        }}
        rowKey={rowKey}
        onRowClick={onRowClick}
        tableProps={tableProps}
      />

      {/* Debug Panel */}
      <DebugPanel state={state} urlParams={Object.fromEntries(urlParams.entries())} />
    </div>
  );
}

// Export memoized version to prevent unnecessary re-renders
export const GenericTable = memo(_GenericTable) as typeof _GenericTable;
