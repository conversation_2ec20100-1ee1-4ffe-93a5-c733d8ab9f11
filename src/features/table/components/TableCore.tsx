import { SmartCell } from '@app/components/common/SmartCell';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CellValue, FeatureFlags, SortColumn, TableColumn, TableRow, TableState } from '../types/core.types';
import { CustomPagination } from './CustomPagination';

export interface TableCoreProps<T extends TableRow> {
  // Data
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;

  // State
  state: TableState;
  totalCount?: number;

  // Features
  activeFeatures: Required<FeatureFlags>;

  // Actions
  actions: {
    setPagination: (pageIndex: number, pageSize: number) => void;
    setSorting: (sorting: SortColumn[]) => void;
  };

  // Row handling
  rowKey: keyof T | ((record: T) => string);
  onRowClick?: (record: T) => void;

  // Additional props
  tableProps?: Record<string, unknown>;
}

function _TableCore<T extends TableRow>({
  data,
  columns,
  loading = false,
  state,
  totalCount = 0,
  activeFeatures,
  actions,
  rowKey,
  onRowClick,
  tableProps = {},
}: TableCoreProps<T>) {
  // Get ordered columns for display

  const tableContainerRef = useRef<HTMLDivElement>(null);

  const orderedColumns = useMemo(() => {
    const order = state.columnOrder.orderedIds;
    if (order.length > 0) {
      const byOrder = order.map((id: string) => columns.find((col) => col.id === id)).filter(Boolean) as typeof columns;
      // Append any columns not present in saved order (e.g., newly added like Actions)
      const extras = columns.filter((c) => !order.includes(c.id));
      return [...byOrder, ...extras];
    }
    return columns;
  }, [state.columnOrder.orderedIds, columns]);

  // Convert columns to Ant Design format
  const antdColumns: ColumnsType<T> = useMemo(() => {
    // Filter ordered columns by visibility
    const visibleOrderedColumns = orderedColumns.filter((col) => state.columnVisibility[col.id] !== false);

    return visibleOrderedColumns.map((col) => ({
      key: col.id,
      title: col.header,
      dataIndex: typeof col.accessor === 'string' ? col.accessor : undefined,
      width: col.width,
      align: col.align,
      fixed: col.sticky,
      sorter: activeFeatures.sorting ? col.sortable : false,
      sortOrder: state.sortColumns.find((s: SortColumn) => s.columnId === col.id)
        ? state.sortColumns.find((s: SortColumn) => s.columnId === col.id)?.direction === 'asc'
          ? ('ascend' as const)
          : ('descend' as const)
        : undefined,
      render: (value: unknown, record: T) => {
        // Compute raw value via accessor when function provided
        const raw: CellValue = typeof col.accessor === 'function' ? col.accessor(record) : (value as CellValue);
        const content = col.cellRenderer ? col.cellRenderer(raw, record) : undefined;
        return (
          <SmartCell
            value={raw}
            content={content}
            typeOverride={col.type}
            copy={col.copy !== false && col.id !== 'actions'}
            title={typeof raw === 'string' || typeof raw === 'number' ? String(raw) : undefined}
          />
        );
      },
    }));
  }, [orderedColumns, state.columnVisibility, state.sortColumns, activeFeatures.sorting]);

  // Determine what data to display
  const displayData = useMemo(() => {
    return data; // Data processing is handled by the parent component
  }, [data]);

  const [tableHeight, setTableHeight] = useState<number | null>(400);

  const calculateTableHeight = useCallback(() => {
    const container = tableContainerRef.current;

    if (!container) {
      return 400; // fallback height
    }

    const table = container.querySelector('.ant-table') as HTMLElement;
    const containerHeight = container.getBoundingClientRect().height;
    const headerElement = container.querySelector('thead') as HTMLElement;
    const headerHeight = headerElement?.getBoundingClientRect().height || 0;
    const tableHeight = table?.getBoundingClientRect().height || 0;

    const tableHeightIsSmaller = tableHeight <= containerHeight;
    if (tableHeightIsSmaller) {
      return null;
    }

    // Reserve some space for potential scrollbars and padding
    const reservedSpace = 20;
    const availableHeight = containerHeight - headerHeight - reservedSpace;
    console.log('Calculated table height:', availableHeight, { containerHeight, headerHeight, tableHeight });
    return Math.max(200, availableHeight); // minimum 200px height
  }, []);

  // Update table height on mount and window resize
  useEffect(() => {
    const updateHeight = () => {
      const newHeight = calculateTableHeight();
      setTableHeight(newHeight);
    };

    // Initial calculation
    updateHeight();

    // Add resize listener
    window.addEventListener('resize', updateHeight);

    // Also listen for container size changes (e.g., when sidebar collapses)
    const resizeObserver = new ResizeObserver(updateHeight);
    if (tableContainerRef.current) {
      resizeObserver.observe(tableContainerRef.current);
    }

    return () => {
      window.removeEventListener('resize', updateHeight);
      resizeObserver.disconnect();
    };
  }, [calculateTableHeight]);

  useEffect(() => {
    const newHeight = calculateTableHeight();
    setTableHeight(newHeight);
  }, [data.length, calculateTableHeight]);

  return (
    <div className="flex-1 min-h-0 flex flex-col">
      {/* Table container that fills available space */}
      <div id="table-container" className="flex-1 min-h-0 overflow-hidden" ref={tableContainerRef}>
        <Table<T>
          {...tableProps}
          columns={antdColumns}
          dataSource={displayData}
          loading={loading}
          rowKey={rowKey as string}
          pagination={false}
          onChange={(pagination, filters, sorter) => {
            // Handle sorting only if enabled
            if (activeFeatures.sorting && sorter && !Array.isArray(sorter)) {
              if (sorter.order) {
                actions.setSorting([
                  {
                    columnId: String(sorter.columnKey),
                    direction: sorter.order === 'ascend' ? 'asc' : 'desc',
                  },
                ]);
              } else {
                actions.setSorting([]);
              }
            } else if (activeFeatures.sorting && Array.isArray(sorter)) {
              // Handle multiple column sorting
              const sortColumns = sorter
                .filter((s) => s.order)
                .map((s) => ({
                  columnId: String(s.columnKey),
                  direction: (s.order === 'ascend' ? 'asc' : 'desc') as 'asc' | 'desc',
                }));
              actions.setSorting(sortColumns);
            }
          }}
          onRow={(record) => ({
            onClick: () => onRowClick?.(record),
            style: { cursor: onRowClick ? 'pointer' : 'default' },
          })}
          scroll={{
            x: 'max-content',
            ...(tableHeight ? { y: `${tableHeight}px` } : {}),
          }}
          size="middle"
        />
      </div>

      {/* Pagination at the bottom */}
      {activeFeatures.pagination && (
        <div className="shrink-0">
          <CustomPagination
            current={state.pagination.pageIndex + 1}
            pageSize={state.pagination.pageSize}
            total={totalCount}
            onChange={(page, pageSize) => {
              actions.setPagination(page - 1, pageSize); // Convert to 0-based
            }}
          />
        </div>
      )}
    </div>
  );
}

export const TableCore = memo(_TableCore) as typeof _TableCore;
