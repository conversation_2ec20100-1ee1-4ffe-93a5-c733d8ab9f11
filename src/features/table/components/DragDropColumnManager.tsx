import { But<PERSON>, Checkbox } from 'antd';
import { memo, useCallback, useState } from 'react';
import { DragDropContext, Draggable, DropResult, Droppable } from 'react-beautiful-dnd';
import { <PERSON><PERSON><PERSON><PERSON>, FiMove } from 'react-icons/fi';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';
import { TableColumn, TableRow } from '../types/core.types';

export interface DragDropColumnManagerProps<T extends TableRow> {
  columns: TableColumn<T>[];
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (columnId: string) => void;
  onColumnReorder: (newOrder: string[]) => void; // Changed to pass the new order directly
  onClose?: () => void; // Add onClose callback to control dropdown
}

function _DragDropColumnManager<T extends TableRow>({
  columns,
  columnVisibility,
  onColumnVisibilityChange,
  onColumnReorder,
  onClose,
}: DragDropColumnManagerProps<T>) {
  // Local state for pending changes
  const [pendingColumnOrder, setPendingColumnOrder] = useState<string[]>(columns.map((col) => col.id));
  const [pendingVisibility, setPendingVisibility] = useState<Record<string, boolean>>(columnVisibility);

  // Handle local column reordering (not applied until "Apply" is clicked)
  const handleLocalColumnReorder = useCallback((result: DropResult) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    setPendingColumnOrder((currentOrder) => {
      const newOrder = Array.from(currentOrder);
      const [removed] = newOrder.splice(sourceIndex, 1);
      newOrder.splice(destinationIndex, 0, removed);
      return newOrder;
    });
  }, []);

  // Handle local visibility changes (not applied until "Apply" is clicked)
  const handleLocalVisibilityChange = useCallback((columnId: string) => {
    setPendingVisibility((prev) => ({
      ...prev,
      [columnId]: prev[columnId] !== false ? false : true,
    }));
  }, []);

  // Apply all pending changes
  const handleApply = useCallback(() => {
    // Apply column reordering with the new order
    onColumnReorder(pendingColumnOrder);

    // Apply visibility changes
    Object.entries(pendingVisibility).forEach(([columnId, isVisible]) => {
      if (isVisible !== (columnVisibility[columnId] !== false)) {
        onColumnVisibilityChange(columnId);
      }
    });

    // Close the dropdown
    onClose?.();
  }, [pendingColumnOrder, pendingVisibility, columnVisibility, onColumnReorder, onColumnVisibilityChange, onClose]);

  // Get ordered columns based on pending order
  const orderedColumns = pendingColumnOrder
    .map((id) => columns.find((col) => col.id === id))
    .filter(Boolean) as TableColumn<T>[];

  // Prevent dropdown from closing when clicking inside
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <div className="w-64" onClick={handleClick}>
      <div className="p-2">
        <div className="text-sm text-center font-medium text-gray-700">Drag to reorder columns</div>
      </div>

      <div className="relative">
        <SimpleBar style={{ maxHeight: '400px' }} className="px-2">
          <DragDropContext onDragEnd={handleLocalColumnReorder}>
            <Droppable droppableId="columns" isDropDisabled={false}>
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="py-2"
                  style={{
                    minHeight: '50px',
                    position: 'relative',
                    width: '100%',
                    overflow: 'visible',
                  }}
                >
                  {orderedColumns.map((col, index) => (
                    <Draggable key={col.id} draggableId={col.id} index={index} isDragDisabled={false}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`flex items-center justify-between p-2 mb-1 rounded border transition-all ${
                            snapshot.isDragging
                              ? 'bg-blue-50 border-blue-200 shadow-lg transform rotate-1'
                              : 'bg-white border-gray-200 hover:bg-gray-50'
                          }`}
                          style={{
                            ...provided.draggableProps.style,
                            // Keep consistent width and prevent layout shifts
                            width: '100%',
                            minWidth: '200px',
                            maxWidth: '224px',
                            // Use transform instead of position changes for better performance
                            transform: snapshot.isDragging ? provided.draggableProps.style?.transform : 'none',
                          }}
                        >
                          <div className="flex items-center flex-1 min-w-0">
                            <div
                              {...provided.dragHandleProps}
                              className="mr-2 p-1 cursor-grab hover:bg-gray-100 rounded flex-shrink-0"
                            >
                              <FiMove className="text-gray-400" size={14} />
                            </div>
                            <span className="text-sm truncate">{col.header}</span>
                          </div>
                          <Checkbox
                            checked={pendingVisibility[col.id] !== false}
                            onChange={() => handleLocalVisibilityChange(col.id)}
                            className="ml-2 flex-shrink-0"
                          />
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </SimpleBar>
      </div>

      <div className="p-2">
        <Button size="middle" type="primary" onClick={handleApply} icon={<FiCheck />} className="w-full">
          Apply
        </Button>
      </div>
    </div>
  );
}

export const DragDropColumnManager = memo(_DragDropColumnManager) as typeof _DragDropColumnManager;
