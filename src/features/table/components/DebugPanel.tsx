import { memo } from 'react';
import { TableState } from '../types/core.types';

export interface DebugPanelProps {
  state: TableState;
  urlParams?: Record<string, string>;
  isVisible?: boolean;
}

function _DebugPanel({ state, urlParams, isVisible = false }: DebugPanelProps) {
  if (!isVisible || process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="mt-4 p-2 bg-gray-100 rounded text-xs">
      <details>
        <summary>Debug Info</summary>
        <pre>{JSON.stringify({ state, urlParams }, null, 2)}</pre>
      </details>
    </div>
  );
}

export const DebugPanel = memo(_DebugPanel);
