import { GenericTable } from '@app/features/table/components/GenericTable';
import {
  ExternalFilter,
  RowActionsConfig,
  TableColumn,
  TypedDataFetchParams,
  UrlParamConfig,
} from '@app/features/table/types/core.types';
import {
  ClientSearchInput,
  ClientSearchInputProps,
  ClientSearchInputRef,
} from '@feat-clients/components/ClientTable/ClientSearchInput';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// Generic API response structure
interface ApiResponse<T> {
  data: T[];
  total: number;
}

// Generic API parameters
interface ApiParams {
  limit: number;
  offset: number;
  query?: string;
  [key: string]: unknown; // Allow for additional API-specific parameters
}

// Generic fetch function type
type FetchFunction<T> = (params: ApiParams) => Promise<ApiResponse<T>>;

// Transform function to add ID field if needed
type TransformFunction<TApi, TRow> = (item: TApi) => TRow;

// Base configuration for each table
interface TableConfig<TApi, TRow extends { id: string | number }> {
  // Table identification
  tableId: string;

  // URL configuration
  urlParamConfig: UrlParamConfig;

  // Data fetching
  fetchData: FetchFunction<TApi>;
  transformData?: TransformFunction<TApi, TRow>;

  // UI Configuration
  columns: TableColumn<TRow>[];
  searchPlaceholder?: string;

  // Optional row actions (adds an Actions column)
  actions?: RowActionsConfig<TRow>;

  // Add button configuration
  addButton?: {
    text: string;
    onClick: (param?: TRow) => void;
  };

  // Search configuration
  searchConfig?: {
    minSearchLength?: number; // Minimum characters before searching
    debounceMs?: number; // Debounce delay in milliseconds
  };
}

interface GenericDataTableProps<TApi, TRow extends { id: string | number }> {
  config: TableConfig<TApi, TRow>;

  // Optional external state management
  onDataChange?: (data: TRow[], totalCount: number) => void;
  onLoadingChange?: (loading: boolean) => void;
  suffixHoverInfo?: ClientSearchInputProps['suffixHoverInfo'];
}

export function GenericDataTable<TApi, TRow extends { id: string | number }>({
  config,
  onDataChange,
  onLoadingChange,
  suffixHoverInfo,
}: GenericDataTableProps<TApi, TRow>) {
  console.log('Rendering generic', suffixHoverInfo);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TRow[]>([]);
  const [totalCount, setTotalCount] = useState(0);

  // Search input ref and state for managing external filter updates
  const searchInputRef = useRef<ClientSearchInputRef>(null);
  const [updateExternalFilter, setUpdateExternalFilter] = useState<((key: string, value: unknown) => void) | null>(
    null
  );
  const [, setSearchValue] = useState<string>('');
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Create external filters based on URL config
  const externalFilters: ExternalFilter[] = useMemo(() => {
    const filters: ExternalFilter[] = [];

    // Add search term filter if it exists in URL config
    if (config.urlParamConfig.external?.searchTerm) {
      filters.push({
        key: 'searchTerm',
        urlParam: config.urlParamConfig.external.searchTerm,
        value: undefined,
        includeInServerRequest: true,
      });
    }

    // Add other external filters from URL config
    Object.entries(config.urlParamConfig.external || {}).forEach(([key, urlParam]) => {
      if (key !== 'searchTerm') {
        filters.push({
          key,
          urlParam,
          value: undefined,
          includeInServerRequest: true,
        });
      }
    });

    return filters;
  }, [config.urlParamConfig]);

  // Default transform function if none provided
  const defaultTransform = useCallback((item: TApi): TRow => {
    // Assume the API item already has an id field or can be used as-is
    return item as unknown as TRow;
  }, []);

  const transformFunction = config.transformData || defaultTransform;

  // Handle parameter changes with helper object
  const handleParamsChange = useCallback(
    async (
      params: TypedDataFetchParams<typeof config.urlParamConfig>,
      helpers: {
        externalValues: Record<string, unknown>;
        updateExternalFilter: (key: string, value: unknown) => void;
      }
    ) => {
      console.log(`${config.tableId} - Params changed:`, params);
      setLoading(true);
      if (onLoadingChange) onLoadingChange(true);

      // Store the updateExternalFilter function for the search input
      setUpdateExternalFilter(() => helpers.updateExternalFilter);

      try {
        // Extract search term from params
        const searchTerm = (params.searchTerm as string) || '';

        // Apply minimum search length if configured
        const minLength = config.searchConfig?.minSearchLength || 3;
        if (searchTerm.length > 0 && searchTerm.length < minLength) {
          return;
        }

        // Build API parameters
        const apiParams: ApiParams = {
          limit: params.pageSize,
          offset: params.page * params.pageSize,
          ...(searchTerm && { query: searchTerm }),
        };

        // Add any additional external filter values to API params
        Object.entries(helpers.externalValues).forEach(([key, value]) => {
          if (key !== 'searchTerm' && value !== undefined && value !== null && value !== '') {
            apiParams[key] = value;
          }
        });

        console.log(`${config.tableId} - API call with params:`, apiParams);

        // Call the fetch function
        const response = await config.fetchData(apiParams);

        // Transform data if needed
        const transformedData = response.data.map(transformFunction);

        setData(transformedData);
        setTotalCount(response.total);

        // Notify external state if provided
        if (onDataChange) {
          onDataChange(transformedData, response.total);
        }
      } catch (error) {
        console.error(`Failed to fetch ${config.tableId}:`, error);
        setData([]);
        setTotalCount(0);
        if (onDataChange) {
          onDataChange([], 0);
        }
      } finally {
        setLoading(false);
        if (onLoadingChange) onLoadingChange(false);
      }
    },
    [config, transformFunction, onDataChange, onLoadingChange]
  );

  // Handle search input changes with debouncing
  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchValue(value);

      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set new timeout for debounced search
      const debounceMs = config.searchConfig?.debounceMs || 300;
      debounceTimeoutRef.current = setTimeout(() => {
        if (updateExternalFilter) {
          updateExternalFilter('searchTerm', value);
        }
      }, debounceMs);
    },
    [updateExternalFilter, config.searchConfig?.debounceMs]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Create search input component if search is configured
  const searchInput = useMemo(() => {
    if (!config.urlParamConfig.external?.searchTerm) {
      return null;
    }

    return (
      <ClientSearchInput
        ref={searchInputRef}
        placeholder={config.searchPlaceholder || 'Search...'}
        onSearch={handleSearchChange}
        suffixHoverInfo={suffixHoverInfo}
      />
    );
  }, [config.searchPlaceholder, config.urlParamConfig.external?.searchTerm, handleSearchChange, suffixHoverInfo]);

  return (
    <GenericTable<TRow>
      tableId={config.tableId}
      rowKey="id"
      urlParamConfig={config.urlParamConfig}
      columns={config.columns}
      data={data}
      loading={loading}
      totalCount={totalCount}
      serverSide={true}
      onParamsChange={handleParamsChange}
      externalFilters={externalFilters}
      leftContent={searchInput}
      addButton={config.addButton}
      rowActions={config.actions}
    />
  );
}
