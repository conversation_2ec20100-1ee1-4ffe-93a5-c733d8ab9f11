import { ApiClient } from '@api/api-configuration';
import { Client } from '@api/READ_ONLY/client_api/Api';
import {
  GenericSearchSelect,
  type ApiFetchFunction,
  type ApiQueryParams,
} from '@app/components/ui/GenericSearchSelect';
import QuickCreateClientModal from '@feat-clients/components/ClientTable/QuickCreateClientModal';
import { VisitLocation } from '@feat-scheduling/components/VisitCalendarEvent/VisitLocation';
import { Button, SelectProps, Tooltip } from 'antd';
import React from 'react';
import { AiOutlineUserAdd } from 'react-icons/ai';
import { GrLocation, GrPhone } from 'react-icons/gr';

type ClientSelectProps = {
  onClientChange?: (client: Client | Client[] | undefined) => void;
  placeholder?: string;
  pageSize?: number;
  minSearchLength?: number;
  searchDebounceMs?: number;
} & Omit<SelectProps, 'options' | 'onChange' | 'loading' | 'onSearch'>;

// API fetch function for clients
const fetchClients: ApiFetchFunction<Client> = async (params: ApiQueryParams) => {
  return await ApiClient.clientApi.clients.getClientsClientsGet(params);
};

export const ClientSelectGeneric = ({
  onClientChange,
  placeholder = 'Select Patient',
  pageSize = 10,
  minSearchLength = 3,
  searchDebounceMs = 300,
  ...selectProps
}: ClientSelectProps) => {
  const [createOpen, setCreateOpen] = React.useState(false);
  const [lastCreatedClient, setLastCreatedClient] = React.useState<Client | null>(null);

  return (
    <>
      <GenericSearchSelect<Client>
        {...selectProps}
        apiFetch={fetchClients}
        getItemId={(client) => client.clientId}
        getItemLabel={(client) => `${client.firstName} ${client.lastName}`}
        onSelectionChange={onClientChange}
        placeholder={placeholder}
        pageSize={pageSize}
        minSearchLength={minSearchLength}
        searchDebounceMs={searchDebounceMs}
        searchPlaceholder="Search patients"
        lazyLoadOnOpen
        selectedItemsProvider={() => (lastCreatedClient ? [lastCreatedClient] : undefined)}
        renderOption={(client) => (
          <div className="flex flex-col text-sm">
            <span className="font-semibold">{`${client.firstName}, ${client.lastName}`}</span>
            <span className="text-gray-500 text-xs flex items-center gap-2">
              <GrPhone /> {client.phone}
            </span>
            <span className="text-gray-400 text-xs flex items-center gap-2">
              <GrLocation />
              <VisitLocation
                street={client.street || ''}
                city={client.city || ''}
                postalCode={client.postalCode || ''}
              />
            </span>
          </div>
        )}
        labelRender={(option) => {
          console.log({ option });
          return <span>{option.label}</span>;
        }}
        inputSearchProps={{
          suffix: (
            <Tooltip title="Create new client">
              <Button
                type="primary"
                icon={<AiOutlineUserAdd className="text-base" />}
                onClick={(e) => {
                  e.stopPropagation();
                  setCreateOpen(true);
                }}
              />
            </Tooltip>
          ),
        }}
      />
      <QuickCreateClientModal
        open={createOpen}
        onCancel={() => setCreateOpen(false)}
        onCreated={(client) => {
          setCreateOpen(false);
          setLastCreatedClient(client);
          onClientChange?.(client);
        }}
      />
    </>
  );
};

export default ClientSelectGeneric;
