import { ApiClient } from '@api/api-configuration';
import { Client, ClientCreate } from '@api/READ_ONLY/client_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, CLIENT_PAGE, CLIENTS_LIST } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useNavigate } from 'react-router-dom';
import { clientFormConfig } from './form.config';

type Props = {
  data?: Client;
};
export function ClientForm({ data }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const onSubmit = async (formData: ClientCreate) => {
    try {
      // Clean & format data
      const payload: ClientCreate = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        username: formData.username,
        phone: formData.phone,
        dateOfBirth: new Date(formData.dateOfBirth).toISOString().split('T')[0],
        roles: Array.isArray(formData.roles) ? formData.roles : formData.roles ? [formData.roles] : [],
      };

      // Call API
      if (!data) await ApiClient.clientApi.clients.createClientClientsPost(payload);
      else await ApiClient.clientApi.clients.updateClientClientsClientIdPut(String(data.clientId), payload);

      openNotification('topRight', {
        title: `Client`,
        description: 'Client created successfully.',
        type: 'Success',
      });

      navigate(`/${APP_PREFIX}/${CLIENT_PAGE}/${CLIENTS_LIST}`);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Client`,
          description: 'Client creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };

  return <FormWrapper defaultValues={data as ClientCreate} fields={clientFormConfig} onSubmit={onSubmit} />;
}
