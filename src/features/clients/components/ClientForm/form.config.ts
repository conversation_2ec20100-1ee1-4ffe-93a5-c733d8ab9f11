import { FieldGroup } from '@app/types/form.types';

export const clientFormConfig: FieldGroup[] = [
  {
    title: 'Personal Information',
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        width: 'third',
        rules: { required: 'First name is required' },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        width: 'third',
        rules: { required: 'Last name is required' },
      },
      {
        name: 'dateOfBirth',
        label: 'Date of Birth',
        type: 'date',
        width: 'third',
        rules: { required: 'Date of birth is required' },
      },
      {
        name: 'gender',
        label: 'Gender',
        type: 'select',
        width: 'third',
        options: [
          { label: 'Male', value: 'Male' },
          { label: 'Female', value: 'Female' },
          { label: 'Other', value: 'Other' },
        ],
      },
      { name: 'nationalId', label: 'National ID', type: 'text', width: 'third' },
      { name: 'phone', label: 'Phone', type: 'text', width: 'third', rules: { required: 'Phone is required' } },
      { name: 'email', label: 'Email', type: 'email', width: 'third' },
      { name: 'preferredLanguage', label: 'Preferred Language', type: 'text', width: 'third' },
      { name: 'username', label: 'Username', type: 'text', width: 'half', rules: { required: 'Username is required' } },
      {
        name: 'passwordHash',
        label: 'Password',
        type: 'password',
        width: 'half',
        rules: { required: 'Password is required' },
      },
    ],
  },
  {
    title: 'Address',
    fields: [
      { name: 'address.street', label: 'Street', type: 'text', width: 'third' },
      { name: 'address.city', label: 'City', type: 'text', width: 'third' },
      { name: 'address.zip', label: 'ZIP Code', type: 'text', width: 'third' },
      { name: 'address.geoLocation.lat', label: 'Latitude', type: 'number', width: 'half' },
      { name: 'address.geoLocation.lng', label: 'Longitude', type: 'number', width: 'half' },
    ],
  },
  {
    title: 'Medical & Health',
    fields: [
      { name: 'medicalHistory', label: 'Medical History', type: 'textarea', width: 'full' },
      { name: 'medications', label: 'Medications (comma-separated)', type: 'text', width: 'half' },
      { name: 'allergies', label: 'Allergies (comma-separated)', type: 'text', width: 'half' },
      { name: 'mobilityNotes', label: 'Mobility Notes', type: 'textarea', width: 'full' },
    ],
  },
  {
    title: 'Contacts',
    fields: [
      { name: 'emergencyContact.name', label: 'Emergency Contact Name', type: 'text', width: 'third' },
      { name: 'emergencyContact.relationship', label: 'Emergency Contact Relationship', type: 'text', width: 'third' },
      { name: 'emergencyContact.phone', label: 'Emergency Contact Phone', type: 'text', width: 'third' },
      { name: 'secondaryContact.name', label: 'Secondary Contact Name', type: 'text', width: 'third' },
      { name: 'secondaryContact.relationship', label: 'Secondary Contact Relationship', type: 'text', width: 'third' },
      { name: 'secondaryContact.phone', label: 'Secondary Contact Phone', type: 'text', width: 'third' },
    ],
  },
  {
    title: 'Preferences & System',
    fields: [
      { name: 'notes', label: 'Notes', type: 'textarea', width: 'full' },
      {
        name: 'roles',
        label: 'Roles',
        type: 'select',
        width: 'third',
        options: [{ label: 'Client', value: 'client' }],
        rules: { required: 'Role is required' },
      },
      { name: 'active', label: 'Active', type: 'checkbox', width: 'third' },
    ],
  },
];
