import { FieldGroup } from '@app/types/form.types';

export const clientFormConfig: FieldGroup[] = [
  {
    title: 'Personal Information',
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        width: 'third',
        rules: { required: 'First name is required' },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        width: 'third',
        rules: { required: 'Last name is required' },
      },
      { name: 'phone', label: 'Phone', type: 'text', width: 'third', rules: { required: 'Phone is required' } },
      {
        name: 'dateOfBirth',
        label: 'Date of Birth',
        type: 'date',
        width: 'third',
      },
      {
        name: 'gender',
        label: 'Gender',
        type: 'select',
        width: 'third',
        options: [
          { label: 'Male', value: 'Male' },
          { label: 'Female', value: 'Female' },
          { label: 'Other', value: 'Other' },
        ],
      },
      { name: 'nationalId', label: 'National ID', type: 'text', width: 'third' },
      { name: 'email', label: 'Email', type: 'email', width: 'third' },
      { name: 'preferredLanguage', label: 'Preferred Language', type: 'text', width: 'third' },
      { name: 'notes', label: 'Notes', type: 'textarea', width: 'full' },
    ],
  },
  {
    title: 'Address',
    fields: [
      {
        name: 'custom.custom',
        label: 'Address',
        type: 'address', // This will use AddressFieldWrapper automatically
        width: 'full',
        placeholder: 'Start typing to search for address...',
        componentProps: {
          // apiKey: process.env.REACT_APP_GOOGLE_API_KEY,
          apiKey: 'AIzaSyBss9T8BDh_FLg9JZLcLlgS-82RwvHfIf8',
          types: ['address'],
          language: 'en',
          region: 'us',
        },
      },
      { name: 'street', label: 'Street', type: 'text', width: 'third' },
      { name: 'city', label: 'City', type: 'text', width: 'third', rules: { required: 'City is required' } },
      { name: 'postalCode', label: 'Postal Code', type: 'text', width: 'third' },
      { name: 'geoLat', label: 'Latitude', type: 'number', width: 'half' },
      { name: 'geoLng', label: 'Longitude', type: 'number', width: 'half' },
    ],
  },
  {
    title: 'Medical & Health',
    fields: [
      { name: 'medicalHistory', label: 'Medical History', type: 'textarea', width: 'full' },
      { name: 'medications', label: 'Medications (comma-separated)', type: 'text', width: 'half' },
      { name: 'allergies', label: 'Allergies (comma-separated)', type: 'text', width: 'half' },
      { name: 'mobility', label: 'Mobility Notes', type: 'textarea', width: 'full' },
    ],
  },
  {
    title: 'Contacts',
    fields: [
      { name: 'emergencyContact.name', label: 'Emergency Contact Name', type: 'text', width: 'third' },
      { name: 'emergencyContact.relationship', label: 'Emergency Contact Relationship', type: 'text', width: 'third' },
      {
        name: 'emergencyContact.phone',
        label: 'Emergency Contact Phone',
        type: 'text',
        width: 'third',
      },
      { name: 'secondaryContact.name', label: 'Secondary Contact Name', type: 'text', width: 'third' },
      { name: 'secondaryContact.relationship', label: 'Secondary Contact Relationship', type: 'text', width: 'third' },
      { name: 'secondaryContact.phone', label: 'Secondary Contact Phone', type: 'text', width: 'third' },
    ],
  },
];
