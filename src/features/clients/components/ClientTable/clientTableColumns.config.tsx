import { Client } from '@api/READ_ONLY/client_api/Api';
import DateTimeDisplay from '@app/components/common/DateTimeDisplay';
import TableLink from '@app/components/common/TableLink';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';

// Extend Client to work with TableRow requirements
export interface ClientRow extends Omit<Client, 'clientId'>, TableRow {
  id: string | number; // Map clientId to id
  clientId: number; // Keep original field for compatibility
}

export const getClientTableColumns = (editNavigation: (clientId: number) => void): BaseColumn<ClientRow>[] => [
  {
    id: 'name',
    header: 'Full Name',
    accessor: (row) => `${row.firstName} ${row.lastName}`,
    width: 200,
    sortable: true,
    filterable: true,
    cellRenderer: (_, row) => (
      <TableLink onClick={() => editNavigation(row.clientId)}>
        <ClientDisplay firstName={row.firstName || ''} lastName={row.lastName || ''} />
      </TableLink>
    ),
  },
  {
    id: 'dateOfBirth',
    header: 'Date of Birth',
    accessor: 'dateOfBirth',
    width: 150,
    sortable: true,
    filterable: true,
    cellRenderer: (value) => (
      <>
        {value ? (
          <DateTimeDisplay
            startTime={new Date(value as string).toLocaleDateString()}
            showDateSeparately={false}
            dateFormat="MMM DD, YYYY"
            timeFormat=""
          />
        ) : (
          '-'
        )}
      </>
    ),
  },

  {
    id: 'phone',
    header: 'Phone',
    accessor: 'phone',
    width: 150,
    sortable: true,
    filterable: true,
    type: 'phone',
    cellRenderer: (value) => <>{String(value || '-')}</>,
  },
  {
    id: 'email',
    header: 'Email',
    accessor: 'email',
    width: 200,
    sortable: true,
    filterable: true,
    type: 'email',
    cellRenderer: (value) => <>{String(value || '-')}</>,
  },
  {
    id: 'city',
    header: 'City',
    accessor: 'city',
    width: 140,
    sortable: true,
    filterable: true,
    cellRenderer: (value) => <>{String(value || '-')}</>,
  },
  {
    id: 'address',
    header: 'Address',
    accessor: (row) => `${row.street || ''} ${row.city || ''} ${row.postalCode || ''}`.trim(),
    width: 250,
    sortable: false,
    filterable: true,
    type: 'address',
    cellRenderer: (_, row) => <>{`${row.street || ''} ${row.city || ''} ${row.postalCode || ''}`.trim() || '-'}</>,
  },
  {
    id: 'active',
    header: 'Status',
    accessor: 'active',
    width: 100,
    sortable: true,
    filterable: true,
    dataType: 'boolean',
    cellRenderer: (value) => (
      <span style={{ color: value ? '#52c41a' : '#ff4d4f' }}>{value ? 'Active' : 'Inactive'}</span>
    ),
  },
];
