import { ApiClient } from '@api/api-configuration';
import { Client, ClientCreate } from '@api/READ_ONLY/client_api/Api';
import { isValidPhone } from '@app/utils/validators/validPhone';
import { Button, Form, Input, message, Modal } from 'antd';
import React, { useCallback, useState } from 'react';

type QuickCreateClientModalProps = {
  open: boolean;
  onCancel: () => void;
  onCreated: (client: Client) => void;
};

const QuickCreateClientModal: React.FC<QuickCreateClientModalProps> = ({ open, onCancel, onCreated }) => {
  const [form] = Form.useForm<ClientCreate>();
  const [submitting, setSubmitting] = useState(false);

  const handleOk = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      const payload: ClientCreate = {
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        city: values.city.trim(),
        phone: values.phone.trim(),
        active: true,
      };

      const res = await ApiClient.clientApi.clients.createClientClientsPost(payload);
      message.success('Client created');
      form.resetFields();
      onCreated(res.data);
    } catch (err: unknown) {
      if (typeof err === 'object' && err !== null && 'errorFields' in err) {
        return;
      }
      console.error('Failed to create client', err);
      message.error('Failed to create client');
    } finally {
      setSubmitting(false);
    }
  }, [form, onCreated]);

  return (
    <Modal
      open={open}
      title="Create New Patient"
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={submitting}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={submitting} onClick={handleOk}>
          Save
        </Button>,
      ]}
      destroyOnHidden
      maskClosable={!submitting}
    >
      <Form form={form} layout="vertical" preserve={false}>
        <div className="grid grid-cols-2 gap-2">
          <Form.Item
            label="First Name"
            name="firstName"
            rules={[{ required: true, message: 'First name is required' }]}
          >
            <Input size="large" placeholder="First name" autoComplete="off" />
          </Form.Item>
          <Form.Item label="Last Name" name="lastName" rules={[{ required: true, message: 'Last name is required' }]}>
            <Input size="large" placeholder="Last name" autoComplete="off" />
          </Form.Item>
        </div>
        <Form.Item label="City" name="city" rules={[{ required: true, message: 'City is required' }]}>
          <Input size="large" placeholder="City" autoComplete="off" />
        </Form.Item>
        <Form.Item
          label="Phone Number"
          name="phone"
          rules={[
            { required: true, message: 'Phone number is required' },
            {
              validator: (_: unknown, value: string) =>
                !value || isValidPhone(value)
                  ? Promise.resolve()
                  : Promise.reject(new Error('Enter a valid phone number')),
            },
          ]}
        >
          <Input placeholder="Phone number" autoComplete="off" size="large" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuickCreateClientModal;
