import Table from '@app/features/table/Table';
import { APP_PREFIX, CLIENT_PAGE, EDIT_CLIENT, NEW_CLIENT } from '@app/routes/urls';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useNavigate } from 'react-router-dom';

const ClientTable = () => {
  const navigate = useNavigate();
  const clients = useSchedulingStore((state) => state.clients);
  const loadingClients = useSchedulingStore((state) => state.loadingClients);

  return (
    <div className="w-full h-full">
      <Table
        data={clients}
        totalData={clients.length}
        loadingDataFetch={loadingClients}
        addNavigate={() => navigate(`/${APP_PREFIX}/${CLIENT_PAGE}/${NEW_CLIENT}`)}
        exportComponent={<></>}
        localStorageKey="ClientTable"
        onRowClick={(record) => {
          console.log('record', record);
          const path = `/${APP_PREFIX}/${CLIENT_PAGE}/${EDIT_CLIENT.replace(':id', String(record.clientId))}`;
          navigate(path, { state: record });
        }}
      />
    </div>
  );
};

export default ClientTable;
