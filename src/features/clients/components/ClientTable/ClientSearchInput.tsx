import { SearchOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Input, Tooltip } from 'antd';
import { forwardRef, useImperativeHandle, useRef } from 'react';
import { BsInfo } from 'react-icons/bs';

export interface ClientSearchInputRef {
  getValue: () => string;
  setValue: (value: string) => void;
  focus: () => void;
  clear: () => void;
}

export interface ClientSearchInputProps {
  placeholder?: string;
  defaultValue?: string;
  onSearch?: (value: string) => void;
  className?: string;
  suffixHoverInfo?: React.ReactNode;
}

export const ClientSearchInput = forwardRef<ClientSearchInputRef, ClientSearchInputProps>(
  ({ placeholder = 'Search clients...', defaultValue = '', onSearch, className, suffixHoverInfo }, ref) => {
    const inputRef = useRef<InputRef>(null);

    useImperativeHandle(ref, () => ({
      getValue: () => inputRef.current?.input?.value || '',
      setValue: (value: string) => {
        if (inputRef.current?.input) {
          inputRef.current.input.value = value;
        }
      },
      focus: () => inputRef.current?.focus(),
      clear: () => {
        if (inputRef.current?.input) {
          inputRef.current.input.value = '';
        }
      },
    }));

    const handleSearch = (value: string) => {
      onSearch?.(value);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      handleSearch(value);
    };

    return (
      <Input
        ref={inputRef}
        placeholder={placeholder}
        defaultValue={defaultValue}
        onChange={handleChange}
        onPressEnter={(e) => {
          const value = (e.target as HTMLInputElement).value;
          handleSearch(value);
        }}
        prefix={<SearchOutlined />}
        suffix={
          <>
            <Tooltip title={suffixHoverInfo ?? undefined}>
              <div className="bg-gray-200 rounded cursor-pointer">
                <BsInfo />
              </div>
            </Tooltip>
          </>
        }
        allowClear
        className={className}
      />
    );
  }
);

ClientSearchInput.displayName = 'ClientSearchInput';
