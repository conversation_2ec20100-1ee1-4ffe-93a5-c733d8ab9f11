import { Client } from '@app/types/client.types';

export const dummyClients: Client[] = [
  {
    id: '1a2b3c4d-0001-0000-0000-000000000001',
    firstName: 'Alice',
    lastName: '<PERSON>',
    dateOfBirth: new Date('1999-10-08'),
    gender: 'Female',
    nationalId: 'ID123456',
    phone: '+**********',
    email: '<EMAIL>',
    username: 'alice.smith',
    passwordHash: 'hashedpassword1',
    address: {
      street: '123 Main St',
      city: 'Springfield',
      zip: '12345',
      geoLocation: { lat: 40.7128, lng: -74.006 },
    },
    medicalHistory: 'Diabetes',
    medications: ['Metformin'],
    allergies: ['Penicillin'],
    mobilityNotes: 'Uses cane',
    emergencyContact: {
      name: '<PERSON>',
      relationship: 'Husband',
      phone: '+**********',
    },
    secondaryContact: {
      name: '<PERSON>',
      relationship: 'Daughter',
      phone: '+**********',
    },
    preferredLanguage: 'English',
    favoriteProviders: ['caregiver-001'],
    notes: 'Prefers morning visits',
    roles: ['client'],
    active: true,
    createdAt: new Date('2023-01-01T10:00:00Z'),
    updatedAt: new Date('2023-06-01T10:00:00Z'),
  },
  {
    id: '1a2b3c4d-0002-0000-0000-000000000002',
    firstName: 'Bob',
    lastName: '<PERSON>',
    dateOfBirth: new Date('1975-05-20'),
    gender: 'Male',
    username: 'bob.johnson',
    passwordHash: 'hashedpassword2',
    address: {
      city: 'Metropolis',
      zip: '54321',
    },
    medications: ['Aspirin'],
    allergies: [],
    roles: ['client'],
    active: true,
    createdAt: new Date('2023-02-01T11:00:00Z'),
    updatedAt: new Date('2023-06-02T11:00:00Z'),
  },
  {
    id: '1a2b3c4d-0003-0000-0000-000000000003',
    firstName: 'Carol',
    lastName: 'Williams',
    dateOfBirth: new Date('1990-09-10'),
    gender: 'Other',
    phone: '+**********',
    email: '<EMAIL>',
    username: 'carol.williams',
    passwordHash: 'hashedpassword3',
    address: {
      street: '456 Elm St',
      city: 'Gotham',
      zip: '67890',
    },
    medicalHistory: '',
    medications: [],
    allergies: ['Latex'],
    emergencyContact: {
      name: 'Eve Williams',
      relationship: 'Mother',
      phone: '+**********',
    },
    roles: ['client'],
    active: false,
    createdAt: new Date('2023-03-01T12:00:00Z'),
    updatedAt: new Date('2023-06-03T12:00:00Z'),
  },
  {
    id: '1a2b3c4d-0004-0000-0000-000000000004',
    firstName: 'David',
    lastName: 'Brown',
    dateOfBirth: new Date('1965-12-25'),
    username: 'david.brown',
    passwordHash: 'hashedpassword4',
    address: {
      street: '789 Oak St',
      city: 'Star City',
      zip: '13579',
    },
    notes: 'Requires wheelchair access',
    roles: ['client'],
    active: true,
    createdAt: new Date('2023-04-01T13:00:00Z'),
    updatedAt: new Date('2023-06-04T13:00:00Z'),
  },
  {
    id: '1a2b3c4d-0005-0000-0000-000000000005',
    firstName: 'Eve',
    lastName: 'Davis',
    dateOfBirth: new Date('2000-07-30'),
    gender: 'Female',
    nationalId: 'ID654321',
    phone: '+**********',
    email: '<EMAIL>',
    username: 'eve.davis',
    passwordHash: 'hashedpassword5',
    address: {
      city: 'Central City',
      zip: '24680',
      geoLocation: { lat: 34.0522, lng: -118.2437 },
    },
    medications: ['Ibuprofen'],
    allergies: ['Peanuts', 'Shellfish'],
    preferredLanguage: 'Spanish',
    favoriteProviders: ['caregiver-002', 'caregiver-003'],
    roles: ['client'],
    active: true,
    createdAt: new Date('2023-05-01T14:00:00Z'),
    updatedAt: new Date('2023-06-05T14:00:00Z'),
  },
];
