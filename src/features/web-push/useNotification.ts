import { del, get } from 'idb-keyval';
import { useEffect, useState } from 'react';
import { CLOSE_KEY, NOTIFICATION_DB_KEY, REMIND_LATER_KEY } from './web-push.static';

export const useNotification = () => {
  const [showPrompt, setShowPrompt] = useState<boolean>(true);

  useEffect(() => {
    const permission = Notification.permission;
    const remindLater = localStorage.getItem(REMIND_LATER_KEY);
    const closed = localStorage.getItem(CLOSE_KEY);

    if (permission === 'granted') {
      setShowPrompt(false);
    } else if (remindLater && new Date().getTime() < Number(remindLater)) {
      setShowPrompt(false);
    } else if (closed) {
      setShowPrompt(false);
    }
  }, []);

  const handleRemindMe = () => {
    const remindTime = new Date().getTime() + 24 * 60 * 60 * 1000; // 24 hours
    localStorage.setItem(REMIND_LATER_KEY, remindTime.toString());
    setShowPrompt(false);
  };

  const handleClose = () => {
    localStorage.setItem(CLOSE_KEY, 'true');
    setShowPrompt(false);
  };

  const handleBackgroundNotification = async () => {
    const data = await get(NOTIFICATION_DB_KEY);
    if (!data) return;
    del(NOTIFICATION_DB_KEY);

    try {
      if (data.type === 'NOTIFICATION_CLICK') {
        alert(`${data.module}-${data.action} ${data.id}`);
      }
    } catch (error) {
      console.error('Error handling background notification', error);
    }
  };

  return {
    showPrompt,
    handleRemindMe,
    handleClose,
    handleBackgroundNotification,
  };
};
