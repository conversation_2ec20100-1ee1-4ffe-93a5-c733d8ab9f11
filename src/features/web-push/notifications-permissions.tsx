import { BellOutlined, CloseOutlined } from '@ant-design/icons';
import useNotifications from '@context/notifications/useNotificationContext';
import { useWebPush } from '@context/web-push';
import { Button } from 'antd';
import { JSX, memo, useEffect } from 'react';

type PermissionsAction = {
  id: string;
  label: string | JSX.Element;
  action: () => void;
};

const WebPushPermissions = () => {
  const { openNotification } = useNotifications();
  const { enabled, showPrompt, lastMessage, enable, remindLater, closePrompt } = useWebPush();

  const PermissionsActions: PermissionsAction[] = [
    {
      id: 'enable',
      label: 'Enable',
      action: async () => {
        await enable();
      },
    },
    {
      id: 'remind-me',
      label: 'Remind me',
      action: () => remindLater(),
    },
    {
      id: 'close',
      label: <CloseOutlined />,
      action: () => closePrompt(),
    },
  ];

  // Show notification when a new message arrives
  useEffect(() => {
    if (!lastMessage) return;

    openNotification('topRight', {
      title: lastMessage.title ?? 'N/A',
      description: lastMessage.body ?? 'N/A',
      type: 'Success',
    });
  }, [lastMessage, openNotification]);

  // Don't render if notifications are enabled or prompt shouldn't be shown
  if (enabled || !showPrompt) return null;

  return (
    <div
      className="flex w-full justify-between p-4 bg-[#3e63dd] text-white text-[0.8rem]"
      style={{ background: '#3e63dd', color: 'white', fontSize: '0.8rem' }}
    >
      <div className="flex justify-start items-center gap-2">
        <div>
          <BellOutlined />
        </div>
        <div>Homecare needs your permission to send notifications </div>
      </div>
      <div className="flex justify-end items-center gap-4">
        {PermissionsActions.map((action) => (
          <Button className="bg-transparent text-white" key={action.id} onClick={action.action} variant="outlined">
            {action.label}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default memo(WebPushPermissions);
