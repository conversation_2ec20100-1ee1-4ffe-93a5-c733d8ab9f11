import { WebPushProvider } from '@context/web-push';
import { initializeApp } from 'firebase/app';
import { getMessaging } from 'firebase/messaging';
import React from 'react';

// Firebase configuration (matches existing config)
const firebaseConfig = {
  apiKey: 'AIzaSyClHnd6BzHefbhJg-arg6yqsP9mb39p4b8',
  authDomain: 'homecare-2498a.firebaseapp.com',
  projectId: 'homecare-2498a',
  storageBucket: 'homecare-2498a.firebasestorage.app',
  messagingSenderId: '1063363203978',
  appId: '1:1063363203978:web:ee1a9263e338d61fa6ae73',
  measurementId: 'G-69C67NLG31',
};

// VAPID key (matches existing key)
const VAPID_KEY = 'BBjgIqTaqEawNM2_WxLcz5XC8z_U2zmnz8e1PsXl-fTXFf18OpToxYvonFVt81beldq1LDw0rOYGzPTWdyerOYk';

// Initialize Firebase
const firebaseApp = initializeApp(firebaseConfig);
const messaging = getMessaging(firebaseApp);

interface WebPushSetupProps {
  children: React.ReactNode;
  onToken?: (token: string) => Promise<void>;
}

/**
 * Pre-configured WebPushProvider with Firebase setup
 *
 * This component wraps the WebPushProvider with the existing Firebase configuration
 * and VAPID key, making it easy to add to your app without configuration.
 */
export const WebPushSetup: React.FC<WebPushSetupProps> = ({ children, onToken }) => {
  return (
    <WebPushProvider
      messaging={messaging}
      vapidKey={VAPID_KEY}
      swPath="/firebase-messaging-sw.js"
      dbKey="notification"
      remindKey="webpush_remind_later"
      closeKey="webpush_closed"
      onToken={onToken}
    >
      {children}
    </WebPushProvider>
  );
};

export { messaging, VAPID_KEY };
