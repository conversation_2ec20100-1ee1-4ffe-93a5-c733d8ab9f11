import { Visit } from '@api/READ_ONLY/visits_api/Api';
import CaregiverAvailabilityCalendar from '@feat-scheduling/components/AvailabilityCalendar/CaregiverAvailabilityCalendar';
import { useEventsAdjustments } from '@feat-scheduling/components/LeaveCalendar/eventsAdjustments/useEventAdjustment';
import VisitCalendarEvent from '@feat-scheduling/components/VisitCalendarEvent/VisitCalendarEvent';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Dayjs } from 'dayjs';
import 'dayjs/locale/en-gb';
import { memo, useEffect, useRef } from 'react';
import AvailabilityCalendarEvent from '../Availability/AvailabilityCalendarEvent';
import './calendar.scss';
import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';

const SCHEDULING_CALENDAR_ID = 'scheduling-calendar';

interface LeaveCalendarProps {
  height: number;
  visits: Visit[];
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
}

const LeaveCalendar = ({ height, visits, currentDate, setCurrentDate }: LeaveCalendarProps) => {
  useEventsAdjustments(visits, SCHEDULING_CALENDAR_ID);

  const activeCaregiver = useSchedulingStore((state) => state.activeCaregiver);
  const calendarRef = useRef<FullCalendar>(null);
  const leftWrapperRef = useRef<HTMLDivElement>(null);
  const rightWrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const leftWrapper = leftWrapperRef.current;
    const rightWrapper = rightWrapperRef.current;
    if (!leftWrapper || !rightWrapper) return;

    const leftScroller = leftWrapper.querySelectorAll<HTMLDivElement>('.fc-scroller')[1];
    const rightScroller = rightWrapper.querySelectorAll<HTMLDivElement>('.fc-scroller')[1];
    if (!leftScroller || !rightScroller) return;

    const onScroll = () => {
      const leftScrollTop = leftScroller.scrollTop;
      const leftMaxScroll = leftScroller.scrollHeight - leftScroller.clientHeight;
      const ratio = leftMaxScroll > 0 ? leftScrollTop / leftMaxScroll : 0;
      const rightMaxScroll = rightScroller.scrollHeight - rightScroller.clientHeight;
      rightScroller.scrollTop = ratio * rightMaxScroll;
    };

    leftScroller.addEventListener('scroll', onScroll);
    return () => {
      leftScroller.removeEventListener('scroll', onScroll);
    };
  }, []);

  const commonCalendarProps = {
    expandRows: true,
    nowIndicator: true,
    slotMinTime: '06:00:00',
    slotMaxTime: '30:00:00',
    slotDuration: '00:30:00',
    slotEventOverlap: false,
    headerToolbar: false as const,
    allDaySlot: false,
    height,
  };

  const events = visits.map((visit) => ({
    id: visit.id.toString(),
    title:
      typeof visit.client === 'object' && visit.client !== null
        ? `${visit.client ?? ''} ${visit.client ?? ''}`.trim()
        : '',
    start: visit.startTime,
    end: visit.endTime,
    extendedProps: visit,
  }));

  return (
    <div className="w-full">
      <DateNavigator currentDate={currentDate} setCurrentDate={setCurrentDate} calendarRef={calendarRef} />
      <div className="flex h-full w-full relative">
        <div className="basis-full flex-grow-0 flex-shrink-0" id={SCHEDULING_CALENDAR_ID} ref={leftWrapperRef}>
          <FullCalendar
            events={events}
            ref={calendarRef}
            eventOverlap={true}
            moreLinkClassNames="!bg-neutral-100 hover:!bg-neutral-200 w-full text-center hover:text-slate-800 !rounded-full hover:shadow"
            eventDisplay="block"
            initialView="timeGridDay"
            plugins={[dayGridPlugin, interactionPlugin, timeGridPlugin]}
            eventClassNames="rounded-xl overflow-hidden !border-none !bg-transparent"
            eventContent={({ event }) => {
              const fullData: Visit = {
                ...(event.extendedProps as Visit),
                id: Number(event.id),
              };

              if (event.display === 'background') {
                return <AvailabilityCalendarEvent {...fullData} />;
              }

              return <VisitCalendarEvent {...fullData} />;
            }}
            {...commonCalendarProps}
          />
        </div>
        {activeCaregiver && (
          <div className="w-8 absolute right-4 top-0 z-[999]" ref={rightWrapperRef}>
            <CaregiverAvailabilityCalendar height={height} />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(LeaveCalendar);
