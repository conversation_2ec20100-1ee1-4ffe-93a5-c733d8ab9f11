import { useEffect } from 'react';

import { Visit } from '@api/READ_ONLY/visits_api/Api';
import {
  adjustEventWidths,
  findClosestLeftNeighbor,
  findHorizontalLeftGapElements,
  getCalendarElements,
  shiftElementNextToNeighbor,
} from './calendar.utils';

export function useEventsAdjustments(visits: Visit[], calendarId?: string) {
  useEffect(() => {
    if (!visits || visits.length === 0) return;
    (async () => {
      adjustEventWidths(calendarId);
      let iterations = 0; // optional safeguard to avoid infinite loop
      const maxIterations = 10000;
      //So here fo each rendered event. we adjust the first and we continue with the enxt one until we reach the end of the row.
      //if neighborEl exissts we shift the element next to it.
      while (iterations < maxIterations) {
        const els = findHorizontalLeftGapElements(calendarId);
        if (!els || els.length === 0) {
          console.warn(`No more elements found with horizontal left gap. We exit with ${iterations} iterations.`);
          break;
        }

        const allElements = getCalendarElements(calendarId);

        const firstElement = els[0];

        const neighborEl = findClosestLeftNeighbor(firstElement, allElements);
        if (neighborEl) {
          shiftElementNextToNeighbor(calendarId, firstElement, neighborEl);
        }
        iterations++;
      }
    })();
  }, [visits]);
}
