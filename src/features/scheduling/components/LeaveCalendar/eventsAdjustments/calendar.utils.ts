const GAP_THRESHOLD = 10;
const FULLCALNDER_LEFT_TIME_OFFSET = 28; // px
const ELE_WIDTH = null;

function calculateMaxHorizontalOverlap(calendarId?: string) {
  const eventEls = document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId));
  const boxes = Array.from(eventEls).map((el) => {
    const rect = el.getBoundingClientRect();
    return {
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
    };
  });

  let maxOverlap = 0;

  // Build a list of unique Y positions to check (every pixel in range)
  const minY = Math.min(...boxes.map((b) => b.top));
  const maxY = Math.max(...boxes.map((b) => b.bottom));

  for (let y = minY; y <= maxY; y += 1) {
    // you can step by 5 or 10 pixels for performance
    let count = 0;
    boxes.forEach((box) => {
      if (y >= box.top && y <= box.bottom) {
        count++;
      }
    });
    if (count > maxOverlap) {
      maxOverlap = count;
    }
  }

  console.log(`Max horizontal overlap in rendered view: ${maxOverlap}`);
  return maxOverlap;
}
function adjustEventWidths(calendarId?: string) {
  const container = document.querySelector(AdjustCalendarIdToClassName('.fc-timegrid-body', calendarId)); // Adjust selector if needed
  console.log({ container });
  if (!container) return;

  const containerWidth = container.clientWidth; // e.g., 1624px
  const maxOverlap = calculateMaxHorizontalOverlap(calendarId); // Call your existing function
  const idealWidth = ELE_WIDTH || containerWidth / maxOverlap - FULLCALNDER_LEFT_TIME_OFFSET; // Ideal width per event
  console.log('we have maxOverlap', maxOverlap);
  console.log('we have idealWidth', idealWidth, { containerWidth, maxOverlap, FULLCALNDER_LEFT_TIME_OFFSET });

  const eventEls = document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId));
  console.log({ eventEls });
  eventEls.forEach((el: Element) => {
    const parentEl = el as HTMLElement;

    parentEl.style.width = `${idealWidth}px`;
    parentEl.style.zIndex = '1'; // Optional: ensure layering
  });
}

function getCalendarElements(calendarId?: string): HTMLElement[] {
  return Array.from(
    document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId))
  ) as HTMLElement[];
}
function findHorizontalLeftGapElements(calendarId?: string, gapThreshold = GAP_THRESHOLD) {
  const container = document.querySelector(AdjustCalendarIdToClassName('.fc-timegrid-slot', calendarId)); // Adjust selector if needed
  if (!container) return;

  const containerRight = container.getBoundingClientRect().right;

  const allEls = Array.from(
    document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId))
  ).map((el) => {
    const rect = el.getBoundingClientRect();
    return {
      el: el as HTMLElement,
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
    };
  });
  const noNeighborElements: HTMLElement[] = [];
  allEls.forEach((current) => {
    const targetLeft = current.left - gapThreshold;

    // Skip if targetLeft is outside the container's left boundary
    if (targetLeft < containerRight) {
      // console.log('Skipping element at boundary:', current.el);
      return;
    }

    let hasHorizontalOverlap = false;

    for (const other of allEls) {
      if (current.el === other.el) continue;

      // Check vertical overlap (at least 1px)
      const verticallyOverlapping = other.bottom >= current.top + 1 && other.top <= current.bottom - 1;
      if (!verticallyOverlapping) continue;

      // Check if targetLeft is within other's left-right range
      if (targetLeft >= other.left && targetLeft <= other.right) {
        hasHorizontalOverlap = true;
        break;
      }
    }

    if (!hasHorizontalOverlap) {
      // console.log('🟥 Element with horizontal gap found:', current.el);
      // current.el.style.outline = '1px solid red'; // Optional highlight
      noNeighborElements.push(current.el);
    }
  });
  return noNeighborElements;
}

function findClosestLeftNeighbor(currentEl: HTMLElement, allEls: HTMLElement[]): HTMLElement | null {
  const currentRect = currentEl.getBoundingClientRect();
  const currentLeft = currentRect.left;
  const currentTop = currentRect.top;
  const currentBottom = currentRect.bottom;

  let closestLeft: HTMLElement | null = null;
  let maxRight = -Infinity; // Start with minimal value
  for (const otherEl of allEls) {
    if (currentEl === otherEl) continue;

    const otherRect = otherEl.getBoundingClientRect();

    // Check vertical overlap (at least 1px)
    const verticallyOverlapping = otherRect.bottom >= currentTop + 1 && otherRect.top <= currentBottom - 1;
    if (!verticallyOverlapping) continue;

    // Check if other is to the left of current
    if (otherRect.right <= currentLeft && otherRect.right > maxRight) {
      closestLeft = otherEl;
      maxRight = otherRect.right;
    }
  }
  return closestLeft;
}

function shiftElementNextToNeighbor(
  calendarId: string | undefined,
  currentEl: HTMLElement,
  neighborEl: HTMLElement,
  gapThreshold: number = GAP_THRESHOLD
) {
  const neighborRect = neighborEl.getBoundingClientRect();

  const desiredLeft = neighborRect.right + gapThreshold;

  // Get container and axis (left columns) offsets
  const container = document.querySelector<HTMLElement>(AdjustCalendarIdToClassName('.fc-timegrid-body', calendarId));
  const axis = document.querySelector<HTMLElement>(AdjustCalendarIdToClassName('td .fc-timegrid-axis', calendarId)); // Adjust selector if needed
  if (!container || !axis) return;

  const containerRect = container.getBoundingClientRect();
  const axisRect = axis.getBoundingClientRect();

  const totalOffset = containerRect.left + axisRect.width; // Total left offset to subtract
  const relativeLeft = desiredLeft - totalOffset;

  // Set left directly
  currentEl.style.left = `${relativeLeft - GAP_THRESHOLD}px`;
  currentEl.style.right = 'auto'; // Optional: unset right if needed
}

function AdjustCalendarIdToClassName(className: string, calendarId?: string) {
  return calendarId ? `#${calendarId} ${className}` : `${className}`;
}

export {
  adjustEventWidths,
  findClosestLeftNeighbor,
  findHorizontalLeftGapElements,
  getCalendarElements,
  shiftElementNextToNeighbor,
};
