$cell-height: 100px;
$table-border: 2px solid theme('colors.slate.100');

.fc .fc-daygrid-day.fc-day-today {
  background-color: theme('colors.slate.50') !important;
}
.fc-daygrid-day-top {
  justify-content: end;
}

.fc {
  td {
    height: $cell-height;
    background: white;
    border: $table-border;
  }
  .fc-daygrid-event {
    padding: 0;
  }
}
thead {
  th {
    border-bottom: none;
  }
}

$we-lines: theme('colors.slate.200');
$we-bg: theme('colors.slate.50');

.fc-weekend {
  background-image: linear-gradient(
    135deg,
    $we-lines 2.94%,
    $we-bg 2.94%,
    $we-bg 50%,
    $we-lines 50%,
    $we-lines 52.94%,
    $we-bg 52.94%,
    $we-bg 100%
  ) !important;
  background-size: 24.04px 24.04px !important;
}

.disabled-day {
  pointer-events: none; // Prevent clicks
  opacity: 0.5; // Make it look disabled
}

.fc-highlight {
  box-shadow: inset 0 0 0.25rem 0.25rem theme('colors.slate.200') !important;
  background-color: theme('colors.slate.100') !important;
}

.selected {
  border: 2px dashed #54a0ff !important;
  background-color: transparent !important;
}

.Meeting {
  border: 0;
}

.Trip {
  border: 0;
  padding-left: 0.25rem;
}

.Leave {
  border: 0;
  padding-left: 0.25rem;
}

.CountryHoliday {
  border: 0;
  padding-left: 0.25rem;
}

.holiday {
  border: 0;
  padding-left: 0.25rem;
}

.fc-daygrid-day-frame .fc-scrollgrid-sync-inner {
  height: $cell-height !important;
}

.status {
  border: none;
  padding-left: 0.25rem;
}

.calendar-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--dynamic-color);
  border-color: var(--dynamic-color);
}

.calendar-checkbox .ant-checkbox-checked::after {
  border-color: var(--dynamic-color);
}

.calendar-checkbox:hover .ant-checkbox-inner {
  border-color: var(--dynamic-color);
}

.calendar-checkbox .ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: var(--dynamic-color);
}
.fc .fc-col-header-cell-cushion {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: theme('colors.slate.600') !important;
}

// Removes day title-header
#scheduling-calendar {
  .fc-col-header-cell {
    background-color: theme('colors.slate.100');
    border: $table-border !important;
    display: none;
  }
}

.fc-scrollgrid-sync-inner {
  height: 2rem !important;
  margin: auto;
}
.fc-theme-standard .fc-scrollgrid {
  border: $table-border !important;
  overflow: hidden !important;
  border-radius: 0.75rem !important;
}
.fc .fc-daygrid-day-number {
  color: theme('colors.slate.600') !important;
  font-weight: 600;
}

.fc-view {
  box-shadow: theme('boxShadow.sm') !important;
  border-radius: theme('borderRadius.2xl') !important;
}

.fc-daygrid-more-link .fc-more-link {
  border-radius: 0.5rem !important;
  width: 100% !important;
  text-align: center !important;
  background-color: var(--tw-color-neutral-500) !important;
}

.fc-popover,
.fc-more-popover {
  border-radius: 0.75rem !important;
}

.fc-popover-body {
  padding: 0px 0px 0.175rem 0px !important;
}
.fc-event {
  box-shadow: none !important;
  // border: none !important;
  // background: none !important;
}
// .fc-timegrid-event-harness {
//   transition: 0.3s left ease-in-out !important;
// }

#left-calendar {
  padding-bottom: 40px;
}

#right-calendar {
  // .fc-timegrid-slot-label,
  // .fc-timegrid-axis {
  //   display: none !important;
  // }
  .fc-timegrid-slot-label {
    display: none !important;
  }
}
.simplebar-scrollbar::before {
  background-color: var(--color-app-secondary) !important;
  opacity: 0.7;
}

/* Set red background for the time grid */
.fc-timegrid-body,
.fc-timegrid-slot-lane {
  // background-color: var(--color-app-secondary-extra-lightest) !important;
  background-color: #eef2ff !important;
}

.fc-timegrid-slot[data-time$=':30:00'] {
  border-top: none !important;
}

.fc-timegrid-slot .fc-timegrid-slot-label .fc-timegrid-slot-minor {
  border: none !important;
}
