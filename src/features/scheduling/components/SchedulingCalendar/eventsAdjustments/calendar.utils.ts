import { calculateMaxHorizontalOverlap } from '@feat-scheduling/components/SchedulingCalendar/eventsAdjustments/utils/calculateMaxHorizontalOverlaping';
import { AdjustCalendarIdToClassName } from '@feat-scheduling/components/SchedulingCalendar/eventsAdjustments/utils/helpers';

const GAP_THRESHOLD = 10;
const FULLCALNDER_LEFT_TIME_OFFSET = 28 * 2; // px
const ELE_WIDTH = null;
const MIN_WIDTH = 100; //px

function adjustEventWidths(calendarId?: string) {
  const container = document.querySelector(AdjustCalendarIdToClassName('.fc-timegrid-body', calendarId)); // Adjust selector if needed

  console.log('main calendar element to checkup for: ', { container });

  if (!container) return;

  const containerWidth = container.clientWidth; // e.g., 1624px

  // We scan the calendar and we find the max number of overlapping events horizontally
  const maxOverlap = calculateMaxHorizontalOverlap(calendarId);

  //we take into account the left offset of the calendar (time column)
  const fixedContainerWidth = containerWidth - FULLCALNDER_LEFT_TIME_OFFSET;

  // Ideal width per event
  let idealWidth = Math.ceil(ELE_WIDTH || fixedContainerWidth / maxOverlap);

  if (idealWidth > MIN_WIDTH) idealWidth = MIN_WIDTH;

  console.log(
    ` Max horizontal overlap: ${maxOverlap}, containerWidth: ${containerWidth}, calendarOffset: ${FULLCALNDER_LEFT_TIME_OFFSET}\n\n idealWidth: ${idealWidth}`
  );

  const eventEls = document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId));

  eventEls.forEach((el: Element) => {
    const parentEl = el as HTMLElement;

    parentEl.style.width = `${idealWidth}px`;
    parentEl.style.zIndex = '1'; // Optional: ensure layering
  });
}

function getCalendarElements(calendarId?: string): HTMLElement[] {
  return Array.from(
    document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId))
  ) as HTMLElement[];
}
function findHorizontalLeftGapElements(calendarId?: string, gapThreshold = GAP_THRESHOLD) {
  const container = document.querySelector(AdjustCalendarIdToClassName('.fc-timegrid-slot', calendarId)); // Adjust selector if needed
  if (!container) return;

  const containerRight = container.getBoundingClientRect().right;

  const allEls = Array.from(
    document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId))
  ).map((el) => {
    const rect = el.getBoundingClientRect();
    return {
      el: el as HTMLElement,
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
    };
  });
  const noNeighborElements: HTMLElement[] = [];
  allEls.forEach((current) => {
    const targetLeft = current.left - gapThreshold;

    // Skip if targetLeft is outside the container's left boundary
    if (targetLeft < containerRight) {
      // console.log('Skipping element at boundary:', current.el);
      return;
    }

    let hasHorizontalOverlap = false;

    for (const other of allEls) {
      if (current.el === other.el) continue;

      // Check vertical overlap (at least 1px)
      const verticallyOverlapping = other.bottom >= current.top + 1 && other.top <= current.bottom - 1;
      if (!verticallyOverlapping) continue;

      // Check if targetLeft is within other's left-right range
      if (targetLeft >= other.left && targetLeft <= other.right) {
        hasHorizontalOverlap = true;
        break;
      }
    }

    if (!hasHorizontalOverlap) {
      // console.log('🟥 Element with horizontal gap found:', current.el);
      // current.el.style.outline = '1px solid red'; // Optional highlight
      noNeighborElements.push(current.el);
    }
  });
  return noNeighborElements;
}

function findClosestLeftNeighbor(currentEl: HTMLElement, allEls: HTMLElement[]): HTMLElement | null {
  const currentRect = currentEl.getBoundingClientRect();
  const currentLeft = currentRect.left;
  const currentTop = currentRect.top;
  const currentBottom = currentRect.bottom;

  let closestLeft: HTMLElement | null = null;
  let maxRight = -Infinity; // Start with minimal value
  for (const otherEl of allEls) {
    if (currentEl === otherEl) continue;

    const otherRect = otherEl.getBoundingClientRect();

    // Check vertical overlap (at least 1px)
    const verticallyOverlapping = otherRect.bottom >= currentTop + 1 && otherRect.top <= currentBottom - 1;
    if (!verticallyOverlapping) continue;

    // Check if other is to the left of current
    if (otherRect.right <= currentLeft && otherRect.right > maxRight) {
      closestLeft = otherEl;
      maxRight = otherRect.right;
    }
  }
  return closestLeft;
}

function shiftElementNextToNeighbor(
  calendarId: string | undefined,
  currentEl: HTMLElement,
  neighborEl: HTMLElement,
  gapThreshold: number = GAP_THRESHOLD
) {
  const neighborRect = neighborEl.getBoundingClientRect();

  const desiredLeft = neighborRect.right + gapThreshold;

  // Get container and axis (left columns) offsets
  const container = document.querySelector<HTMLElement>(AdjustCalendarIdToClassName('.fc-timegrid-body', calendarId));
  const axis = document.querySelector<HTMLElement>(AdjustCalendarIdToClassName('td .fc-timegrid-axis', calendarId)); // Adjust selector if needed
  if (!container || !axis) return;

  const containerRect = container.getBoundingClientRect();
  const axisRect = axis.getBoundingClientRect();

  const totalOffset = containerRect.left + axisRect.width; // Total left offset to subtract
  const relativeLeft = desiredLeft - totalOffset;

  // Set left directly
  currentEl.style.left = `${relativeLeft - GAP_THRESHOLD}px`;
  currentEl.style.right = 'auto'; // Optional: unset right if needed
}

export {
  adjustEventWidths,
  findClosestLeftNeighbor,
  findHorizontalLeftGapElements,
  getCalendarElements,
  shiftElementNextToNeighbor,
};
