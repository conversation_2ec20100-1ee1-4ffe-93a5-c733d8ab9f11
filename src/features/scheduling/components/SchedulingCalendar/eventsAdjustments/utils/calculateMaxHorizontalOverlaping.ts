import { AdjustCalendarIdToClassName } from '@feat-scheduling/components/SchedulingCalendar/eventsAdjustments/utils/helpers';

interface EventData {
  element: Element;
  left: number;
  right: number;
  top: number;
  bottom: number;
}

interface SweepLineEvent {
  y: number;
  type: 'start' | 'end';
  eventIndex: number;
}

// Optimized version using sweep line algorithm - O(n log n) instead of O(n²)
export function calculateMaxHorizontalOverlap(calendarId?: string): number {
  const eventEls = document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId));

  if (eventEls.length === 0) return 0;

  const events: EventData[] = Array.from(eventEls).map((el) => {
    const rect = el.getBoundingClientRect();
    return {
      element: el,
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
    };
  });

  // Create sweep line events (start and end points for each event)
  const sweepEvents: SweepLineEvent[] = [];
  const TOLERANCE = 1; // 1px tolerance to avoid boundary touching

  events.forEach((event, index) => {
    sweepEvents.push({
      y: event.top,
      type: 'start',
      eventIndex: index,
    });
    sweepEvents.push({
      y: event.bottom - TOLERANCE, // Subtract tolerance from end point
      type: 'end',
      eventIndex: index,
    });
  });

  // Sort by Y coordinate, with 'end' events coming before 'start' events at the same Y
  // This ensures that when an event ends at Y=200 and another starts at Y=200,
  // the end happens first, preventing false overlap counting
  sweepEvents.sort((a, b) => {
    if (a.y !== b.y) return a.y - b.y;
    return a.type === 'end' ? -1 : 1; // end before start at same Y
  });

  let maxOverlap = 0;
  let currentOverlap = 0;

  // Sweep through the events
  for (const sweepEvent of sweepEvents) {
    if (sweepEvent.type === 'start') {
      currentOverlap++;
      maxOverlap = Math.max(maxOverlap, currentOverlap);
    } else {
      currentOverlap--;
    }
  }

  return maxOverlap;
}

// Keep the original O(n²) version for comparison or fallback
export function calculateMaxHorizontalOverlapOriginal(calendarId?: string): number {
  const eventEls = document.querySelectorAll(AdjustCalendarIdToClassName('.fc-timegrid-event-harness', calendarId));
  const events: EventData[] = Array.from(eventEls).map((el) => {
    const rect = el.getBoundingClientRect();

    return {
      element: el,
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
    };
  });

  let maxOverlap = 0;

  // Check each event against all others for vertical overlap (time overlap in FullCalendar)
  for (let i = 0; i < events.length; i++) {
    let overlapCount = 1; // Count the current event itself
    const currentEvent = events[i];

    for (let j = 0; j < events.length; j++) {
      if (i === j) continue; // Skip self

      const otherEvent = events[j];

      // Check if events overlap vertically (same time period)
      if (eventsOverlapVertically(currentEvent, otherEvent)) {
        overlapCount++;
      }
    }

    if (overlapCount > maxOverlap) {
      maxOverlap = overlapCount;
    }
  }

  return maxOverlap;
}

// Helper function to check if two events overlap vertically (time overlap)
function eventsOverlapVertically(event1: EventData, event2: EventData): boolean {
  // Events overlap vertically if their Y ranges intersect
  // This means they happen at the same time period in FullCalendar
  return !(event1.bottom <= event2.top || event2.bottom <= event1.top);
}
