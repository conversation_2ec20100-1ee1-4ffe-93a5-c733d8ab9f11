import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';
import CaregiverAvailabilityCalendar from '@feat-scheduling/components/AvailabilityCalendar/CaregiverAvailabilityCalendar';
import { useEventsAdjustments } from '@feat-scheduling/components/SchedulingCalendar/eventsAdjustments/useEventAdjustment';
import VisitCalendarEvent from '@feat-scheduling/components/VisitCalendarEvent/VisitCalendarEvent';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Dayjs } from 'dayjs';
import 'dayjs/locale/en-gb';
import { memo, useEffect, useRef } from 'react';
import AvailabilityCalendarEvent from '../Availability/AvailabilityCalendarEvent';
import './calendar.scss';

const SCHEDULING_CALENDAR_ID = 'scheduling-calendar';

interface SchedulingCalendarProps {
  visits: VisitPopulated[];
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
}

export type commonSlotProperties = {
  slotMinTime: string;
  slotMaxTime: string;
  slotDuration: string;
};
const CALENDAR_SLOT_PROPS: commonSlotProperties = {
  slotMinTime: '06:00:00',
  slotMaxTime: '23:59:00',
  slotDuration: '01:00:00',
};
const SchedulingCalendar = ({ visits, currentDate, setCurrentDate }: SchedulingCalendarProps) => {
  useEventsAdjustments(visits, SCHEDULING_CALENDAR_ID);

  const activeCaregiver = useSchedulingStore((state) => state.activeCaregiver);
  const calendarRef = useRef<FullCalendar | null>(null);
  const leftWrapperRef = useRef<HTMLDivElement>(null);
  const rightWrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const leftWrapper = leftWrapperRef.current;
    const rightWrapper = rightWrapperRef.current;
    if (!leftWrapper || !rightWrapper) return;

    const leftScroller = leftWrapper.querySelectorAll<HTMLDivElement>('.fc-scroller')[1];
    const rightScroller = rightWrapper.querySelectorAll<HTMLDivElement>('.fc-scroller')[1];
    if (!leftScroller || !rightScroller) return;

    const onScroll = () => {
      const leftScrollTop = leftScroller.scrollTop;
      const leftMaxScroll = leftScroller.scrollHeight - leftScroller.clientHeight;
      const ratio = leftMaxScroll > 0 ? leftScrollTop / leftMaxScroll : 0;
      const rightMaxScroll = rightScroller.scrollHeight - rightScroller.clientHeight;
      rightScroller.scrollTop = ratio * rightMaxScroll;
    };

    leftScroller.addEventListener('scroll', onScroll);
    return () => {
      leftScroller.removeEventListener('scroll', onScroll);
    };
  }, []);

  const commonCalendarProps = {
    expandRows: true,
    nowIndicator: true,
    slotEventOverlap: false,
    headerToolbar: false as const,
    allDaySlot: false,
    height: '100%',
    ...CALENDAR_SLOT_PROPS,
  } as const;

  const events = visits.map((visit) => ({
    id: visit.id.toString(),
    title:
      typeof visit.client === 'object' && visit.client !== null
        ? `${visit.client ?? ''} ${visit.client ?? ''}`.trim()
        : '',
    start: visit.startTime,
    end: visit.endTime,
    extendedProps: visit,
  }));
  console.log({ events });
  return (
    <div className="flex h-full min-h-0 w-full flex-col">
      <div className="shrink-0">
        <DateNavigator currentDate={currentDate} setCurrentDate={setCurrentDate} calendarRef={calendarRef} />
      </div>
      <div className="relative flex-1 min-h-0 flex">
        <div className="flex-1 min-w-0 h-full" id={SCHEDULING_CALENDAR_ID} ref={leftWrapperRef}>
          <FullCalendar
            viewClassNames={'scheduling-calendars'}
            events={events}
            ref={calendarRef}
            eventOverlap={true}
            moreLinkClassNames="!bg-neutral-100 hover:!bg-neutral-200 w-full text-center hover:text-slate-800 !rounded-full hover:shadow"
            eventDisplay="block"
            initialView="timeGridDay"
            plugins={[dayGridPlugin, interactionPlugin, timeGridPlugin]}
            eventClassNames="rounded-xl overflow-hidden !border-none !bg-transparent"
            eventContent={({ event }) => {
              const fullData: VisitPopulated = {
                ...(event.extendedProps as VisitPopulated),
                id: Number(event.id),
              };

              if (event.display === 'background') {
                return <AvailabilityCalendarEvent {...fullData} />;
              }

              return <VisitCalendarEvent {...fullData} />;
            }}
            {...commonCalendarProps}
          />
          {activeCaregiver && (
            <div className="testing w-8 h-full absolute right-0 top-0 z-[999]" ref={rightWrapperRef}>
              <CaregiverAvailabilityCalendar slotProps={CALENDAR_SLOT_PROPS} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(SchedulingCalendar);
