import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { memo } from 'react';
import { VisitCaregiver } from './VisitCaregiver';
import { VisitMapDisplay } from './VisitMapDisplay';
import { VisitNotesEditor } from './VisitNotesEditor';
import { VisitServices } from './VisitServices';
import { VisitTimeEditor } from './VisitTimeEditor';

type VisitDetailsProps = {
  visit: VisitPopulated;
  isActive: boolean;
  onTimeUpdate: (visitId: number, startTime: string, endTime: string) => Promise<void>;
  onNotesUpdate: (visitId: number, notes: string) => Promise<void>;
};

/**
 * Component that displays the expandable details of a visit
 * Shows services, notes, time details, map, and caregiver when expanded
 */
export const VisitDetails = memo(function VisitDetails({
  visit,
  isActive,
  onTimeUpdate,
  onNotesUpdate,
}: VisitDetailsProps) {
  const { services, caregivers, client } = visit;

  console.log('service notes', visit.serviceRequest.notes);

  return (
    <div className={`transition-all overflow-hidden duration-500 ${isActive ? 'max-h-[1000px]' : 'max-h-0'} mt-2`}>
      <div className="pt-2">
        {/* Services Section */}
        <VisitServices services={services} />

        {/* Editable Notes Section */}
        <VisitNotesEditor visit={visit} onNotesUpdate={onNotesUpdate} />

        {/* Editable Time Details */}
        <VisitTimeEditor visit={visit} onTimeUpdate={onTimeUpdate} />

        {/* Map Display */}
        <VisitMapDisplay client={client} />

        {/* Caregiver Display */}
        <VisitCaregiver caregivers={caregivers} visitId={visit.id} showRemoveButton={true} />
      </div>
    </div>
  );
});
