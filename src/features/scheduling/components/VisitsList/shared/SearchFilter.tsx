import { Input } from 'antd';
import { FiSearch } from 'react-icons/fi';
import { CustomFilterIcon } from './CustomFilterIcon';

type SearchFilterProps = {
  title: JSX.Element;
  placeholder: string;
  searchText: string;
  setSearchText: (text: string) => void;
  onOpenFilters?: () => void;
};

const SearchFilter = ({ title, placeholder, searchText, setSearchText, onOpenFilters }: SearchFilterProps) => {
  return (
    <div className="max-w-md flex flex-col ">
      <div className="text-center p-2 text-sm rounded-t-md bg-app-secondary-extra-light">{title}</div>
      <Input
        size="large"
        placeholder={placeholder}
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        prefix={<FiSearch className="text-color-app-text mr-2 " />}
        suffix={onOpenFilters && <CustomFilterIcon onClick={onOpenFilters} />}
        style={{ borderRadius: '0 0 6px 6px' }}
      />
    </div>
  );
};

export default SearchFilter;
