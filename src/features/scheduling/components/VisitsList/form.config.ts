import { services } from '@app/mock/services';
import { FieldGroup } from '@app/types/form.types';
import { dummyClients } from '@feat-clients/data/dummy';

export const filtersFormConfig: FieldGroup[] = [
  {
    fields: [
      {
        name: 'clientName',
        label: 'Client Name',
        type: 'select',
        width: 'full',
        options: dummyClients.map((t) => {
          return { label: t.firstName + ' ' + t.lastName, value: t.id };
        }),
      },
      { name: 'address.street', label: 'Street', type: 'text', width: 'third' },
      { name: 'address.city', label: 'City', type: 'text', width: 'third' },
      { name: 'address.zip', label: 'ZIP Code', type: 'text', width: 'third' },
      {
        name: 'services',
        label: 'Services',
        type: 'multiple-select',
        width: 'full',
        options: services.map((t) => {
          return { label: t.name, value: t.id };
        }),
      },
      {
        name: 'assignments',
        label: 'Assignment status',
        type: 'radiogroup',
        width: 'full',
        options: [
          { label: 'All', value: 0 },
          { label: 'Assigned', value: 1 },
          { label: 'Unassigned', value: 2 },
        ],
      },
    ],
  },
];
