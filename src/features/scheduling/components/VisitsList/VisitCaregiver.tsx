import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { CaregiverAvatar } from '@feat-scheduling/components/CaregiverAvatar';

type VisitCaregiverProps = {
  caregivers: VisitPopulated['caregivers'];
  visitId?: number;
  showRemoveButton?: boolean;
  onCaregiverRemoved?: () => void;
};

/**
 * Component that displays the assigned caregiver for a visit
 * Shows avatar and name of the first caregiver with optional remove functionality
 */
export function VisitCaregiver({
  caregivers,
  visitId,
  showRemoveButton = false,
  onCaregiverRemoved,
}: VisitCaregiverProps) {
  if (!caregivers || caregivers.length === 0) return null;
  const caregiver = caregivers[0];
  const fullname = `${caregiver.firstName} ${caregiver.lastName}`;
  const fullnameLetters = getInitials(fullname, { fallback: 'NA' });

  return (
    <div className="flex items-center gap-2 justify-center mt-2">
      <CaregiverAvatar
        fullName={fullname}
        value={fullnameLetters}
        isSelected={true}
        showRemoveButton={showRemoveButton}
        visitId={visitId}
        caregiverId={caregiver.caregiverId}
        onRemoveComplete={onCaregiverRemoved}
      />
      {`${caregiver.firstName}, ${caregiver.lastName}`}
    </div>
  );
}
