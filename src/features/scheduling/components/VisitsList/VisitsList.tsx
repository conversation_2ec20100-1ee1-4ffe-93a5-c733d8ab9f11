import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { Visit } from '@api/READ_ONLY/visits_api/Api';
import { ScrollProps } from '@app/types/ui.types';
import { getCaregiverFullName } from '@app/utils/getCaregiverFullName';
import VisitAvatar from '@feat-scheduling/components/Avatar/VisitAvatar';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayjs from 'dayjs';
import { memo, useEffect, useRef, useState } from 'react';
import { GrLocation } from 'react-icons/gr';
import { MdOutlineKeyboardArrowDown } from 'react-icons/md';
import SimpleBar from 'simplebar-react';
import SearchContainer from './shared/SearchContainer';
import VisitFilters from './VisitFilters';

function VerticalLineStyling(index: number, length: number) {
  const isFirst = index === 0;
  const isLast = index === length - 1;

  if (isFirst) {
    return 'h-1/2 bottom-0';
  } else if (isLast) {
    return 'h-1/2 top-0';
  } else {
    return 'h-full top-0 ';
  }
}

type VisitProps = {
  visits: Visit[];
  scrollProps: ScrollProps;
};

const VisitsList = ({ visits, scrollProps }: VisitProps) => {
  const setActiveVisit = useSchedulingStore((state) => state.setActiveVisit);
  const activeVisitId = useSchedulingStore((state) => state.activeVisitId);
  const caregivers = useSchedulingStore((state) => state.caregivers);
  const clients = useSchedulingStore((state) => state.clients);
  const setClients = useSchedulingStore((state) => state.setClients);
  // Use record with string keys and nullable HTMLDivElement values
  const visitRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const [filteredVisits, setFilteredVisits] = useState<Visit[]>(visits);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await ApiClient.clientApi.clients.getClientsClientsGet();
        setClients(response.data);
      } catch (err) {
        console.log(err);
      }
    };
    fetchData();
  }, []);
  useEffect(() => {
    const ref = visitRefs.current[String(activeVisitId)];
    if (ref) {
      ref.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, [activeVisitId]);

  const toggleVisit = (id: number) => {
    if (activeVisitId === id) {
      setActiveVisit(null);
      return;
    }
    setActiveVisit(id);
  };

  useEffect(() => {
    setFilteredVisits([...visits]);
  }, [visits]);

  // const fetchData = useCallback(
  //   (params?: { searchText: string; filters: unknown }) => {
  //     const search = (params?.searchText || '').toLowerCase();

  //     const filtered = search
  //       ? visits.filter(({ client }) => {
  //           if (typeof client === 'object' && client !== null) {
  //             // Compose full name from firstName + lastName (fallback to empty string if null/undefined)
  //             const fullName = `${client ?? ''} ${client ?? ''}`.toLowerCase();
  //             return fullName.includes(search.toLowerCase());
  //           }
  //           return false; // client is a number or not an object, so no match
  //         })
  //       : [...visits];

  //     setFilteredVisits(filtered);
  //   },
  //   [visits]
  // );
  function getLatLngFromClient(client: Client | number | undefined): [number, number] | null {
    if (!client || typeof client === 'number') return null; // If just ID or undefined, no lat/lng
    const { geoLat, geoLng } = client;
    if (typeof geoLat === 'number' && typeof geoLng === 'number') {
      return [geoLat, geoLng];
    }
    return null;
  }

  return (
    <div className="flex flex-col px-2 gap-2">
      <SearchContainer
        fetchData={ApiClient.visitsApi.visits.searchVisitsVisitsSearchPost}
        FiltersComponent={VisitFilters}
        placeholder="Search visits..."
        title={
          <div className="flex justify-between">
            {visits.length > 0 && (
              <div>
                {/* <strong>{dayjs(visits[0].startTime).format('D') + ' '}</strong> */}
                {/* {dayjs(visits[0].startTime).format('MMMM')} */}
              </div>
            )}
            <div>
              <strong>{visits.length} </strong> today
            </div>
          </div>
        }
      />
      <SimpleBar
        forceVisible={scrollProps.forceVisible}
        autoHide={scrollProps.autoHide}
        style={scrollProps.style}
        className={scrollProps.className}
      >
        <div className="pr-1">
          {filteredVisits.map((visit, index) => {
            const { id, client, caregiver, startTime, endTime, services } = visit;
            const isActive = activeVisitId === id;
            console.log(clients);
            const caregiverCurrentVisit = caregivers.find((c: Caregiver) => c.caregiverId === caregiver);
            const clientCurrentVist = clients.find((t: Client) => t.clientId === client);
            // Get coordinates safely
            const latlng = getLatLngFromClient(client);

            // Get full name safely
            // const fullName =
            //   typeof client === 'object' && client !== null
            //     ? `${client ?? ''} ${client ?? ''}`.trim() || 'Unknown Client'
            //     : client;

            return (
              <div
                className="relative flex w-full min-h-full items-stretch"
                key={id}
                ref={(el) => (visitRefs.current[String(id)] = el)}
              >
                <div className="w-12 text-xs flex flex-col justify-center text-right pr-2 ">
                  <div className="text-app-text-dark">{dayjs(startTime).format('HH:mm')}</div>
                  <div className="text-app-text-light"> {dayjs(endTime).format('HH:mm')}</div>
                </div>

                <div className="relative w-7">
                  <div
                    className={`w-0.5 border-l-1 border-dashed border-app-primary-200 absolute left-1/2 -translate-x-1/2 z-0 ${VerticalLineStyling(
                      index,
                      visits.length
                    )}`}
                  ></div>
                  <div className="absolute top-1/2 left-1/2 w-6 h-6 rounded-full bg-app-primary-100/50 -translate-x-1/2 -translate-y-1/2"></div>
                  <div className="absolute top-1/2 left-1/2 w-3 h-3 rounded-full bg-app-secondary-light/75 -translate-x-1/2 -translate-y-1/2"></div>
                </div>

                <div className="pl-2 py-2 w-full">
                  <div
                    className={`visit-item rounded-md py-2 px-4 cursor-pointer transition-all duration-300 ${
                      isActive
                        ? 'bg-app-primary-500 text-app-gray'
                        : 'bg-app-primary-600/10 text-app-text-dark hover:bg-app-primary-600/25'
                    }`}
                    onClick={() => toggleVisit(id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="text-lg capitalize">
                        {clientCurrentVist?.firstName} {clientCurrentVist?.lastName}
                      </div>
                      <MdOutlineKeyboardArrowDown
                        className={`transition-transform duration-300 ${isActive ? 'rotate-180' : ''}`}
                      />
                    </div>

                    <div
                      className={`flex items-center gap-2 mt-1 text-sm ${
                        isActive ? 'text-app-gray' : 'text-app-text-dark'
                      }`}
                    >
                      <GrLocation />
                      <span>
                        {visit.street},{visit.city},{visit.postalCode}
                        {/* {latlng ? `(${latlng[0].toFixed(4)}, ${latlng[1].toFixed(4)})` : 'No location available'} */}
                      </span>
                    </div>

                    <div
                      className={`transition-all overflow-hidden duration-500 ${
                        isActive ? 'max-h-[1000px]' : 'max-h-0'
                      } mt-2`}
                    >
                      <div className="pt-2">
                        {services.length > 0 && (
                          <div className="my-2">
                            <div className="text-base font-semibold">Υπηρεσίες</div>
                            {services.map((t) => {
                              // const serviceObj = typeof t.service === 'object' && t.service !== null ? t.service : null;

                              return (
                                <div className="pl-2 text-sm" key={t.id}>
                                  {t ? `${t.notes} - 45 λεπτά` : 'Άγνωστη υπηρεσία'}
                                </div>
                              );
                            })}
                          </div>
                        )}

                        <div className="my-2">
                          <div className="text-base font-semibold">Σημειώσεις</div>
                          <textarea className="w-full h-20 border border-slate-400 border-dashed rounded-md" />
                        </div>

                        <p className="text-sm mt-2">
                          {new Date(startTime).toLocaleString()} - {new Date(endTime).toLocaleString()}
                        </p>

                        {latlng && (
                          <div className="my-3">
                            <div
                              className="mt-2 text-app-primary-600 underline text-sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                window.open(`https://www.google.com/maps?q=${latlng[0]},${latlng[1]}`, '_blank');
                              }}
                            >
                              <iframe
                                className="w-full h-40 rounded-md border pointer-events-none"
                                loading="lazy"
                                src={`https://maps.google.com/maps?q=${latlng[0]},${latlng[1]}&z=15&output=embed`}
                                title="Χάρτης"
                              ></iframe>
                            </div>
                          </div>
                        )}

                        {caregiver && (
                          <div className="flex justify-center items-center gap-2 mt-2 w-full">
                            <VisitAvatar
                              fullname={getCaregiverFullName(caregiverCurrentVisit ? caregiverCurrentVisit : null)}
                            />
                            <span className="">
                              {getCaregiverFullName(caregiverCurrentVisit ? caregiverCurrentVisit : null)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </SimpleBar>
    </div>
  );
};

export default memo(VisitsList);
