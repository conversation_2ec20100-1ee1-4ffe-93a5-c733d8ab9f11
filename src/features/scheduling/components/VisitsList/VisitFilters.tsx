import { Visit } from '@api/READ_ONLY/visits_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { filtersFormConfig } from './form.config';

const VisitFilters = () => {
  const { openNotification } = useNotifications();

  const onSubmit = async (formData: Visit) => {
    try {
      console.log(formData);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };

  return (
    <>
      <FormWrapper fields={filtersFormConfig} onSubmit={onSubmit} withTabs={false} extraClasses="" />
    </>
  );
};

export default VisitFilters;
