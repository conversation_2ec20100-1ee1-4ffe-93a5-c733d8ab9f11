import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';

import timeGridPlugin from '@fullcalendar/timegrid';
import { Popover } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/en-gb';
import { memo } from 'react';
import './avCalendar.scss';
const events = [
  // Shifts (green background)
  {
    title: 'r',
    start: '2025-07-10T11:00:00',
    end: '2025-07-10T16:00:00',
    display: 'background',
    backgroundColor: 'var(--color-app-success)',
    borderColor: 'green',
  },
  {
    title: 'Caregiver Name',
    start: '2025-07-10T18:00:00',
    end: '2025-07-10T22:00:00',
    display: 'background',
    backgroundColor: 'var(--color-app-success)',
    borderColor: 'green',
  },
  // Unavailable slots (red overlays)
  {
    title: 'b',
    start: '2025-07-10T12:00:00',
    end: '2025-07-10T13:00:00',
    backgroundColor: 'var(--color-app-danger)',
    borderColor: 'red',
  },
  {
    title: 'c',
    start: '2025-07-10T15:00:00',
    end: '2025-07-10T15:40:00',
    backgroundColor: 'var(--color-app-danger)',
    borderColor: 'red',
  },
  {
    title: 'd',
    start: '2025-07-10T18:00:00',
    end: '2025-07-10T19:00:00',
    backgroundColor: 'var(--color-app-danger)',
    borderColor: 'red',
  },
].map((e) => {
  const now = dayjs();
  const start = dayjs(e.start);
  const end = dayjs(e.end);

  const makeToday = (original: Dayjs) =>
    now
      .set('hour', original.hour())
      .set('minute', original.minute())
      .set('second', original.second())
      .set('millisecond', original.millisecond());

  return {
    ...e,
    start: makeToday(start).toISOString(),
    end: makeToday(end).toISOString(),
  };
});

const CaregiverAvailabilityCalendar = ({ height }: { height: number }) => {
  const commonCalendarProps = {
    expandRows: true,
    nowIndicator: true,
    slotMinTime: '06:00:00',
    slotMaxTime: '30:00:00',
    slotDuration: '00:30:00',
    slotEventOverlap: false,
    headerToolbar: false as const,
    allDaySlot: false,
    height,
    slotLabelContent: () => null,
  };

  return (
    // <Popover
    //   title="Calendar Info"
    //   content={<div className="max-w-72">This calendar shows daily availability for the selected caregiver.</div>}
    //   trigger="hover" // or "click"
    //   placement="top"
    // >
    //   <div className="relative">
    <FullCalendar
      viewClassNames={'availability-wrapper'}
      plugins={[dayGridPlugin, interactionPlugin, timeGridPlugin]}
      initialView="timeGridDay"
      events={events}
      eventDisplay="block"
      eventOverlap={true}
      moreLinkClassNames="!bg-neutral-100 hover:!bg-neutral-200 w-full text-center hover:text-slate-800 !rounded-full hover:shadow"
      eventClassNames="rounded-xl overflow-hidden !border-none"
      // Render background and overlay events
      eventContent={(arg) => {
        const event = arg.event;

        return (
          <Popover
            content={
              <div className=" flex flex-col items-center justify-center gap-1 bg-app-secondary-extra-light py-2 px-4 rounded-xl">
                <div className="text-app-text-dark">{event.title}</div>
                <div className="text-app-text-light text-xs">
                  {' '}
                  {event.start?.toLocaleTimeString()} - {event.end?.toLocaleTimeString()}
                </div>
                <div className="text-app-gray-light bg-app-primary-500 px-2 py-1 rounded-xl text-xs mt-1">
                  {'Caregiver Name'}
                </div>
              </div>
            }
            trigger="hover"
            placement="top"
            styles={{
              body: {
                padding: 0,
                backgroundColor: 'transparent',
                boxShadow: '0 6px 4px  rgba(0, 0, 0, 0.2)',
                borderRadius: '15px',
              },
            }}
          >
            <div className=" w-full h-full"></div>
          </Popover>
        );
      }}
      {...commonCalendarProps}
    />
    //   </div>
    // </Popover>
  );
};

export default memo(CaregiverAvailabilityCalendar);
