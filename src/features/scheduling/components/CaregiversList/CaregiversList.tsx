import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { ScrollProps } from '@app/types/ui.types';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { memo, useEffect, useState } from 'react';
import SimpleBar from 'simplebar-react';
import SearchContainer from '../VisitsList/shared/SearchContainer';
import DraggableCaregiver from './DraggableCaregiver';

type CaregiverProps = {
  caregivers: Caregiver[];
  scrollProps: ScrollProps;
};

const CaregiversList = ({ caregivers = [], scrollProps }: CaregiverProps) => {
  const activeVisitId = useSchedulingStore((state) => state.activeVisitId);
  const activeCaregiver = useSchedulingStore((state) => state.activeCaregiver);
  const setActiveCaregiver = useSchedulingStore((state) => state.setActiveCaregiver);

  const [filteredCaregivers, setFilteredCaregivers] = useState(caregivers);

  useEffect(() => {
    setFilteredCaregivers([...caregivers]);
  }, [caregivers]);

  // console.log('TEST', caregivers, filteredCaregivers);

  // type FilterParams = {
  //   searchText?: string;
  //   filters?: Record<string, unknown>; // or a more specific type if you have one
  // };

  // const fetchData = useCallback((params?: FilterParams) => {
  //   const search = (params?.searchText || '').toLowerCase();

  //   const filtered = search ? caregivers.filter((c) => c.firstName.toLowerCase().includes(search)) : [...caregivers];

  //   setFilteredCaregivers(filtered);
  // }, []); //caregivers

  return (
    <div className="flex flex-col gap-2 h-full">
      <SearchContainer
        // fetchData={fetchData}
        placeholder="Search caregivers..."
        title={<div className="flex justify-center">Caregivers</div>}
      />
      <SimpleBar
        forceVisible={scrollProps.forceVisible}
        autoHide={scrollProps.autoHide}
        style={scrollProps.style}
        className={scrollProps.className}
      >
        <div className="flex flex-col gap-1 h-full pr-1">
          {filteredCaregivers.map((caregiver) => (
            <DraggableCaregiver
              key={caregiver.caregiverId}
              caregiver={caregiver}
              isActive={caregiver.caregiverId === activeCaregiver}
              onClick={() => setActiveCaregiver(caregiver.caregiverId)}
            />
          ))}
          {filteredCaregivers.length === 0 && (
            <div className="flex h-[300px] justify-center items-center">
              {!activeVisitId ? <div>You need to select an event</div> : <>No caregiver available</>}
            </div>
          )}
        </div>
      </SimpleBar>
    </div>
  );
};

export default memo(CaregiversList);
