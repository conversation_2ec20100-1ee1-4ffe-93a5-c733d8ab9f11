import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import AppAvatar from '@app/components/ui/Avatar';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { memo, Ref, useEffect } from 'react';
import { ConnectDragSource, useDrag } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';

type Props = {
  caregiver: CaregiverWithServices;
  isActive: boolean;
  onClick: () => void;
};

const DraggableCaregiver = ({ caregiver, isActive, onClick }: Props) => {
  const [{ isDragging }, dragRef, preview] = useDrag(() => ({
    type: 'CAREGIVER',
    item: { caregiver },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  // Use empty image as preview so CustomDragLayer handles the visual
  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  const fullname = `${caregiver.firstName} ${caregiver.lastName}`;
  const fullnameLetters = getInitials(fullname, { fallback: '' });

  return (
    <div
      ref={dragRef as unknown as Ref<HTMLDivElement> & ConnectDragSource}
      className={`visit-item rounded-2xl px-2  cursor-move transition flex gap-2 items-center py-4  ${
        isDragging
          ? 'opacity-50'
          : isActive
            ? 'opacity-15 bg-app-secondary-light text-app-text'
            : 'bg-app-gray-light hover:bg-app-primary text-app-text hover:text-app-gray-light'
      } `}
      onClick={() => {
        onClick();
      }}
    >
      <AppAvatar isSelected={true} fullName={fullname} value={fullnameLetters} />
      <span className="truncate">{`${caregiver.firstName}, ${caregiver.lastName}`}</span>
    </div>
  );
};

export default memo(DraggableCaregiver);
