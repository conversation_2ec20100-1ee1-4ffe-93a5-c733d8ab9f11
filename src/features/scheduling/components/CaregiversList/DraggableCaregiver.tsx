import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import VisitAvatar from '@feat-scheduling/components/Avatar/VisitAvatar';
import { memo } from 'react';
import { useDrag } from 'react-dnd';

type Props = {
  caregiver: Caregiver;
  isActive: boolean;
  onClick: () => void;
};

const DraggableCaregiver = ({ caregiver, isActive, onClick }: Props) => {
  const [{ isDragging }, dragRef] = useDrag(() => ({
    type: 'CAREGIVER',
    item: { caregiver },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={dragRef}
      className={`visit-item rounded-2xl px-2  cursor-move transition flex gap-2 items-center py-4  ${
        isDragging
          ? 'opacity-50'
          : isActive
            ? ' bg-app-secondary-light text-app-text'
            : 'bg-app-gray-light hover:bg-app-primary text-app-text hover:text-app-gray-light'
      } `}
      onClick={() => {
        onClick();
      }}
    >
      <VisitAvatar fullname={caregiver.firstName + caregiver.lastName} />
      <span className="truncate">{caregiver.firstName + ' ' + caregiver.lastName}</span>
    </div>
  );
};

export default memo(DraggableCaregiver);
