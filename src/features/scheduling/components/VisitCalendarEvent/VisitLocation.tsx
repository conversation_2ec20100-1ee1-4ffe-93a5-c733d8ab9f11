type VisitLocationProps = {
  street: string;
  city: string;
  postalCode: string;
};

/**
 * Component that displays visit location information
 */
export function VisitLocation({ street, city, postalCode }: VisitLocationProps) {
  const hasLocation = street || city || postalCode;

  if (!hasLocation) {
    return <div className="text-sm">Χωρίς διεύθυνση</div>;
  }

  return (
    <div className="text-sm flex gap-1">
      {street && <span>{street},</span>}
      {city && <span>{city},</span>}
      {postalCode && <span>{postalCode}</span>}
    </div>
  );
}
