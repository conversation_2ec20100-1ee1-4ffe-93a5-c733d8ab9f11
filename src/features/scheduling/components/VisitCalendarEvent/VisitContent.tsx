import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { Visit } from '@api/READ_ONLY/visits_api/Api';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import React from 'react';

type Props = {
  visit: Visit;
};

const VisitContent: React.FC<Props> = ({ visit }) => {
  const clients = useSchedulingStore((state) => state.clients);
  const caregivers = useSchedulingStore((state) => state.caregivers);

  if (!visit) return null;

  // Assuming client could be an object with fullName or just an id (number)
  // const clientFullname =
  //   typeof visit.client === 'object' && visit.client !== null
  //     ? `${visit.client ?? ''} ${visit.client ?? ''}`.trim() || 'Unknown Client'
  //     : 'Unknown Client';
  const clientCurrentVist = clients.find((t: Client) => t.clientId === visit.client);
  const caregiverCurrentVisit = caregivers.find((c: Caregiver) => c.caregiverId === visit.caregiver);

  // Address is not in Visit interface — if you want it, maybe client or caregiver has it?
  // For now, fallback:
  // const address = 'Not Scheduled'; // Or extract from visit.client if available

  // const caregiverName =
  //   visit.caregiver && typeof visit.caregiver === 'object'
  //     ? `${visit.caregiver ?? ''} ${visit.caregiver ?? ''}`.trim() || 'Unassigned'
  //     : 'Unassigned';

  return (
    <div className="flex flex-col items-center justify-center gap-1 bg-app-secondary-extra-light py-2 px-4 rounded-xl">
      <div className="text-app-text-dark">
        {clientCurrentVist?.firstName} {clientCurrentVist?.lastName}
      </div>
      <div className="text-app-text-light text-xs">
        {visit.street},{visit.city},{visit.postalCode}
      </div>
      <div className="text-app-gray-light bg-app-primary-500 px-2 py-1 rounded-xl text-xs mt-1">
        {caregiverCurrentVisit?.firstName} {caregiverCurrentVisit?.lastName}
      </div>
    </div>
  );
};

export default VisitContent;
