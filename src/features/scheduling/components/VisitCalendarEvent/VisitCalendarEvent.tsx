import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { Visit } from '@api/READ_ONLY/visits_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import VisitAvatar from '@feat-scheduling/components/Avatar/VisitAvatar';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Popover } from 'antd';
import { memo, useState } from 'react';
import { useDrop } from 'react-dnd';
import { GoPlus } from 'react-icons/go';
import VisitContent from './VisitContent';

const VisitCalendarEvent = (visit: Visit) => {
  const activeVisitId = useSchedulingStore((state) => state.activeVisitId);
  const setActiveVisitId = useSchedulingStore((state) => state.setActiveVisit);
  const caregivers = useSchedulingStore((state) => state.caregivers);
  const { openNotification } = useNotifications();

  const caregiverCurrentVisit = caregivers.find((c: Caregiver) => c.caregiverId === visit.caregiver);
  const updateCaregiverOnVisit = async (id: number, data: Visit) => {
    try {
      await ApiClient.visitsApi.visits.updateVisitVisitsVisitIdPut(id, data);
      openNotification('topRight', {
        title: `Visit`,
        description: 'Visit updated successfully. ',
        type: 'Success',
      });
    } catch (error) {
      openNotification('topRight', {
        title: `Visit`,
        description: 'Visit failed to update.',
        type: 'Warning',
      });
    }
  };
  const [{ isOver, canDrop }, dropRef] = useDrop(
    () => ({
      accept: 'CAREGIVER',
      drop: (item: { caregiver: Caregiver }) => {
        // console.log('DROP', visit.id, activeVisitId);

        updateCaregiverOnVisit(visit.id, { ...visit, caregiver: item.caregiver.caregiverId });
        console.log('💡 Dropped caregiver', item.caregiver.caregiverId, 'on visit', visit.id);
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    }),
    [visit.id, activeVisitId] //active visit did not update without these dependencies
  );

  const isActive = Number(activeVisitId) === visit.id;
  const isDroppable = isOver && canDrop;

  const [hoveredVisit, setHoveredVisit] = useState<Visit | null>(null);
  const handleMouseEnter = () => setHoveredVisit(visit);
  const handleMouseLeave = () => setHoveredVisit(null);
  console.log('Visit', visit);
  // const caregiverName =
  //   visit.caregiver && typeof visit.caregiver === 'object'
  //     ? `${visit.caregiver ?? ''} ${visit.caregiver ?? ''}`.trim() || 'Unassigned'
  //     : 'Unassigned';
  return (
    <Popover
      placement="top"
      // open={true}
      // title={'Visit Details'}
      motion={{ visible: false }}
      content={<VisitContent visit={hoveredVisit ?? visit} />}
      arrow={false}
      styles={{
        body: {
          padding: 0,
          backgroundColor: 'transparent',
          boxShadow: '0 6px 4px  rgba(0, 0, 0, 0.2)',
          borderRadius: '15px',
        },
      }}
    >
      <div
        ref={dropRef}
        className={`w-full border border-app-gray-dark rounded-xl h-full flex items-center justify-center group transition-colors duration-200 
        ${isDroppable ? 'bg-app-success/50' : isActive ? 'bg-app-secondary-light' : 'bg-app-gray hover:bg-app-gray-dark'}`}
        onClick={() => setActiveVisitId(visit.id)}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div>
          {visit.caregiver ? (
            <VisitAvatar
              fullname={
                caregiverCurrentVisit
                  ? caregiverCurrentVisit.firstName + ' ' + caregiverCurrentVisit.lastName
                  : 'Unassigned'
              }
            />
          ) : (
            <div className="w-full h-full max-w-[20px] max-h-[20px] border border-app-text-dark text-app-text-dark border-dashed flex items-center justify-center rounded-full transition-transform duration-200 group-hover:scale-150">
              <GoPlus />
            </div>
          )}
          {/* Optional debug: <div>{isActive ? 'ACTIVE' : ''}</div> */}
        </div>
      </div>
    </Popover>
  );
};

export default memo(VisitCalendarEvent);
