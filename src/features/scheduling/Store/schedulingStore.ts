import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { Service, ServiceType } from '@api/READ_ONLY/services_api/Api';
import { Visit } from '@api/READ_ONLY/visits_api/Api';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

type VisitStore = {
  visits: Visit[];
  caregivers: Caregiver[];
  clients: Client[];
  availableCaregivers: Caregiver[];
  services: Service[];
  serviceTypes: ServiceType[];
  activeVisitId: number | null;
  activeCaregiver: number | null;

  loadingClients: boolean;
  loadingVisits: boolean;
  loadingServices: boolean;
  loadingServiceTypes: boolean;
  loadingCaregivers: boolean;

  setVisits: (visits: Visit[]) => void;
  setCaregivers: (caregivers: Caregiver[]) => void;
  setServices: (services: Service[]) => void;
  setClients: (clients: Client[]) => void;
  setServiceTypes: (serviceTypes: ServiceType[]) => void;
  setActiveCaregiver: (id: number | null) => void;
  setActiveVisit: (id: number | null) => void;
  assignCaregiver: (visitId: number, caregiver: Caregiver) => void;

  setLoadingClients: (val: boolean) => void;
  setLoadingVisits: (val: boolean) => void;
  setLoadingServices: (val: boolean) => void;
  setLoadingServiceTypes: (val: boolean) => void;
  setLoadingCaregivers: (val: boolean) => void;
};

export const useSchedulingStore = create<VisitStore>()(
  devtools((set, get) => ({
    visits: [], // load these as needed
    caregivers: [],
    clients: [],
    availableCaregivers: [],
    serviceTypes: [],
    services: [],
    activeVisitId: null,
    activeCaregiver: null,
    loadingClients: false,
    setLoadingClients: (val) => set({ loadingClients: val }),
    loadingVisits: false,
    setLoadingVisits: (val) => set({ loadingVisits: val }),
    loadingServices: false,
    setLoadingServices: (val) => set({ loadingServices: val }),
    loadingServiceTypes: false,
    setLoadingServiceTypes: (val) => set({ loadingServiceTypes: val }),
    loadingCaregivers: false,
    setLoadingCaregivers: (val) => set({ loadingCaregivers: val }),
    setClients: (clients) => set({ clients }),
    setVisits: (visits) => set({ visits }),
    setCaregivers: (caregivers) => set({ caregivers }),
    setServices: (services) => set({ services }),
    setActiveCaregiver: (activeCaregiver) => set({ activeCaregiver }),
    setServiceTypes: (serviceTypes) => set({ serviceTypes }),
    setActiveVisit: (id) => {
      const { visits, caregivers } = get();
      const selectedVisit = visits.find((v) => String(v.id) === String(id));

      if (!selectedVisit) {
        set({ activeVisitId: null, availableCaregivers: [] });
        return;
      }
      const requiredServiceIds = selectedVisit.services.map((t) => t.id);

      const matchedCaregivers = caregivers.filter((cg) => {
        const caregiverServiceIds =
          cg?.services?.map((service) => (typeof service === 'number' ? service : service.serviceId)) ?? [];
        if (caregiverServiceIds) return requiredServiceIds.every((tid) => caregiverServiceIds.includes(tid));
      });

      set({
        activeVisitId: id,
        availableCaregivers: matchedCaregivers,
      });
    },

    assignCaregiver: (visitId: number, caregiver: Caregiver) =>
      set((state) => ({
        visits: state.visits.map((visit) =>
          Number(visit.id) === visitId ? { ...visit, caregiver: caregiver.caregiverId } : visit
        ),
      })),
  }))
);
