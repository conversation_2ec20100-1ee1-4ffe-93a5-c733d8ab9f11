// import { caregivers } from '@app/mock/caregivers';
import { ApiClient } from '@api/api-configuration';
import CaregiversList from '@feat-scheduling/components/CaregiversList/CaregiversList';
// import { overlappingEvents } from '@feat-scheduling/components/LeaveCalendar/events.demo';
import LeaveCalendar from '@feat-scheduling/components/LeaveCalendar/LeaveCalendar';
import VisitsList from '@feat-scheduling/components/VisitsList/VisitsList';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import useResizeObserver from '@react-hook/resize-observer';
import dayjs from 'dayjs';
import { memo, useEffect, useRef, useState } from 'react';

const ListHeader = 0; //(padddings we need //TODO check how to add these values dynamically so we can ref them and substract them)
const calendarHeaderHeight = 32 + 16; //TODO: check if we can get this dynamically

const SchedulingContainer = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  const setCaregivers = useSchedulingStore((state) => state.setCaregivers);
  const setVisits = useSchedulingStore((state) => state.setVisits);
  const setServices = useSchedulingStore((state) => state.setServices);

  const visits = useSchedulingStore((state) => state.visits);
  const caregivers = useSchedulingStore((state) => state.caregivers);
  // const availableCaregivers = useSchedulingStore((state) => state.availableCaregivers);

  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [currentDate, setCurrentDate] = useState(dayjs());

  const listHeights = dimensions.height - ListHeader || 0;
  const calendarHeight = dimensions.height - calendarHeaderHeight || 0;

  useResizeObserver(containerRef, (entry) => {
    setDimensions({
      width: entry.contentRect.width,
      height: entry.contentRect.height,
    });
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [services, caregivers] = await Promise.all([
          ApiClient.serviceApi.services.getServicesServicesGet({ offset: 0, limit: 100 }),

          ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet(),
        ]);

        setServices(services.data);
        setCaregivers(caregivers.data);
      } catch (error) {
        console.error('Error fetching scheduling data', error);
      }
    };

    fetchData();
  }, []);
  useEffect(() => {
    const fetchData = async () => {
      const start_time_from = dayjs(currentDate).startOf('day').toISOString(); // today 00:00
      const start_time_to = dayjs(currentDate).endOf('day').toISOString(); // today 23:59
      try {
        const visits = await ApiClient.visitsApi.visits.getAllVisitsEndpointVisitsGet({
          start_time_from: start_time_from,
          start_time_to: start_time_to,
        });
        setVisits(visits.data);
      } catch (error) {
        console.error('Error fetching scheduling data', error);
      }
    };
    fetchData();
  }, [currentDate]);
  console.log('Width', dimensions.width);
  console.log('visits', visits);
  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex w-full h-full" ref={containerRef}>
        <div className="basis-[30%] flex-grow-0 flex-shrink-0 h-full overflow-hidden">
          {/* <div className="text-center font-semibold mb-4">Visits</div> */}
          <VisitsList
            visits={visits}
            scrollProps={{
              forceVisible: true,
              autoHide: true,
              style: { maxHeight: listHeights },
              className: 'pr-2',
            }}
          />
        </div>
        <div className="basis-[50%] flex-grow-0 flex-shrink-0">
          <LeaveCalendar
            currentDate={currentDate}
            setCurrentDate={setCurrentDate}
            height={calendarHeight}
            visits={visits}
          />
        </div>
        <div className="basis-[15 %] flex-grow-0 pl-2 flex-shrink-0 h-full overflow-hidden">
          {/* <div className="text-center font-semibold mb-4">Caregivers</div> */}
          <CaregiversList
            caregivers={caregivers}
            scrollProps={{
              forceVisible: true,
              autoHide: true,
              style: { maxHeight: listHeights },
              className: 'pl-2',
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(SchedulingContainer);
