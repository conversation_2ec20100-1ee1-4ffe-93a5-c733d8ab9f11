import { FormField } from '@app/features/form/Components/FormFields/FormFields';
import { FieldConfig, FieldGroup } from '@app/types/form.types';
import { <PERSON><PERSON>, Tabs } from 'antd';
import { DefaultValues, FieldErrors, FieldValues, FormProvider, SubmitHandler, useForm } from 'react-hook-form';

function getColSpan(width: string = 'full') {
  switch (width) {
    case 'third':
      return 'col-span-12 sm:col-span-4';
    case 'half':
      return 'col-span-12 sm:col-span-6';
    case 'twoThirds':
      return 'col-span-12 sm:col-span-8';
    case 'full':
    default:
      return 'col-span-12';
  }
}

type FormProps<T extends FieldValues> = {
  fields: FieldGroup[];
  onSubmit: SubmitHandler<T>;
  defaultValues?: DefaultValues<T>;
  withTabs?: boolean;
  extraClasses?: string;
};

export function FormWrapper<T extends FieldValues>({
  fields,
  onSubmit,
  defaultValues,
  withTabs = true,
  extraClasses = 'min-h-screen',
}: FormProps<T>) {
  const methods = useForm<T>({ defaultValues });

  const errors = methods.formState.errors as FieldErrors<T>;

  const hasGroupError = (group: FieldGroup) =>
    group.fields.some((field: FieldConfig<unknown>) => {
      const keys = field.name.split('.');
      let err: unknown = errors;
      for (const key of keys) {
        if (!err || typeof err !== 'object' || !(key in (err as object))) return false;
        err = (err as Record<string, unknown>)[key];
      }
      return !!err && typeof err === 'object' && 'type' in (err as object);
    });

  const renderGroupFields = (group: FieldGroup) => (
    <div className="grid grid-cols-12 gap-4 sm:gap-6" key={group.title}>
      {group.fields.map((field) => (
        <div key={field.name} className={getColSpan(field.width)}>
          <FormField field={field} />
        </div>
      ))}
    </div>
  );

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)} // Properly wired to RHF
        className={`bg-white p-4 sm:p-6 overflow-y-auto ${extraClasses}`}
      >
        {withTabs ? (
          <Tabs defaultActiveKey="0" type="card">
            {fields.map((group, i) => (
              <Tabs.TabPane
                forceRender
                tab={
                  <span
                    style={{
                      color: hasGroupError(group) ? 'orange' : undefined,
                    }}
                  >
                    {group.title || `Section ${i + 1}`}
                  </span>
                }
                key={group.title || `section-${i}`}
              >
                {renderGroupFields(group)}
              </Tabs.TabPane>
            ))}
          </Tabs>
        ) : (
          <div className="space-y-8">
            {fields.map((group, i) => (
              <div key={group.title || `section-${i}`}>
                {group.title && <h2 className="text-lg font-semibold mb-4">{group.title}</h2>}
                {renderGroupFields(group)}
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <Button type="primary" htmlType="submit" size="large">
            Save
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}
