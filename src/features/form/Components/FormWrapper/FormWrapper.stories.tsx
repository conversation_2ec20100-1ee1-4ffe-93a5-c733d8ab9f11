import { FieldGroup } from '@app/types/form.types';
import { <PERSON>a, StoryObj } from '@storybook/react-vite';
import React from 'react';
import { FormWrapper } from './FormWrapper';

// Define two sets of fields to toggle between
const FieldSets: Record<string, FieldGroup[]> = {
  default: [
    {
      title: 'Personal Information',
      fields: [
        {
          name: 'firstName',
          label: 'First Name',
          type: 'text',
          width: 'third',
          rules: { required: 'First name is required' },
        },
        {
          name: 'lastName',
          label: 'Last Name',
          type: 'text',
          width: 'third',
          rules: { required: 'Last name is required' },
        },
        {
          name: 'email',
          label: 'Email',
          type: 'email',
          width: 'third',
        },
      ],
    },
    {
      title: 'Address',
      fields: [
        {
          name: 'address.street',
          label: 'Street',
          type: 'text',
          width: 'full',
        },
        {
          name: 'address.city',
          label: 'City',
          type: 'text',
          width: 'half',
        },
        {
          name: 'address.zipCode',
          label: 'Zip Code',
          type: 'number',
          width: 'half',
        },
      ],
    },
    {
      title: 'Preferences',
      fields: [
        {
          name: 'newsletter',
          label: 'Subscribe to Newsletter',
          type: 'checkbox',
          width: 'full',
        },
      ],
    },
  ],
  custom: [
    {
      title: 'Custom Section',
      fields: [
        {
          name: 'customField',
          label: 'Custom Field',
          type: 'text',
          width: 'full',
          rules: { required: 'This is required' },
        },
        {
          name: 'customDate',
          label: 'Custom Date',
          type: 'date',
          width: 'half',
        },
        {
          name: 'customChoice',
          label: 'Custom Choice',
          type: 'select',
          width: 'half',
          options: [
            { label: 'Option A', value: 'A' },
            { label: 'Option B', value: 'B' },
          ],
        },
      ],
    },
  ],
};

type StoryProps = {
  fieldSet: 'default' | 'custom';
  onSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
};

// Wrapper that switches fields dynamically based on `fieldSet` prop
const DynamicFormWrapper = ({ fieldSet, onSubmit }: StoryProps) => (
  <FormWrapper fields={FieldSets[fieldSet]} onSubmit={onSubmit} />
);

const meta: Meta<typeof DynamicFormWrapper> = {
  title: 'Components/FormWrapper',
  component: DynamicFormWrapper,
  args: {
    fieldSet: 'default',
    onSubmit: async (e) => {
      if (e) e.preventDefault();
      alert('Form submitted!');
    },
  },
  argTypes: {
    fieldSet: {
      control: 'radio',
      options: ['default', 'custom'],
      description: 'Toggle between default and custom field sets',
    },
    onSubmit: { action: 'submitted' },
  },
};

export default meta;
type Story = StoryObj<typeof DynamicFormWrapper>;

export const Playground: Story = {};
