import { FieldConfig } from '@app/types/form.types';
import { Meta, StoryObj } from '@storybook/react-vite';
import { FormProvider, useForm } from 'react-hook-form';
import { FormField } from './FormFields';

type FormFieldStoryProps = {
  type: FieldConfig['type'];
  field: Omit<FieldConfig, 'type'>;
};

const FormWrapper = ({ type, field }: FormFieldStoryProps) => {
  const mergedField: FieldConfig = { ...field, type };

  const methods = useForm({
    defaultValues: {
      [mergedField.name]: type === 'checkbox' ? false : '',
    },
  });

  return (
    <div className="max-w-md p-6 bg-white shadow rounded">
      <FormProvider {...methods}>
        <form>
          <FormField field={mergedField} />
        </form>
      </FormProvider>
    </div>
  );
};

// ❌ Don't assign Form<PERSON>ield here, because our story is for FormWrapper
const meta: Meta<typeof FormWrapper> = {
  title: 'Components/FormField',
  component: FormWrapper,
  args: {
    type: 'text',
    field: {
      name: 'example',
      label: 'Example Field',
      rules: { required: 'Required' },
      options: [
        { label: 'Option 1', value: '1' },
        { label: 'Option 2', value: '2' },
      ],
    },
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'email', 'password', 'number', 'date', 'select', 'checkbox', 'textarea'],
    },
    field: { control: 'object' },
  },
};

export default meta;

type Story = StoryObj<typeof FormWrapper>;

export const Playground: Story = {};
