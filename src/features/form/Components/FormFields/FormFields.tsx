// src/components/FormField.tsx
import { appTheme } from '@app/styles/app-theme';
import { FieldConfig } from '@app/types/form.types';
import { Checkbox, DatePicker, Input, InputNumber, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import { Controller, ControllerRenderProps, FieldError, useFormContext } from 'react-hook-form';

export function FormField<TValue>({ field }: { field: FieldConfig<TValue> }): JSX.Element {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const renderInput = (controllerField: ControllerRenderProps): JSX.Element => {
    const hasError = Boolean(errors[field.name]);

    switch (field.type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <Input
            size="large"
            {...controllerField}
            type={field.type === 'password' ? 'password' : 'text'}
            status={hasError ? 'error' : undefined}
          />
        );

      case 'number':
        return (
          <InputNumber className="!w-full" size="large" {...controllerField} status={hasError ? 'error' : undefined} />
        );

      case 'list':
        return (
          <Input
            size="large"
            value={controllerField.value ?? ''}
            onChange={(e) => controllerField.onChange(e.target.value)}
            status={hasError ? 'error' : undefined}
          />
        );

      case 'date':
        return (
          <DatePicker
            size="large"
            className="w-full"
            value={controllerField.value ? dayjs(controllerField.value) : null}
            onChange={(date) => controllerField.onChange(date ? date.toDate() : null)}
            onBlur={controllerField.onBlur}
            name={controllerField.name}
            status={hasError ? 'error' : undefined}
          />
        );

      case 'select':
        return (
          <Select
            size="large"
            className="w-full"
            value={controllerField.value}
            onChange={(val) => controllerField.onChange(val)}
            onBlur={controllerField.onBlur}
            options={field.options ?? []}
            allowClear
            status={hasError ? 'error' : undefined}
          />
        );

      case 'textarea':
        return <TextArea rows={4} {...controllerField} status={hasError ? 'error' : undefined} />;

      case 'checkbox':
        return (
          <Checkbox
            checked={!!controllerField.value}
            onChange={(e) => controllerField.onChange(e.target.checked)}
            onBlur={controllerField.onBlur}
            name={controllerField.name}
            className={hasError ? '!border-app-danger' : ''}
          >
            {field.label}
          </Checkbox>
        );

      case 'custom':
        if (!field.component) return <></>; // 👈 return an empty JSX.Element, not null
        return (
          <>
            {field.component({
              value: controllerField.value as TValue,
              onChange: controllerField.onChange as (val: TValue) => void,
              error: hasError ? ((errors[field.name] as FieldError)?.message ?? '') : '',
            })}
          </>
        );

      default:
        return <Input {...controllerField} status={hasError ? 'error' : undefined} size="large" />;
    }
  };

  const hasError = Boolean(errors[field.name]);

  return (
    <>
      {field.type !== 'checkbox' && (
        <label className="text-ellipsis" style={{ fontSize: appTheme.components?.InputNumber?.fontSize }}>
          {field.label}
        </label>
      )}

      <Controller
        name={field.name}
        control={control}
        rules={field.rules}
        render={({ field: controllerField }) => renderInput(controllerField)}
      />

      {field.type !== 'checkbox' && field.type !== 'custom' && hasError && (
        <p className="text-app-danger text-xs mt-1">{(errors[field.name] as FieldError).message}</p>
      )}
    </>
  );
}
