// src/components/FormField.tsx
import { appTheme } from '@app/styles/app-theme';
import { FieldConfig } from '@app/types/form.types';
import { Checkbox, DatePicker, Input, InputNumber, Radio, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { Controller, ControllerRenderProps, FieldError, useFormContext } from 'react-hook-form';

export function FormField({ field }: { field: FieldConfig }) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const renderInput = (controllerField: ControllerRenderProps) => {
    const hasError = errors[field.name];
    const errorClass = hasError ? '!border-app-danger' : '';

    switch (field.type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <Input
            size="large"
            {...controllerField}
            type={field.type === 'password' ? 'password' : 'text'}
            status={hasError ? 'error' : undefined}
          />
        );
      case 'number':
        return (
          <InputNumber className="!w-full" size="large" {...controllerField} status={hasError ? 'error' : undefined} />
        );
      case 'date':
        return (
          <DatePicker
            size="large"
            className=" w-full"
            {...controllerField}
            value={controllerField.value || null}
            onChange={(date) => controllerField.onChange(date)}
            status={hasError ? 'error' : undefined}
          />
        );
      case 'select':
        return (
          <Select
            size="large"
            className="w-full"
            {...controllerField}
            options={field.options}
            onChange={(val) => controllerField.onChange(val)}
            allowClear
            status={hasError ? 'error' : undefined}
          />
        );
      case 'multiple-select':
        return (
          <Select
            size="large"
            mode="multiple"
            {...controllerField}
            options={field.options}
            onChange={(val) => controllerField.onChange(val)}
            allowClear
            status={hasError ? 'error' : undefined}
          />
        );
      case 'textarea':
        return <TextArea rows={4} />;
      case 'checkbox':
        return (
          <Checkbox
            checked={!!controllerField.value}
            onChange={(e) => controllerField.onChange(e.target.checked)}
            className={errorClass}
          >
            {field.label}
          </Checkbox>
        );
      case 'radiogroup':
        return (
          <Radio.Group
            {...controllerField}
            options={field.options}
            onChange={(val) => controllerField.onChange(val)}
            className={errorClass}
          >
            {field.label}
          </Radio.Group>
        );
      default:
        return <Input {...controllerField} status={hasError ? 'error' : undefined} />;
    }
  };

  return (
    <>
      {field.type !== 'checkbox' && (
        <label className="text-ellipsis" style={{ fontSize: appTheme.components?.InputNumber?.fontSize }}>
          {field.label}
        </label>
      )}

      <Controller
        name={field.name}
        control={control}
        rules={field.rules}
        render={({ field: controllerField }) => renderInput(controllerField)}
      />

      {field.type !== 'checkbox' && errors[field.name] && (
        <p className="text-app-danger text-xs mt-1">{(errors[field.name] as FieldError).message}</p>
      )}
    </>
  );
}
