import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { BaseColumn, TableRow } from '@app/features/table/types/core.types';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Tag, Typography } from 'antd';
import { DateTimeDisplay } from '../../../../components/common/DateTimeDisplay';
import { getAddressWithFallback } from '../../../../utils/addressUtils';

export interface VisitRow extends Omit<VisitPopulated, 'id'>, TableRow {
  id: string | number;
}

// ...existing code...

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'scheduled':
      return 'blue';
    case 'in_progress':
      return 'processing';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

export const getVisitTableColumns = (): BaseColumn<VisitRow>[] => [
  {
    id: 'client',
    header: 'Client',
    accessor: (row) => (row.client ? `${row.client.firstName ?? ''} ${row.client.lastName ?? ''}`.trim() : ''),
    width: 180,
    cellRenderer: (_, visit) => (
      <div>
        {visit?.client && (
          <ClientDisplay firstName={visit.client.firstName || ''} lastName={visit.client.lastName || ''} />
        )}
      </div>
    ),
  },
  {
    id: 'caregivers',
    header: 'Caregivers',
    accessor: (row) =>
      row.caregivers ? row.caregivers.map((c) => `${c.firstName ?? ''} ${c.lastName ?? ''}`.trim()).join(', ') : '',
    width: 200,
    cellRenderer: (_, visit) => (
      <>
        {visit?.caregivers?.map((caregiver) => (
          <ClientDisplay
            key={caregiver.caregiverId}
            firstName={caregiver.firstName || ''}
            lastName={caregiver.lastName || ''}
          />
        )) || '-'}
      </>
    ),
  },
  {
    id: 'visitTime',
    header: 'Time',
    accessor: 'startTime',
    align: 'center',
    width: 150,
    cellRenderer: (_, visit) => (
      <DateTimeDisplay
        startTime={visit?.startTime}
        endTime={visit?.endTime}
        showDateSeparately
        timeClassName="text-gray-600"
      />
    ),
  },
  {
    id: 'services',
    header: 'Services',
    accessor: 'services',
    align: 'center',
    width: 150,
    cellRenderer: (_, visit) => (
      <div>
        {visit?.services?.map((service) => (
          <Tag key={service.id} style={{ marginBottom: 2 }}>
            {service.service?.name || 'Service'}
          </Tag>
        )) || '-'}
      </div>
    ),
  },
  {
    id: 'status',
    header: 'Status',
    accessor: 'status',
    align: 'center',
    width: 120,
    cellRenderer: (status) => (
      <Tag color={getStatusColor(status as string)}>{((status as string) || '').replace('_', ' ').toUpperCase()}</Tag>
    ),
  },
  {
    id: 'address',
    header: 'Address',
    accessor: (row) =>
      getAddressWithFallback(
        {
          street: row?.street || undefined,
          city: row?.city || undefined,
          zip: row?.postalCode || undefined,
        },
        row?.client
          ? {
              street: row.client.street || undefined,
              city: row.client.city || undefined,
              zip: row.client.postalCode || undefined,
            }
          : undefined
      ),
    align: 'center',
    width: 200,
    cellRenderer: (_, visit) => {
      const visitAddress = {
        street: visit?.street || undefined,
        city: visit?.city || undefined,
        zip: visit?.postalCode || undefined,
      };
      const clientAddress = visit?.client
        ? {
            street: visit.client.street || undefined,
            city: visit.client.city || undefined,
            zip: visit.client.postalCode || undefined,
          }
        : undefined;
      const formattedAddress = getAddressWithFallback(visitAddress, clientAddress);
      return <Typography.Text ellipsis>{formattedAddress}</Typography.Text>;
    },
  },
  {
    id: 'notes',
    header: 'Notes',
    accessor: 'notes',
    align: 'center',
    width: 200,
    cellRenderer: (notes) => <Typography.Text ellipsis>{String(notes ?? '-')}</Typography.Text>,
  },
  {
    id: 'createdAt',
    header: 'Created',
    accessor: 'createdAt',
    align: 'center',
    width: 150,
    cellRenderer: (date) => (
      <DateTimeDisplay startTime={date as string} showDateSeparately={false} dateFormat="MMM DD, YYYY" />
    ),
  },
];
