import FiltersPanel, { FilterValues } from '@feat-availability/components/FiltersPanel';
import { Button } from 'antd';
import { useState } from 'react';
import { MdFilterList } from 'react-icons/md';
import { RiDragMoveFill } from 'react-icons/ri';

type Props = {
  filters: FilterValues;
  onToggle?: (isOpen: boolean) => void;
  onFiltersChange: (filters: FilterValues) => void;
};

const FloatingFiltersPanel = ({ filters, onFiltersChange, onToggle }: Props) => {
  const [isOpen, setIsOpen] = useState(true);

  const togglePanel = () => {
    setIsOpen(!isOpen);
    onToggle && onToggle(!isOpen);
  };

  return (
    <div className="bg-white/80 rounded-lg shadow-2xl border border-gray-200 transition-all duration-300">
      {/* Container with button and filters in same row */}
      <div className="flex items-center">
        {/* Drag Handle and Toggle Button */}
        <div
          className={`drag-handle flex items-center gap-2 p-2 bg-gradient-to-r from-blue-50 to-indigo-50 cursor-move hover:bg-gradient-to-r hover:from-blue-100 hover:to-indigo-100 transition-colors ${
            isOpen ? 'rounded-l-lg' : 'rounded-lg'
          }`}
          style={{ userSelect: 'none' }}
        >
          {<RiDragMoveFill className="text-gray-500 text-2xl" />}
          <Button
            type="primary"
            shape="circle"
            size="small"
            icon={<MdFilterList />}
            onClick={togglePanel}
            style={{
              width: '40px',
              height: '40px',
              fontSize: '18px',
            }}
            title={isOpen ? 'Close Filters' : 'Open Filters'}
          />
        </div>

        {/* Filters Panel - shown/hidden based on state */}
        {isOpen && (
          <div className="px-2 rounded-r-lg transition-all duration-300 border-l border-gray-100">
            <FiltersPanel {...filters} onFiltersChange={onFiltersChange} />
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingFiltersPanel;
