import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { Service } from '@api/READ_ONLY/services_api/Api';
import CaregiversSelectGeneric from '@feat-caregivers/components/CaregiversSelect';
import ClientSelectGeneric from '@feat-clients/components/ClientSelect';
import ServicesSelectGeneric from '@feat-services/components/ServicesSelect/ServicesSelectGeneric';
import { InputNumber, Space, Tooltip } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { AiOutlineMore } from 'react-icons/ai';

export type FilterValues = {
  patient: Client | undefined;
  services: number[];
  caregivers: number[];
  duration: number;
};

type Props = {
  onFiltersChange: (filters: FilterValues) => void;
} & Partial<FilterValues>;
const FiltersPanel = ({ onFiltersChange, ...props }: Props) => {
  console.log({ props });
  const [filters, setFilters] = useState<FilterValues>({
    patient: props.patient,
    services: props.services || [],
    caregivers: props.caregivers || [],
    duration: props.duration || 30,
  });

  // Emit changes after state updates (prevents infinite loops)
  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  // Memoized setters for each filter type
  const setPatient = useCallback((client: Client | Client[] | undefined) => {
    // Handle the case where ClientSelect might return an array or single client
    const patient = Array.isArray(client) ? client[0] : client;
    setFilters((prev) => ({ ...prev, patient }));
  }, []);

  const setServices = useCallback((services: Service[] | undefined) => {
    const serviceIds = Array.isArray(services) ? services.map((s) => s.serviceId) : [];
    setFilters((prev) => ({ ...prev, services: serviceIds }));
  }, []);

  const setCaregivers = useCallback((caregivers: CaregiverWithServices | CaregiverWithServices[] | undefined) => {
    const caregiverIds = Array.isArray(caregivers) ? caregivers.map((c) => c.caregiverId) : [];
    setFilters((prev) => ({ ...prev, caregivers: caregiverIds }));
  }, []);

  const setDuration = useCallback((duration: number) => {
    setFilters((prev) => ({ ...prev, duration }));
  }, []);

  return (
    <>
      <div className="flex flex-wrap gap-2 w-full">
        {/* Patient */}
        <div className="flex-1 min-w-[300px]">
          <ClientSelectGeneric
            size="large"
            disabled={false}
            value={filters.patient?.clientId}
            onClientChange={setPatient}
            allowClear={true}
          />
        </div>
        {/* TODO: add address select component here */}
        {/* Service - Multiple Selection */}
        <div className="flex-1 min-w-[300px]">
          <ServicesSelectGeneric
            size="large"
            allowClear={true}
            value={filters.services}
            mode="multiple"
            placeholder="Select services..."
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => (
              <Tooltip
                styles={{ root: { pointerEvents: 'none' } }}
                title={omittedValues.map(({ label }) => label).join(', ')}
              >
                <div className="flex items-center justify-center h-full">
                  <div className="text-[10px]">+{omittedValues.length}</div>
                  <div>
                    <AiOutlineMore />
                  </div>
                </div>
              </Tooltip>
            )}
            onServiceChange={(services) => setServices(services as Service[])}
          />
        </div>
        {/* Caregiver - Multiple Selection */}
        <div className="flex-1 min-w-[300px]">
          <CaregiversSelectGeneric
            size="large"
            allowClear={true}
            disabled={false}
            value={filters.caregivers}
            mode="multiple"
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => (
              <Tooltip
                styles={{ root: { pointerEvents: 'none' } }}
                title={omittedValues.map(({ label }) => label).join(', ')}
              >
                <div className="flex items-center justify-center h-full">
                  <div className="text-[10px]">+{omittedValues.length}</div>
                  <div>
                    <AiOutlineMore />
                  </div>
                </div>
              </Tooltip>
            )}
            placeholder="Select caregivers..."
            onCaregiverChange={setCaregivers}
          />
        </div>
        {/* Duration */}
        <div className="flex-1">
          <Space.Compact block className="w-full">
            <div className="text-base rounded-md rounded-r-none bg-app-secondary-extra-light p-2 ">Duration</div>
            <InputNumber
              size="large"
              value={filters.duration}
              step={15}
              min={30}
              controls={false}
              onChange={(value) => setDuration(Number(value))}
              className="border-l border-solid border-white !w-[70px]"
              placeholder="Mins"
              suffix="m"
            />
          </Space.Compact>
        </div>
      </div>
    </>
  );
};

export default FiltersPanel;
