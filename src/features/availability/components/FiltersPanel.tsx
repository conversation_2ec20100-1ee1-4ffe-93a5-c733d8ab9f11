import { Service } from '@api/READ_ONLY/caregiver_api/Api';
import { CaregiverSearch } from '@app/components/ui/CaregiverSearch';
import { ServicessSearch } from '@app/components/ui/ServicesSearch';
import { PatientSearch } from '@feat-service-requests/components/PatientSearch/PatientSearch';
import { Button, Input, InputNumber, Space } from 'antd';
import { Dispatch, SetStateAction } from 'react';
import { Controller, FormProvider, useForm, useWatch } from 'react-hook-form';
import { IoPersonAdd } from 'react-icons/io5';

type Props = {
  duration: number;
  setDuration: Dispatch<SetStateAction<number>>;
};

const FiltersPanel = ({ duration, setDuration }: Props) => {
  const methods = useForm({
    defaultValues: {
      patient: undefined,
      address: '',
      service: undefined,
      caregiver: undefined,
      duration: duration,
    },
  });
  const {
    control,
    // handleSubmit,
    formState: { errors },
    // getValues,
  } = methods;

  const selectedService = useWatch({ control, name: 'service' }) as Service | undefined;
  const caregiverQuery = selectedService ? { services: [selectedService.serviceId] } : undefined;

  //   const selectedCaregiver = useWatch({ control, name: 'caregiver' }) as Caregiver | undefined;
  // const serviceQuery = selectedCaregiver ? { services: [selectedCaregiver.serviceId] } : undefined;

  console.log('TEST HERE', selectedService);

  return (
    <FormProvider {...methods}>
      <div className="w-64 mb-4  flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          <Space.Compact>
            <div className="w-full">
              <Controller
                name="patient"
                rules={{ required: 'Patient is required' }}
                control={control}
                render={({ field }) => <PatientSearch disabled={false} value={undefined} setValue={field.onChange} />}
              />
              {errors.patient && <p className="text-app-danger text-xs mt-1">{errors.patient.message}</p>}
            </div>
            <Button type="primary" size="large" icon={<IoPersonAdd />} />
          </Space.Compact>
          <Input size="large" placeholder="Address" readOnly />
        </div>

        <div className="flex flex-col gap-2">
          <div className=" w-full">
            <Controller
              name="service"
              // rules={{ required: 'At least one service is required' }}
              control={control}
              render={({ field }) => <ServicessSearch value={field.value} setValue={field.onChange} />}
            />
            {errors.service && <p className="text-app-danger text-xs mt-1">{errors.service.message}</p>}
          </div>
          <div className="w-full">
            <Controller
              name="caregiver"
              // rules={{ required: 'At least one caregiver is required' }}
              control={control}
              render={({ field }) => (
                <CaregiverSearch
                  disabled={false}
                  value={field.value}
                  setValue={field.onChange}
                  query={caregiverQuery}
                />
              )}
            />
            {errors.caregiver && <p className="text-app-danger text-xs mt-1">{errors.caregiver.message}</p>}
          </div>
        </div>

        <div className=" w-full ">
          <Space.Compact block>
            <label className="text-base rounded-md rounded-r-none bg-app-secondary-extra-light p-2 flex-1">
              Duration
            </label>
            <InputNumber
              size="large"
              value={duration}
              step={15}
              min={30}
              onChange={(value) => setDuration(Number(value))}
              className="border-l border-solid border-white flex-1"
              placeholder="Mins"
            />
          </Space.Compact>
        </div>
      </div>{' '}
    </FormProvider>
  );
};

export default FiltersPanel;
