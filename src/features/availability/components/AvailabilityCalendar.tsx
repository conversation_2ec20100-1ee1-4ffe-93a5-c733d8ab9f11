import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { memo } from 'react';
import Calendar from './Calendar';
import { AvailabilityEvent } from './EventContent';

dayjs.extend(utc);

type Props = {
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
  events: AvailabilityEvent[];
  onEventClick?: (event: AvailabilityEvent) => void;
};

const AvailabilityCalendar = ({ currentDate, setCurrentDate, events, onEventClick }: Props) => {
  return (
    <Calendar currentDate={currentDate} setCurrentDate={setCurrentDate} events={events} onEventClick={onEventClick} />
  );
};

export default memo(AvailabilityCalendar);
