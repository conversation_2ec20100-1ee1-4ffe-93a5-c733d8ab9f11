import { EventContentArg } from '@fullcalendar/core/index.js';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import { memo } from 'react';
import { GoPersonFill } from 'react-icons/go';
import { MdAddCircle } from 'react-icons/md';

export type AvailabilityEvent = {
  id: string;
  title: string;
  start: string;
  end: string;
  durationFormatted: string;
  caregivers: { caregiverId: number; firstName: string; lastName: string }[];
};

type Props = {
  eventInfo: EventContentArg;
  onEventClick?: (event: AvailabilityEvent) => void;
};

const EventContent = memo(({ eventInfo, onEventClick }: Props) => {
  const eventStart = dayjs(eventInfo.event.start);
  const eventEnd = dayjs(eventInfo.event.end);
  const now = dayjs();

  const caregivers = eventInfo.event.extendedProps.caregivers as AvailabilityEvent['caregivers'];
  const durationFormatted = eventInfo.event.extendedProps.durationFormatted as AvailabilityEvent['durationFormatted'];

  let opacity = 1;
  if (eventStart.isBefore(now, 'day') || (eventStart.isSame(now, 'day') && eventStart.hour() < now.hour())) {
    opacity = 0.5;
  }

  const handleClick = () => {
    if (onEventClick) {
      const event: AvailabilityEvent = {
        id: eventInfo.event.id,
        title: eventInfo.event.title,
        start: eventStart.format(),
        end: eventEnd.format(),
        durationFormatted,
        caregivers,
      };
      onEventClick(event);
    }
  };

  return (
    <div
      style={{ opacity }}
      className="w-full rounded-xl border-0 h-full text-app-primary overflow-hidden bg-app-primary/25 duration-300 border-solid border-app-primary border-y-0 border-x-4 p-0.5"
    >
      <div className="flex justify-between items-center h-full">
        <div>{durationFormatted}</div>
        <Tooltip
          title={
            <div>
              {caregivers.map((c) => (
                <div key={c.caregiverId}>{c.firstName + ' ' + c.lastName}</div>
              ))}
            </div>
          }
        >
          <div className="flex justify-center items-center">
            <GoPersonFill />
            {caregivers.length}
          </div>
        </Tooltip>
      </div>
      <div
        className="flex cursor-pointer justify-center items-center absolute -bottom-[5px] left-1/2 -translate-x-1/2 rounded-full text-white"
        onClick={handleClick}
      >
        <MdAddCircle size={20} className="text-app-primary" />
      </div>
    </div>
  );
});

EventContent.displayName = 'EventContent';

export default EventContent;
