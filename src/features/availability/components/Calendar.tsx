import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayjs, { Dayjs } from 'dayjs';
import { memo, useMemo, useRef } from 'react';
import EventContent, { AvailabilityEvent } from './EventContent';
import './availability-calendar.scss';

type Props = {
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
  events: AvailabilityEvent[];
  onEventClick?: (event: AvailabilityEvent) => void;
};

const Calendar = memo(({ currentDate, setCurrentDate, events, onEventClick }: Props) => {
  const calendarRef = useRef<FullCalendar | null>(null);
  const nowHHMMSS = useMemo(() => dayjs().add(-4, 'h').format('HH:mm:ss'), []);

  // Memoize events for FullCalendar
  const memoizedEvents = useMemo(() => events, [events]);

  return (
    <div className="flex flex-col h-full w-full relative">
      <DateNavigator currentDate={currentDate} setCurrentDate={setCurrentDate} calendarRef={calendarRef} />

      <div
        className="relative h-full w-full flex-1 overflow-hidden rounded-xl border border-solid border-app-gray-light bg-app-gray-light"
        id="availability-calendar"
      >
        <FullCalendar
          ref={calendarRef}
          firstDay={1}
          plugins={[timeGridPlugin, interactionPlugin]}
          initialView="timeGridWeek"
          headerToolbar={false}
          scrollTime={nowHHMMSS}
          allDaySlot={false}
          eventContent={(eventInfo) => <EventContent eventInfo={eventInfo} onEventClick={onEventClick} />}
          dayHeaderContent={(e) => {
            const d = e.date;
            const date = dayjs(d).format('ddd D - MMM');
            return <div className="flex items-center font-medium text-center text-app-text-light">{date}</div>;
          }}
          dayHeaderClassNames="bg-app-gray-light text-center align-middle "
          expandRows
          height="100%"
          slotMinTime="00:00:00"
          slotMaxTime="24:00:00"
          eventClassNames="!bg-transparent !shadow-none"
          eventBackgroundColor="!bg-transparent !shadow-none"
          eventBorderColor="transparent"
          nowIndicator
          locale="el"
          slotDuration={`00:30:00`}
          //   slotLabelContent={() => {
          //     return 'hi';
          //   }}
          slotLabelFormat={{ hour: 'numeric', minute: '2-digit', hour12: false }}
          dayHeaderFormat={{ weekday: 'short', month: 'short', day: 'numeric' }}
          events={memoizedEvents}
        />
      </div>
    </div>
  );
});

Calendar.displayName = 'Calendar';

export default Calendar;
