import { ApiClient } from '@api/api-configuration';
import { CaregiverAvailabilityResponse } from '@api/READ_ONLY/caregiver_api/Api';
import dayjs from 'dayjs';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import AvailabilityCalendar from './components/AvailabilityCalendar';
import { AvailabilityEvent } from './components/EventContent';
import { FilterValues } from './components/FiltersPanel';
import FloatingFiltersPanel from './components/FloatingFiltersPanel';

import Draggable from 'react-draggable';

// API Types
type AvailabilityDataItem = {
  start: string;
  end: string;
  caregivers?: CaregiverAvailabilityResponse[];
};

type AvailabilityDataResponse = {
  [date: string]: AvailabilityDataItem[];
};

// type AvailabilityFormValues = {
//   patient: { clientId: string } | null;
//   address: string;
//   service: Service[];
//   caregiver: { caregiverId: string } | null;
//   duration: number;
//   fromDate: string;
//   toDate: string;
// };

const AvailabilityContainer = () => {
  const [currentDate, setCurrentDate] = useState(dayjs());
  const [bounds, setBounds] = useState({ left: 0, top: 0, right: 100, bottom: 100 });
  const [availabilityEvents, setAvailabilityEvents] = useState<AvailabilityEvent[]>([]);
  const [filters, setFilters] = useState<FilterValues>({
    patient: undefined,
    services: [],
    caregivers: [],
    duration: 60,
  });
  // const { openNotification } = useNotifications();
  const containerRef = useRef<HTMLDivElement>(null);
  const dragRef = useRef<HTMLDivElement>(null);

  // Calculate bounds function
  const calculateBounds = useCallback(() => {
    console.log('Calculating bounds...');

    if (!containerRef.current || !dragRef.current) {
      console.log('Refs not ready, using default bounds');
      return { left: 0, top: 0, right: 100, bottom: 100 };
    }

    const containerRect = containerRef.current.getBoundingClientRect();
    const panelWidth = dragRef.current.getBoundingClientRect().width || 120;
    const panelHeight = dragRef.current.getBoundingClientRect().height || 60;

    const newBounds = {
      left: 0,
      top: -Math.max(0, containerRect.height) + panelHeight,
      right: Math.max(0, containerRect.width) - panelWidth,
      bottom: 0,
    };

    console.log('New bounds calculated:', newBounds);
    return newBounds;
  }, []);

  // Calculate bounds on mount
  useEffect(() => {
    const newBounds = calculateBounds();
    setBounds(newBounds);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Get full week dates
  const getFullWeek = useCallback(() => {
    const startOfWeek = currentDate.startOf('week');
    const endOfWeek = currentDate.endOf('week');
    const dates: string[] = [];
    let d = startOfWeek;
    while (d.isBefore(endOfWeek) || d.isSame(endOfWeek, 'day')) {
      dates.push(d.format('YYYY-MM-DD'));
      d = d.add(1, 'day');
    }
    return dates;
  }, [currentDate]);

  // Fetch availability data
  const fetchAvailability = useCallback(
    async (filtersToUse: FilterValues) => {
      try {
        const response =
          await ApiClient.caregiverApi.caregivers.getAvailableCaregiversByDatesCaregiversAvailabilityByDatesPost({
            dates: getFullWeek(),
            durationMinutes: filtersToUse.duration,
            caregiverIds: filtersToUse.caregivers,
            serviceIds: filtersToUse.services,
          });

        const availabilityData: AvailabilityDataResponse = response.data.eventsByDate || {};

        const calendarEvents: AvailabilityEvent[] = Object.values(availabilityData).flatMap(
          (events: AvailabilityDataItem[]) =>
            events.map((d: AvailabilityDataItem) => ({
              id: d.start + d.end,
              title: 'Available Slot',
              start: d.start,
              end: d.end,
              durationFormatted:
                filtersToUse.duration % 60 === 0
                  ? `${Math.floor(filtersToUse.duration / 60)}h`
                  : `${Math.floor(filtersToUse.duration / 60)}h ${filtersToUse.duration % 60}m`,
              caregivers: (d.caregivers || []).map((c: CaregiverAvailabilityResponse) => ({
                caregiverId: c.caregiverId,
                firstName: c.firstName,
                lastName: c.lastName,
              })),
            }))
        );

        setAvailabilityEvents(calendarEvents);
      } catch (err) {
        console.error('Failed to fetch availability', err);
        setAvailabilityEvents([]);
      }
    },
    [getFullWeek]
  );

  // Fetch when filters or week change (single source of truth)
  useEffect(() => {
    fetchAvailability(filters);
  }, [fetchAvailability, filters]);

  // Handle filter changes - call API directly
  const onFiltersChange = useCallback((newFilters: FilterValues) => {
    console.log('Filters changed:', newFilters);
    const eqArr = (a: number[], b: number[]) => a.length === b.length && a.every((v, i) => v === b[i]);
    setFilters((prev) => {
      const samePatient = (prev.patient?.clientId || null) === (newFilters.patient?.clientId || null);
      const sameServices = eqArr(prev.services, newFilters.services);
      const sameCaregivers = eqArr(prev.caregivers, newFilters.caregivers);
      const sameDuration = prev.duration === newFilters.duration;
      if (samePatient && sameServices && sameCaregivers && sameDuration) {
        return prev;
      }
      return newFilters;
    });
    // Do not call fetch directly; useEffect will handle it
  }, []);

  // Handle event click
  const onEventClick = useCallback((event: AvailabilityEvent) => {
    console.log('Event clicked:', event);
    // TODO: Handle event click (e.g., open booking modal)
  }, []);

  // Handle date changes - call API directly
  const handleDateChange = useCallback((newDate: dayjs.Dayjs) => {
    setCurrentDate(newDate);
    // Do not call fetch directly; useEffect + memoized fetchAvailability will handle it
  }, []);

  // Handle panel toggle - recalculate bounds when panel changes size
  const onToggle = useCallback(() => {
    // Recalculate bounds after toggle (with small delay for DOM update)
    setTimeout(() => {
      const newBounds = calculateBounds();
      setBounds(newBounds);
    }, 50);
  }, [calculateBounds]);

  return (
    <div ref={containerRef} className="flex flex-row h-screen gap-4 relative">
      <AvailabilityCalendar
        currentDate={currentDate}
        setCurrentDate={handleDateChange}
        events={availabilityEvents}
        onEventClick={onEventClick}
      />
      <Draggable
        nodeRef={dragRef}
        bounds={bounds}
        handle=".drag-handle"
        cancel="input, button:not(.drag-handle button), .ant-select, .ant-input-number, .ant-picker"
      >
        <div ref={dragRef} className="z-50 absolute bottom-2 left-2">
          <FloatingFiltersPanel filters={filters} onFiltersChange={onFiltersChange} onToggle={onToggle} />
        </div>
      </Draggable>
    </div>
  );
};

export default memo(AvailabilityContainer);
