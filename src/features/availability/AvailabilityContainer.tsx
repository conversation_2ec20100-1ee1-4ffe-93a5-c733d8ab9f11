import { memo, useState } from 'react';
import AvailabilityCalendar from './components/AvailabilityCalendar';
import FiltersPanel from './components/FiltersPanel';
import dayjs from 'dayjs';

const AvailabilityContainer = () => {
  const [duration, setDuration] = useState(15);
  const [currentDate, setCurrentDate] = useState(dayjs());

  return (
    <div className="flex flex-row h-screen gap-4">
      {/* Calendar (flex-grow) */}
      <AvailabilityCalendar
        duration={duration}
        currentDate={currentDate}
        setCurrentDate={setCurrentDate}
        childrenSidebar={<FiltersPanel duration={duration} setDuration={setDuration} />}
      />
    </div>
  );
};

export default memo(AvailabilityContainer);
