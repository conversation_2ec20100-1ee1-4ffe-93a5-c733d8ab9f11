import { Client } from '@app/types/client.types';
import { queryOptions } from '@tanstack/react-query';

export default function getClientsQueryParams(state: Client, id: string) {
  return queryOptions({
    queryKey: ['client', id],
    queryFn: () => getClients(id),
    enabled: !state,
  });
}

const getClients = async (id: string) => {
  console.log(id);
  const response = await fetch('https://jsonplaceholder.typicode.com/todos');
  return response.json();
};
