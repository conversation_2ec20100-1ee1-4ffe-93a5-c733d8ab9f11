{"openapi": "3.1.0", "info": {"title": "service-requests", "description": "\nMicroservice for managing home care service requests.\n\n## Features\n* Create, read, update, and delete service request records\n* Secure endpoints with authentication and authorization\n\n## Authentication\nAll endpoints require authentication using JWT tokens.\nAdmin endpoints require additional admin permissions.\n", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/service-requests": {"get": {"tags": ["Service Requests"], "summary": "Get service requests (list or search)", "description": "Retrieve a paginated list of service requests. Provide 'query' (min 3 chars) to perform free-text search across notes, client/caregiver names, and service names.", "operationId": "get_requests_service_requests_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 3}, {"type": "null"}], "description": "Free text query; if not given return all; if given (>3 chars) searches", "title": "Query"}, "description": "Free text query; if not given return all; if given (>3 chars) searches"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestsGetAllPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Service Requests"], "summary": "Create service request", "description": "Creates a new service request", "operationId": "create_request_service_requests_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestCreateReq"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestResponse"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/service-requests/{request_id}": {"get": {"tags": ["Service Requests"], "summary": "Get service request by ID", "description": "Retrieve a specific service request by its ID", "operationId": "get_request_service_requests__request_id__get", "parameters": [{"name": "request_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Request Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestResponse"}}}}, "404": {"description": "Service request not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Service Requests"], "summary": "Create service request", "description": "Creates a new service request", "operationId": "update_request_service_requests__request_id__put", "parameters": [{"name": "request_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Request Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestUpdateReq"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestResponse"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Service Requests"], "summary": "Delete a service request", "description": "Deletes a service request by its ID", "operationId": "delete_request_service_requests__request_id__delete", "parameters": [{"name": "request_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Request Id"}}], "responses": {"204": {"description": "No content"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CaregiverServiceRequestResponse": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "type": "object", "required": ["caregiverId", "firstName", "lastName"], "title": "CaregiverServiceRequestResponse"}, "ClientServiceRequestResponse": {"properties": {"clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}}, "type": "object", "required": ["clientId", "firstName", "lastName"], "title": "ClientServiceRequestResponse", "description": "Client with service request details"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "RequestStatusEnum": {"type": "string", "enum": ["SUBMITTED", "UNFULFILLED", "CANCELLED"], "title": "RequestStatusEnum"}, "ServiceRequestCreateReq": {"properties": {"fromDate": {"type": "string", "format": "date-time", "title": "Fromdate"}, "toDate": {"type": "string", "format": "date-time", "title": "Todate"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "rrule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "status": {"anyOf": [{"$ref": "#/components/schemas/RequestStatusEnum"}, {"type": "null"}]}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "preferredCaregiverId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Preferredcaregiverid"}, "serviceIds": {"items": {"type": "integer"}, "type": "array", "minItems": 1, "title": "Serviceids"}}, "type": "object", "required": ["fromDate", "toDate", "clientId", "serviceIds"], "title": "ServiceRequestCreateReq", "description": "Model for service requests create request payload"}, "ServiceRequestResponse": {"properties": {"fromDate": {"type": "string", "format": "date-time", "title": "Fromdate"}, "toDate": {"type": "string", "format": "date-time", "title": "Todate"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "rrule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "status": {"$ref": "#/components/schemas/RequestStatusEnum"}, "serviceRequestId": {"type": "integer", "title": "Servicerequestid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "client": {"$ref": "#/components/schemas/ClientServiceRequestResponse"}, "preferredCaregiver": {"anyOf": [{"$ref": "#/components/schemas/CaregiverServiceRequestResponse"}, {"type": "null"}]}, "services": {"items": {"$ref": "#/components/schemas/ServiceServiceRequestResponse"}, "type": "array", "title": "Services"}}, "type": "object", "required": ["fromDate", "toDate", "status", "serviceRequestId", "createdAt", "client", "services"], "title": "ServiceRequestResponse", "description": "Model for service requests response body"}, "ServiceRequestUpdateReq": {"properties": {"fromDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Fromdate"}, "toDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Todate"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "rrule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "status": {"anyOf": [{"$ref": "#/components/schemas/RequestStatusEnum"}, {"type": "null"}]}, "preferredCaregiverId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Preferredcaregiverid"}, "serviceIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Serviceids"}}, "type": "object", "title": "ServiceRequestUpdateReq", "description": "Model for service requests update request payload"}, "ServiceRequestsGetAllPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/ServiceRequestResponse"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "ServiceRequestsGetAllPaginationResponse"}, "ServiceServiceRequestResponse": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}}, "type": "object", "required": ["serviceId", "name"], "title": "ServiceServiceRequestResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/auth", "tokenUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/token", "scopes": {}}}}}}, "servers": [{"url": "/api/v1/requests-api/"}], "security": [{"oauth2": []}]}