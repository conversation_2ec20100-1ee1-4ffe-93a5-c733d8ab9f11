{"openapi": "3.1.0", "info": {"title": "Service-Requests-Service", "description": "\n    Microservice for managing home care caregivers.\n\n    ## Features\n    * Create, read, update, and delete caregiver records\n    * Manage caregiver shifts and services\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/service-requests": {"get": {"tags": ["Service Requests"], "summary": "Get All Requests", "operationId": "get_all_requests_service_requests_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ServiceRequestWithServices"}, "type": "array", "title": "Response Get All Requests Service Requests Get"}}}}}}, "post": {"tags": ["Service Requests"], "summary": "Create Request", "operationId": "create_request_service_requests_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestWithServices"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/service-requests/{request_id}": {"get": {"tags": ["Service Requests"], "summary": "Get Request", "operationId": "get_request_service_requests__request_id__get", "parameters": [{"name": "request_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Request Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestWithServices"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Service Requests"], "summary": "Update Request", "operationId": "update_request_service_requests__request_id__put", "parameters": [{"name": "request_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Request Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceRequestWithServices"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Service Requests"], "summary": "Delete Request", "operationId": "delete_request_service_requests__request_id__delete", "parameters": [{"name": "request_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Request Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Caregiver": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "username": {"type": "string", "minLength": 4, "title": "Username"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Zip"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["firstName", "lastName", "username", "caregiverId", "createdAt"], "title": "Caregiver"}, "Client": {"properties": {"firstName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "username": {"anyOf": [{"type": "string", "minLength": 4}, {"type": "null"}], "title": "Username"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "emergencyContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactname"}, "emergencyContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactrelationship"}, "emergencyContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactphone"}, "secondaryContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactname"}, "secondaryContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactrelationship"}, "secondaryContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactphone"}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "roles": {"items": {"type": "string"}, "type": "array", "title": "Roles"}, "active": {"type": "boolean", "title": "Active", "default": true}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["clientId", "createdAt"], "title": "Client", "description": "Model returned from DB (e.g. INSERT … RETURNING * or SELECT)"}, "GenderEnum": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER"], "title": "GenderEnum"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MobilityEnum": {"type": "string", "enum": ["INDEPENDENT", "INDEPENDENT WITH ASSISTIVE DEVICE", "ASSISTED", "WHEELCHAIR", "BEDBOUND"], "title": "MobilityEnum"}, "Service": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}, "serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"type": "integer", "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes"}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s", "description": "Estimated cost of the service in Euros"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat", "description": "Timestamp when the service record was created"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat", "description": "Timestamp of the last service record update"}}, "additionalProperties": false, "type": "object", "required": ["serviceId", "name", "serviceTypeId", "estimatedTimeMinutes", "createdAt"], "title": "Service"}, "ServiceRequestCreate": {"properties": {"client": {"type": "integer", "title": "Client"}, "preferredCaregiver": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Preferredcaregiver"}, "serviceIds": {"items": {"type": "integer"}, "type": "array", "minItems": 1, "title": "Serviceids"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "fromDate": {"type": "string", "format": "date-time", "title": "Fromdate"}, "toDate": {"type": "string", "format": "date-time", "title": "Todate"}}, "additionalProperties": false, "type": "object", "required": ["client", "serviceIds", "fromDate", "toDate"], "title": "ServiceRequestCreate"}, "ServiceRequestUpdate": {"properties": {"preferredCaregiver": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Preferredcaregiver"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "fromDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Fromdate"}, "toDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Todate"}}, "additionalProperties": false, "type": "object", "title": "ServiceRequestUpdate"}, "ServiceRequestWithServices": {"properties": {"serviceRequestId": {"type": "integer", "title": "Servicerequestid"}, "client": {"$ref": "#/components/schemas/Client"}, "preferredCaregiver": {"anyOf": [{"$ref": "#/components/schemas/Caregiver"}, {"type": "null"}]}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "fromDate": {"type": "string", "format": "date-time", "title": "Fromdate"}, "toDate": {"type": "string", "format": "date-time", "title": "Todate"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "services": {"items": {"$ref": "#/components/schemas/Service"}, "type": "array", "title": "Services"}}, "additionalProperties": false, "type": "object", "required": ["serviceRequestId", "client", "fromDate", "toDate", "createdAt", "updatedAt"], "title": "ServiceRequestWithServices"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}