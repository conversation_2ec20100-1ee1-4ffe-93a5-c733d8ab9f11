/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** RequestStatusEnum */
export enum RequestStatusEnum {
  SUBMITTED = "SUBMITTED",
  UNFULFILLED = "UNFULFILLED",
  CANCELLED = "CANCELLED",
}

/** CaregiverServiceRequestResponse */
export interface CaregiverServiceRequestResponse {
  /** Caregiverid */
  caregiverId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Phone */
  phone?: string | null;
}

/**
 * ClientServiceRequestResponse
 * Client with service request details
 */
export interface ClientServiceRequestResponse {
  /** Clientid */
  clientId: number;
  /**
   * Firstname
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * @minLength 1
   */
  lastName: string;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/**
 * ServiceRequestCreateReq
 * Model for service requests create request payload
 */
export interface ServiceRequestCreateReq {
  /**
   * Fromdate
   * @format date-time
   */
  fromDate: string;
  /**
   * Todate
   * @format date-time
   */
  toDate: string;
  /** Notes */
  notes?: string | null;
  /** Rrule */
  rrule?: string | null;
  status?: RequestStatusEnum | null;
  /** Clientid */
  clientId: number;
  /** Preferredcaregiverid */
  preferredCaregiverId?: number | null;
  /**
   * Serviceids
   * @minItems 1
   */
  serviceIds: number[];
}

/**
 * ServiceRequestResponse
 * Model for service requests response body
 */
export interface ServiceRequestResponse {
  /**
   * Fromdate
   * @format date-time
   */
  fromDate: string;
  /**
   * Todate
   * @format date-time
   */
  toDate: string;
  /** Notes */
  notes?: string | null;
  /** Rrule */
  rrule?: string | null;
  status: RequestStatusEnum;
  /** Servicerequestid */
  serviceRequestId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
  /** Client with service request details */
  client: ClientServiceRequestResponse;
  preferredCaregiver?: CaregiverServiceRequestResponse | null;
  /** Services */
  services: ServiceServiceRequestResponse[];
}

/**
 * ServiceRequestUpdateReq
 * Model for service requests update request payload
 */
export interface ServiceRequestUpdateReq {
  /** Fromdate */
  fromDate?: string | null;
  /** Todate */
  toDate?: string | null;
  /** Notes */
  notes?: string | null;
  /** Rrule */
  rrule?: string | null;
  status?: RequestStatusEnum | null;
  /** Preferredcaregiverid */
  preferredCaregiverId?: number | null;
  /** Serviceids */
  serviceIds?: number[] | null;
}

/** ServiceRequestsGetAllPaginationResponse */
export interface ServiceRequestsGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: ServiceRequestResponse[];
}

/** ServiceServiceRequestResponse */
export interface ServiceServiceRequestResponse {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/requests-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title service-requests
 * @version 1.0.0
 * @baseUrl /api/v1/requests-api/
 *
 *
 * Microservice for managing home care service requests.
 *
 * ## Features
 * * Create, read, update, and delete service request records
 * * Secure endpoints with authentication and authorization
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 * Admin endpoints require additional admin permissions.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  serviceRequests = {
    /**
     * @description Retrieve a paginated list of service requests. Provide 'query' (min 3 chars) to perform free-text search across notes, client/caregiver names, and service names.
     *
     * @tags Service Requests
     * @name GetRequestsServiceRequestsGet
     * @summary Get service requests (list or search)
     * @request GET:/service-requests
     * @secure
     */
    getRequestsServiceRequestsGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
        /**
         * Query
         * Free text query; if not given return all; if given (>3 chars) searches
         */
        query?: string | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        ServiceRequestsGetAllPaginationResponse,
        HTTPValidationError | void
      >({
        path: `/service-requests`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Creates a new service request
     *
     * @tags Service Requests
     * @name CreateRequestServiceRequestsPost
     * @summary Create service request
     * @request POST:/service-requests
     * @secure
     */
    createRequestServiceRequestsPost: (
      data: ServiceRequestCreateReq,
      params: RequestParams = {},
    ) =>
      this.request<ServiceRequestResponse, void | HTTPValidationError>({
        path: `/service-requests`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a specific service request by its ID
     *
     * @tags Service Requests
     * @name GetRequestServiceRequestsRequestIdGet
     * @summary Get service request by ID
     * @request GET:/service-requests/{request_id}
     * @secure
     */
    getRequestServiceRequestsRequestIdGet: (
      requestId: number,
      params: RequestParams = {},
    ) =>
      this.request<ServiceRequestResponse, void | HTTPValidationError>({
        path: `/service-requests/${requestId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Creates a new service request
     *
     * @tags Service Requests
     * @name UpdateRequestServiceRequestsRequestIdPut
     * @summary Create service request
     * @request PUT:/service-requests/{request_id}
     * @secure
     */
    updateRequestServiceRequestsRequestIdPut: (
      requestId: number,
      data: ServiceRequestUpdateReq,
      params: RequestParams = {},
    ) =>
      this.request<ServiceRequestResponse, void | HTTPValidationError>({
        path: `/service-requests/${requestId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Deletes a service request by its ID
     *
     * @tags Service Requests
     * @name DeleteRequestServiceRequestsRequestIdDelete
     * @summary Delete a service request
     * @request DELETE:/service-requests/{request_id}
     * @secure
     */
    deleteRequestServiceRequestsRequestIdDelete: (
      requestId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/service-requests/${requestId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),
  };
}
