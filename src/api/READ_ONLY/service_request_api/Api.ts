/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** MobilityEnum */
export enum MobilityEnum {
  INDEPENDENT = "INDEPENDENT",
  INDEPENDENTWITHASSISTIVEDEVICE = "INDEPENDENT WITH ASSISTIVE DEVICE",
  ASSISTED = "ASSISTED",
  WHEELCHAIR = "WHEELCHAIR",
  BEDBOUND = "BEDBOUND",
}

/** GenderEnum */
export enum GenderEnum {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

/** Caregiver */
export interface Caregiver {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /**
   * Username
   * @minLength 4
   */
  username: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: number[] | Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/**
 * Client
 * Model returned from DB (e.g. INSERT … RETURNING * or SELECT)
 */
export interface Client {
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Username */
  username?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Medicalhistory */
  medicalHistory?: string | null;
  /** Medications */
  medications?: string[] | null;
  /** Allergies */
  allergies?: string[] | null;
  mobility?: MobilityEnum | null;
  /** Emergencycontactname */
  emergencyContactName?: string | null;
  /** Emergencycontactrelationship */
  emergencyContactRelationship?: string | null;
  /** Emergencycontactphone */
  emergencyContactPhone?: string | null;
  /** Secondarycontactname */
  secondaryContactName?: string | null;
  /** Secondarycontactrelationship */
  secondaryContactRelationship?: string | null;
  /** Secondarycontactphone */
  secondaryContactPhone?: string | null;
  /** Preferredlanguage */
  preferredLanguage?: string | null;
  /** Favoritecaregivers */
  favoriteCaregivers?: string[] | null;
  /** Notes */
  notes?: string | null;
  /** Roles */
  roles?: string[];
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Clientid */
  clientId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** Service */
export interface Service {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   */
  estimatedTimeMinutes: number;
  /**
   * Costineuros
   * Estimated cost of the service in Euros
   */
  costInEuros?: number | null;
  /**
   * Createdat
   * Timestamp when the service record was created
   * @format date-time
   */
  createdAt: string;
  /**
   * Updatedat
   * Timestamp of the last service record update
   */
  updatedAt?: string | null;
}

/** ServiceRequestCreate */
export interface ServiceRequestCreate {
  /** Client */
  client: number;
  /** Preferredcaregiver */
  preferredCaregiver?: number | null;
  /**
   * Serviceids
   * @minItems 1
   */
  serviceIds: number[];
  /** Notes */
  notes?: string | null;
  /**
   * Fromdate
   * @format date-time
   */
  fromDate: string;
  /**
   * Todate
   * @format date-time
   */
  toDate: string;
}

/** ServiceRequestUpdate */
export interface ServiceRequestUpdate {
  /** Preferredcaregiver */
  preferredCaregiver?: number | null;
  /** Notes */
  notes?: string | null;
  /** Fromdate */
  fromDate?: string | null;
  /** Todate */
  toDate?: string | null;
}

/** ServiceRequestWithServices */
export interface ServiceRequestWithServices {
  /** Servicerequestid */
  serviceRequestId: number;
  /** Model returned from DB (e.g. INSERT … RETURNING * or SELECT) */
  client: Client;
  preferredCaregiver?: Caregiver | null;
  /** Notes */
  notes?: string | null;
  /**
   * Fromdate
   * @format date-time
   */
  fromDate: string;
  /**
   * Todate
   * @format date-time
   */
  toDate: string;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt: string | null;
  /** Services */
  services?: Service[];
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Service-Requests-Service
 * @version 1.0.0
 *
 *
 *     Microservice for managing home care caregivers.
 *
 *     ## Features
 *     * Create, read, update, and delete caregiver records
 *     * Manage caregiver shifts and services
 *     * Secure endpoints with authentication and authorization
 *
 *     ## Authentication
 *     All endpoints require authentication using JWT tokens.
 *     Admin endpoints require additional admin permissions.
 *
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * No description
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health Check
     * @request GET:/health
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  serviceRequests = {
    /**
     * No description
     *
     * @tags Service Requests
     * @name GetAllRequestsServiceRequestsGet
     * @summary Get All Requests
     * @request GET:/service-requests
     */
    getAllRequestsServiceRequestsGet: (params: RequestParams = {}) =>
      this.request<ServiceRequestWithServices[], any>({
        path: `/service-requests`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Service Requests
     * @name CreateRequestServiceRequestsPost
     * @summary Create Request
     * @request POST:/service-requests
     */
    createRequestServiceRequestsPost: (
      data: ServiceRequestCreate,
      params: RequestParams = {},
    ) =>
      this.request<ServiceRequestWithServices, HTTPValidationError>({
        path: `/service-requests`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Service Requests
     * @name GetRequestServiceRequestsRequestIdGet
     * @summary Get Request
     * @request GET:/service-requests/{request_id}
     */
    getRequestServiceRequestsRequestIdGet: (
      requestId: number,
      params: RequestParams = {},
    ) =>
      this.request<ServiceRequestWithServices, HTTPValidationError>({
        path: `/service-requests/${requestId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Service Requests
     * @name UpdateRequestServiceRequestsRequestIdPut
     * @summary Update Request
     * @request PUT:/service-requests/{request_id}
     */
    updateRequestServiceRequestsRequestIdPut: (
      requestId: number,
      data: ServiceRequestUpdate,
      params: RequestParams = {},
    ) =>
      this.request<ServiceRequestWithServices, HTTPValidationError>({
        path: `/service-requests/${requestId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Service Requests
     * @name DeleteRequestServiceRequestsRequestIdDelete
     * @summary Delete Request
     * @request DELETE:/service-requests/{request_id}
     */
    deleteRequestServiceRequestsRequestIdDelete: (
      requestId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/service-requests/${requestId}`,
        method: "DELETE",
        ...params,
      }),
  };
}
