/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** Doctor */
export interface Doctor {
  /** Doctorid */
  doctorId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /**
   * Email
   * @format email
   */
  email: string;
  /** Phone */
  phone?: string | null;
  /** Specialization */
  specialization?: string | null;
  /** Licensenumber */
  licenseNumber?: string | null;
  /**
   * Isactive
   * @default true
   */
  isActive?: boolean;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** DoctorCreate */
export interface DoctorCreate {
  /**
   * Firstname
   * Doctor's first name
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * Doctor's last name
   * @minLength 1
   */
  lastName: string;
  /**
   * Email
   * A valid email address for the doctor
   * @format email
   */
  email: string;
  /** Phone */
  phone?: string | null;
  /** Specialization */
  specialization?: string | null;
  /** Licensenumber */
  licenseNumber?: string | null;
  /**
   * Isactive
   * @default true
   */
  isActive?: boolean;
}

/** DoctorUpdate */
export interface DoctorUpdate {
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Email */
  email?: string | null;
  /** Phone */
  phone?: string | null;
  /** Specialization */
  specialization?: string | null;
  /** Licensenumber */
  licenseNumber?: string | null;
  /** Isactive */
  isActive?: boolean | null;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Doctors-Service
 * @version 1.0.0
 *
 *
 *     Microservice for managing home care doctors.
 *
 *     ## Features
 *     * Create, read, update, and delete doctors
 *     * Secure endpoints with authentication and authorization
 *
 *     ## Authentication
 *     All endpoints require authentication using JWT tokens.
 *     Admin endpoints require additional admin permissions.
 *
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * No description
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health Check
     * @request GET:/health
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  doctors = {
    /**
     * @description Retrieve a list of all doctors with pagination support.
     *
     * @name GetAllDoctorsListDoctorsGet
     * @summary Get all doctors
     * @request GET:/doctors
     */
    getAllDoctorsListDoctorsGet: (
      query?: {
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 1
         * @max 500
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<Doctor[], HTTPValidationError>({
        path: `/doctors`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Register a new doctor in the system.
     *
     * @name AddNewDoctorDoctorsPost
     * @summary Create a new doctor
     * @request POST:/doctors
     */
    addNewDoctorDoctorsPost: (data: DoctorCreate, params: RequestParams = {}) =>
      this.request<Doctor, void | HTTPValidationError>({
        path: `/doctors`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve details for a single doctor by their ID.
     *
     * @name GetSingleDoctorDoctorsDoctorIdGet
     * @summary Get doctor by ID
     * @request GET:/doctors/{doctor_id}
     */
    getSingleDoctorDoctorsDoctorIdGet: (
      doctorId: number,
      params: RequestParams = {},
    ) =>
      this.request<Doctor, void | HTTPValidationError>({
        path: `/doctors/${doctorId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing doctor's information.
     *
     * @name UpdateExistingDoctorDoctorsDoctorIdPut
     * @summary Update doctor information
     * @request PUT:/doctors/{doctor_id}
     */
    updateExistingDoctorDoctorsDoctorIdPut: (
      doctorId: number,
      data: DoctorUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Doctor, void | HTTPValidationError>({
        path: `/doctors/${doctorId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a doctor from the system.
     *
     * @name RemoveDoctorDoctorsDoctorIdDelete
     * @summary Delete doctor
     * @request DELETE:/doctors/{doctor_id}
     */
    removeDoctorDoctorsDoctorIdDelete: (
      doctorId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/doctors/${doctorId}`,
        method: "DELETE",
        ...params,
      }),
  };
}
