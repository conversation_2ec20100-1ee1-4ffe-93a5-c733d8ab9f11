{"openapi": "3.1.0", "info": {"title": "Doctors-Service", "description": "\n    Microservice for managing home care doctors.\n\n    ## Features\n    * Create, read, update, and delete doctors\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/doctors": {"get": {"summary": "Get all doctors", "description": "Retrieve a list of all doctors with pagination support.", "operationId": "get_all_doctors_list_doctors_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of doctor records", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Doctor"}, "title": "Response Get All Doctors List Doctors Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"summary": "Create a new doctor", "description": "Register a new doctor in the system.", "operationId": "add_new_doctor_doctors_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DoctorCreate"}}}}, "responses": {"201": {"description": "Doctor created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Doctor"}}}}, "409": {"description": "Conflict: Doctor with this unique identifier (e.g., email or license) already exists"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/doctors/{doctor_id}": {"get": {"summary": "Get doctor by ID", "description": "Retrieve details for a single doctor by their ID.", "operationId": "get_single_doctor_doctors__doctor_id__get", "parameters": [{"name": "doctor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doctor Id"}}], "responses": {"200": {"description": "Doctor record details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Doctor"}}}}, "404": {"description": "Doctor not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"summary": "Update doctor information", "description": "Update an existing doctor's information.", "operationId": "update_existing_doctor_doctors__doctor_id__put", "parameters": [{"name": "doctor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doctor Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DoctorUpdate"}}}}, "responses": {"200": {"description": "The updated doctor record", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Doctor"}}}}, "404": {"description": "Doctor not found"}, "400": {"description": "Bad Request: No fields provided for update or invalid data"}, "409": {"description": "Conflict: Another doctor with the updated unique identifier already exists"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete doctor", "description": "Delete a doctor from the system.", "operationId": "remove_doctor_doctors__doctor_id__delete", "parameters": [{"name": "doctor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doctor Id"}}], "responses": {"204": {"description": "Doctor deleted successfully"}, "404": {"description": "Doctor not found"}, "409": {"description": "Conflict: Doctor is referenced by other records (e.g., appointments)"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Doctor": {"properties": {"doctorId": {"type": "integer", "title": "<PERSON><PERSON>"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "email": {"type": "string", "format": "email", "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "specialization": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Specialization"}, "licenseNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Licensenumber"}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["doctorId", "firstName", "lastName", "email", "createdAt"], "title": "Doctor"}, "DoctorCreate": {"properties": {"firstName": {"type": "string", "minLength": 1, "title": "Firstname", "description": "Doctor's first name"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname", "description": "Doctor's last name"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "A valid email address for the doctor"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "specialization": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Specialization"}, "licenseNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Licensenumber"}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}}, "additionalProperties": false, "type": "object", "required": ["firstName", "lastName", "email"], "title": "DoctorCreate"}, "DoctorUpdate": {"properties": {"firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "specialization": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Specialization"}, "licenseNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Licensenumber"}, "isActive": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Isactive"}}, "additionalProperties": false, "type": "object", "title": "DoctorUpdate"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}