{"openapi": "3.1.0", "info": {"title": "clients", "description": "\nMicroservice for managing home care clients.\n\n## Features\n* Create, read, update, and delete client records\n* Manage client addresses and emergency contacts\n* Secure endpoints with authentication and authorization\n\n## Authentication\nAll endpoints require authentication using JWT tokens.\nAdmin endpoints require additional admin permissions.\n", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/clients": {"get": {"tags": ["Clients"], "summary": "Get clients (list or search)", "description": "Retrieve a paginated list of clients. Optionally provide a 'query' (min 3 chars) to search by first name, last name, phone, or email.", "operationId": "get_clients_clients_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 3}, {"type": "null"}], "description": "Free text query; if not given return all; if given (>3 chars) searches", "title": "Query"}, "description": "Free text query; if not given return all; if given (>3 chars) searches"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientsGetAllPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Clients"], "summary": "Create new client", "description": "Create a new client record with address and optional emergency contact", "operationId": "create_client_clients_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientCreate"}}}}, "responses": {"201": {"description": "Client created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/clients/minified": {"get": {"tags": ["Clients"], "summary": "Get clients (list or search) with minified schema", "description": "Retrieve a paginated list of clients. Optionally provide a 'query' (min 3 chars) to search by first name, last name, phone, or email.", "operationId": "get_clients_min_clients_minified_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 3}, {"type": "null"}], "description": "Free text query; if not given return all; if given (>3 chars) searches", "title": "Query"}, "description": "Free text query; if not given return all; if given (>3 chars) searches"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientsGetAllMinifiedPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/clients/{client_id}": {"get": {"tags": ["Clients"], "summary": "Get client by ID", "description": "Retrieve a specific client by their ID", "operationId": "get_client_clients__client_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Client Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Clients"], "summary": "Update client", "description": "Update an existing client's information", "operationId": "update_client_clients__client_id__put", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdate"}}}}, "responses": {"200": {"description": "Updated client record", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Clients"], "summary": "Delete client", "description": "Delete a client record by ID", "operationId": "delete_client_clients__client_id__delete", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"204": {"description": "Client deleted successfully"}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/by-user/{user_id}": {"get": {"tags": ["Clients"], "summary": "Get client by user ID", "description": "Retrieve a client by their user ID", "operationId": "get_client_by_user_id_by_user__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/clients/history/{client_id}": {"get": {"tags": ["Clients"], "summary": "Get a client with their full visit history", "description": "Retrieve a client along with their complete visit history, with optional date range filtering", "operationId": "get_client_history_clients_history__client_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter visit history on or after this date", "title": "Start Date"}, "description": "Filter visit history on or after this date"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter visit history on or before this date", "title": "End Date"}, "description": "Filter visit history on or before this date"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientWithHistory"}}}}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CaregiverServiceRequestResponse": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "type": "object", "required": ["caregiverId", "firstName", "lastName"], "title": "CaregiverServiceRequestResponse"}, "Client": {"properties": {"firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"type": "string", "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"type": "string", "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "active": {"type": "boolean", "title": "Active", "default": true}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "createdAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["firstName", "lastName", "phone", "city", "clientId"], "title": "Client", "description": "Model returned from DB (e.g. INSERT … RETURNING * or SELECT)"}, "ClientCreate": {"properties": {"firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"type": "string", "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"type": "string", "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "active": {"type": "boolean", "title": "Active", "default": true}}, "type": "object", "required": ["firstName", "lastName", "phone", "city"], "title": "ClientCreate", "description": "Payload for POST /clients — required fields per SQL (NOT NULL)"}, "ClientMinified": {"properties": {"firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}}, "type": "object", "title": "ClientMinified"}, "ClientUpdate": {"properties": {"firstName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "active": {"type": "boolean", "title": "Active", "default": true}}, "type": "object", "required": ["firstName", "lastName", "phone", "city"], "title": "ClientUpdate", "description": "PATCH/PUT payload – all fields optional (dump with exclude_unset=True)"}, "ClientWithHistory": {"properties": {"firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"type": "string", "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"type": "string", "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "active": {"type": "boolean", "title": "Active", "default": true}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "createdAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "visits": {"items": {"$ref": "#/components/schemas/VisitHistory"}, "type": "array", "title": "Visits"}}, "type": "object", "required": ["firstName", "lastName", "phone", "city", "clientId", "visits"], "title": "ClientWithHistory"}, "ClientsGetAllMinifiedPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/ClientMinified"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "ClientsGetAllMinifiedPaginationResponse", "description": "Client with pagination"}, "ClientsGetAllPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/Client"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "ClientsGetAllPaginationResponse", "description": "Client with pagination"}, "GenderEnum": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER"], "title": "GenderEnum"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MobilityEnum": {"type": "string", "enum": ["INDEPENDENT", "INDEPENDENT WITH ASSISTIVE DEVICE", "ASSISTED", "WHEELCHAIR", "BEDBOUND"], "title": "MobilityEnum"}, "Service": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}, "serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes", "default": 0}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s", "description": "Estimated cost of the service in Euros"}}, "type": "object", "required": ["serviceId", "name", "serviceTypeId"], "title": "Service", "description": "Model representing a Homecare Service offering."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VisitHistory": {"properties": {"visitId": {"type": "integer", "title": "Visitid"}, "caregivers": {"anyOf": [{"items": {"$ref": "#/components/schemas/CaregiverServiceRequestResponse"}, "type": "array"}, {"type": "null"}], "title": "Caregivers"}, "startTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Starttime"}, "endTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "createdAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "services": {"anyOf": [{"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "attachments": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Attachments"}}, "type": "object", "required": ["visitId"], "title": "VisitHistory"}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/auth", "tokenUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/token", "scopes": {}}}}}}, "servers": [{"url": "/api/v1/clients-api/"}], "security": [{"oauth2": []}]}