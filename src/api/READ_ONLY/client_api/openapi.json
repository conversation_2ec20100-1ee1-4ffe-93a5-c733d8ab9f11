{"openapi": "3.1.0", "info": {"title": "Clients-Service", "description": "\n    Microservice for managing home care clients.\n\n    ## Features\n    * Create, read, update, and delete client records\n    * Manage client addresses and emergency contacts\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/clients": {"get": {"tags": ["Clients"], "summary": "Get all clients", "description": "Retrieve a list of all clients with pagination support", "operationId": "get_clients_clients_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of client records", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}, "title": "Response Get Clients Clients Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Clients"], "summary": "Create new client", "description": "Create a new client record with address and optional emergency contact", "operationId": "create_client_clients_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientCreate"}}}}, "responses": {"201": {"description": "Client created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/clients/{client_id}": {"get": {"tags": ["Clients"], "summary": "Get client by ID", "description": "Retrieve a specific client by their ID", "operationId": "get_client_clients__client_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"200": {"description": "Client record details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Clients"], "summary": "Update client", "description": "Update an existing client's information", "operationId": "update_client_clients__client_id__put", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdate"}}}}, "responses": {"200": {"description": "Updated client record", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Clients"], "summary": "Delete client", "description": "Delete a client record by ID", "operationId": "delete_client_clients__client_id__delete", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"204": {"description": "Client deleted successfully"}, "404": {"description": "Client not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/clients/history/{client_id}": {"get": {"tags": ["Clients"], "summary": "Get a client with their full visit history", "operationId": "get_client_history_clients_history__client_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter visit history on or after this date", "title": "Start Date"}, "description": "Filter visit history on or after this date"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter visit history on or before this date", "title": "End Date"}, "description": "Filter visit history on or before this date"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientWithHistory"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/clients/search/{data}": {"get": {"tags": ["Clients"], "summary": "Search clients", "description": "Search for clients by name or phone number with pagination support.", "operationId": "search_client_clients_search__data__get", "parameters": [{"name": "data", "in": "path", "required": true, "schema": {"type": "string", "title": "Data"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of client records matching the search criteria", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}, "title": "Response Search Client Clients Search  Data  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Client": {"properties": {"firstName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "username": {"anyOf": [{"type": "string", "minLength": 4}, {"type": "null"}], "title": "Username"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "emergencyContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactname"}, "emergencyContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactrelationship"}, "emergencyContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactphone"}, "secondaryContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactname"}, "secondaryContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactrelationship"}, "secondaryContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactphone"}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "roles": {"items": {"type": "string"}, "type": "array", "title": "Roles"}, "active": {"type": "boolean", "title": "Active", "default": true}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["clientId", "createdAt"], "title": "Client", "description": "Model returned from DB (e.g. INSERT … RETURNING * or SELECT)"}, "ClientCreate": {"properties": {"firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "username": {"anyOf": [{"type": "string", "minLength": 4}, {"type": "null"}], "title": "Username"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "emergencyContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactname"}, "emergencyContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactrelationship"}, "emergencyContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactphone"}, "secondaryContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactname"}, "secondaryContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactrelationship"}, "secondaryContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactphone"}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "roles": {"items": {"type": "string"}, "type": "array", "title": "Roles"}, "active": {"type": "boolean", "title": "Active", "default": true}}, "additionalProperties": false, "type": "object", "required": ["firstName", "lastName"], "title": "ClientCreate", "description": "Payload for POST /clients"}, "ClientUpdate": {"properties": {"firstName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "username": {"anyOf": [{"type": "string", "minLength": 4}, {"type": "null"}], "title": "Username"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "emergencyContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactname"}, "emergencyContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactrelationship"}, "emergencyContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactphone"}, "secondaryContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactname"}, "secondaryContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactrelationship"}, "secondaryContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactphone"}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "roles": {"items": {"type": "string"}, "type": "array", "title": "Roles"}, "active": {"type": "boolean", "title": "Active", "default": true}}, "additionalProperties": false, "type": "object", "title": "ClientUpdate", "description": "PATCH/PUT payload – all fields optional (dump with exclude_unset=True)"}, "ClientWithHistory": {"properties": {"firstName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "username": {"anyOf": [{"type": "string", "minLength": 4}, {"type": "null"}], "title": "Username"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "emergencyContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactname"}, "emergencyContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactrelationship"}, "emergencyContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergencycontactphone"}, "secondaryContactName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactname"}, "secondaryContactRelationship": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactrelationship"}, "secondaryContactPhone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondarycontactphone"}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "roles": {"items": {"type": "string"}, "type": "array", "title": "Roles"}, "active": {"type": "boolean", "title": "Active", "default": true}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "visits": {"items": {"$ref": "#/components/schemas/VisitHistory"}, "type": "array", "title": "Visits"}}, "additionalProperties": false, "type": "object", "required": ["clientId", "createdAt", "visits"], "title": "ClientWithHistory"}, "GenderEnum": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER"], "title": "GenderEnum"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MobilityEnum": {"type": "string", "enum": ["INDEPENDENT", "INDEPENDENT WITH ASSISTIVE DEVICE", "ASSISTED", "WHEELCHAIR", "BEDBOUND"], "title": "MobilityEnum"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VisitHistory": {"properties": {"id": {"type": "integer", "title": "Id"}, "caregiver": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Caregiver"}, "startTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Starttime"}, "endTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "createdAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "services": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Services"}, "attachments": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Attachments"}}, "additionalProperties": false, "type": "object", "required": ["id"], "title": "VisitHistory"}}}}