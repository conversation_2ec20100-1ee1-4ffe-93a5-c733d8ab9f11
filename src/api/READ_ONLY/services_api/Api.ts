/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/**
 * Service
 * Model representing a Homecare Service offering.
 */
export interface Service {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   * @default 0
   */
  estimatedTimeMinutes?: number | null;
  /**
   * Costineuros
   * Estimated cost of the service in Euros
   */
  costInEuros?: number | null;
}

/**
 * ServiceCreate
 * Schema for creating a new service.
 */
export interface ServiceCreate {
  /**
   * Name
   * Name of the specific service
   */
  name: string;
  /** Description */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * @default 0
   */
  estimatedTimeMinutes?: number | null;
  /** Costineuros */
  costInEuros?: number | null;
}

/**
 * ServiceType
 * Model representing a type of service, stored in a separate table.
 */
export interface ServiceType {
  /**
   * Servicetypeid
   * Unique identifier for the service type (PK of service_types table)
   */
  serviceTypeId: number;
  /**
   * Name
   * Name of the service type (e.g., 'Skin/Wound Management', 'Elimination Management')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service type
   */
  description?: string | null;
}

/**
 * ServiceTypeCreate
 * Schema for creating a new service type.
 */
export interface ServiceTypeCreate {
  /**
   * Name
   * Name of the service type (e.g., 'Skin/Wound Management', 'Elimination Management')
   */
  name: string;
  /** Description */
  description?: string | null;
}

/**
 * ServiceTypeUpdate
 * Schema for updating an existing service type.
 */
export interface ServiceTypeUpdate {
  /** Name */
  name?: string | null;
  /** Description */
  description?: string | null;
}

/** ServiceTypesGetAllPaginationResponse */
export interface ServiceTypesGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: ServiceType[];
}

/**
 * ServiceUpdate
 * Schema for updating an existing service.
 */
export interface ServiceUpdate {
  /** Name */
  name?: string | null;
  /** Description */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number | null;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   */
  estimatedTimeMinutes: number | null;
  /** Costineuros */
  costInEuros?: number | null;
}

/** ServicesGetAllPaginationResponse */
export interface ServicesGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: Service[];
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/services-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title services
 * @version 1.0.0
 * @baseUrl /api/v1/services-api/
 *
 *
 * Microservice for managing home care service types & services.
 *
 * ## Features
 * * Create, read, update, and delete service types
 * * Create, read, update, and delete services
 * * Secure endpoints with authentication and authorization
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 * Admin endpoints require additional admin permissions.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  services = {
    /**
     * @description Retrieve a paginated list of services with optional filters. Provide 'query' (min 3 chars) to perform free-text search across name, description, and service type name.
     *
     * @tags Services
     * @name GetServicesServicesGet
     * @summary Get services (list or search)
     * @request GET:/services
     * @secure
     */
    getServicesServicesGet: (
      query?: {
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 0
         * @default 100
         */
        limit?: number;
        /**
         * Query
         * Free text query; if not given return all; if given (>3 chars) searches
         */
        query?: string | null;
        /**
         * Caregiver Ids
         * Filter by caregiver ids
         */
        caregiver_ids?: number[] | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        ServicesGetAllPaginationResponse,
        HTTPValidationError | void
      >({
        path: `/services`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a new service record
     *
     * @tags Services
     * @name CreateServiceServicesPost
     * @summary Create new service
     * @request POST:/services
     * @secure
     */
    createServiceServicesPost: (
      data: ServiceCreate,
      params: RequestParams = {},
    ) =>
      this.request<Service, void | HTTPValidationError>({
        path: `/services`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a list of services associated with a specific service type ID
     *
     * @tags Services
     * @name GetServicesByTypeServicesByTypeServiceTypeIdGet
     * @summary Get services by type ID
     * @request GET:/services/by-type/{service_type_id}
     * @secure
     */
    getServicesByTypeServicesByTypeServiceTypeIdGet: (
      serviceTypeId: number,
      query?: {
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 0
         * @max 100
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<Service[], void | HTTPValidationError>({
        path: `/services/by-type/${serviceTypeId}`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a specific service by its ID
     *
     * @tags Services
     * @name GetServiceServicesServiceIdGet
     * @summary Get service by ID
     * @request GET:/services/{service_id}
     * @secure
     */
    getServiceServicesServiceIdGet: (
      serviceId: number,
      params: RequestParams = {},
    ) =>
      this.request<Service, void | HTTPValidationError>({
        path: `/services/${serviceId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing service's information
     *
     * @tags Services
     * @name UpdateServiceServicesServiceIdPut
     * @summary Update service
     * @request PUT:/services/{service_id}
     * @secure
     */
    updateServiceServicesServiceIdPut: (
      serviceId: number,
      data: ServiceUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Service, void | HTTPValidationError>({
        path: `/services/${serviceId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a service record by ID
     *
     * @tags Services
     * @name DeleteServiceServicesServiceIdDelete
     * @summary Delete service
     * @request DELETE:/services/{service_id}
     * @secure
     */
    deleteServiceServicesServiceIdDelete: (
      serviceId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/services/${serviceId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),
  };
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  serviceTypes = {
    /**
     * @description Free text query; if not given return all; if given (>3 chars) searches
     *
     * @tags Service Types
     * @name GetServiceTypesServiceTypesGet
     * @summary Get service types (list or search)
     * @request GET:/service-types
     * @secure
     */
    getServiceTypesServiceTypesGet: (
      query?: {
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 0
         * @max 100
         * @default 100
         */
        limit?: number;
        /**
         * Query
         * Free text query; if not given return all; if given (>3 chars) searches
         */
        query?: string | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        ServiceTypesGetAllPaginationResponse,
        HTTPValidationError | void
      >({
        path: `/service-types`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a new service type record
     *
     * @tags Service Types
     * @name CreateServiceTypeServiceTypesPost
     * @summary Create new service type
     * @request POST:/service-types
     * @secure
     */
    createServiceTypeServiceTypesPost: (
      data: ServiceTypeCreate,
      params: RequestParams = {},
    ) =>
      this.request<ServiceType, void | HTTPValidationError>({
        path: `/service-types`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a specific service type by its ID
     *
     * @tags Service Types
     * @name GetServiceTypeServiceTypesServiceTypeIdGet
     * @summary Get service type by ID
     * @request GET:/service-types/{service_type_id}
     * @secure
     */
    getServiceTypeServiceTypesServiceTypeIdGet: (
      serviceTypeId: number,
      params: RequestParams = {},
    ) =>
      this.request<ServiceType, void | HTTPValidationError>({
        path: `/service-types/${serviceTypeId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing service type's information
     *
     * @tags Service Types
     * @name UpdateServiceTypeServiceTypesServiceTypeIdPut
     * @summary Update service type
     * @request PUT:/service-types/{service_type_id}
     * @secure
     */
    updateServiceTypeServiceTypesServiceTypeIdPut: (
      serviceTypeId: number,
      data: ServiceTypeUpdate,
      params: RequestParams = {},
    ) =>
      this.request<ServiceType, void | HTTPValidationError>({
        path: `/service-types/${serviceTypeId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a service type record by ID
     *
     * @tags Service Types
     * @name DeleteServiceTypeServiceTypesServiceTypeIdDelete
     * @summary Delete service type
     * @request DELETE:/service-types/{service_type_id}
     * @secure
     */
    deleteServiceTypeServiceTypesServiceTypeIdDelete: (
      serviceTypeId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/service-types/${serviceTypeId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),
  };
}
