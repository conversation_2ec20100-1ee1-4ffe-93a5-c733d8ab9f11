{"openapi": "3.1.0", "info": {"title": "Caregivers-Service", "description": "\n    Microservice for managing home care caregivers.\n\n    ## Features\n    * Create, read, update, and delete caregiver records\n    * Manage caregiver shifts and services\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/caregivers/availability/by-dates": {"post": {"tags": ["Caregivers Availability"], "summary": "Get available caregivers on specific dates", "description": "Returns availability for caregivers across non-continuous dates. Filters by duration, caregiver IDs, and service IDs.", "operationId": "get_available_caregivers_by_dates_caregivers_availability_by_dates_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverAvailabilityByDatesRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CaregiverAvailability"}, "type": "array", "title": "Response Get Available Caregivers By Dates Caregivers Availability By Dates Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/availability/by-range": {"get": {"tags": ["Caregivers Availability"], "summary": "Get available caregivers in a datetime range", "description": "Returns caregiver availability within a continuous datetime range. Optionally filters by slot duration, caregiver IDs, and service IDs.", "operationId": "get_available_caregivers_by_range_caregivers_availability_by_range_get", "parameters": [{"name": "from_datetime", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "Start of datetime range (UTC)", "title": "From Datetime"}, "description": "Start of datetime range (UTC)"}, {"name": "to_datetime", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "End of datetime range (UTC)", "title": "To Datetime"}, "description": "End of datetime range (UTC)"}, {"name": "duration_minutes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Slot duration in minutes", "title": "Duration Minutes"}, "description": "Slot duration in minutes"}, {"name": "caregiver_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by caregiver IDs", "title": "Caregiver Ids"}, "description": "Filter by caregiver IDs"}, {"name": "service_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by caregiver service IDs", "title": "Service Ids"}, "description": "Filter by caregiver service IDs"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaregiverAvailability"}, "title": "Response Get Available Caregivers By Range Caregivers Availability By Range Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers": {"get": {"tags": ["Caregivers"], "summary": "Get Caregivers", "operationId": "get_caregivers_caregivers_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "username", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by username (partial match)", "title": "Username"}, "description": "Filter by username (partial match)"}, {"name": "services", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by service IDs", "title": "Services"}, "description": "Filter by service IDs"}, {"name": "active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Active"}, "description": "Filter by active status"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by city (exact match)", "title": "City"}, "description": "Filter by city (exact match)"}, {"name": "skills", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "Filter by skills (match any)", "title": "Skills"}, "description": "Filter by skills (match any)"}, {"name": "languages_spoken", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "Filter by languages spoken (match any)", "title": "Languages Spoken"}, "description": "Filter by languages spoken (match any)"}, {"name": "rating_min", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "Minimum rating", "title": "Rating <PERSON>"}, "description": "Minimum rating"}, {"name": "rating_max", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "Maximum rating", "title": "Rating Max"}, "description": "Maximum rating"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Caregiver"}, "title": "Response Get Caregivers Caregivers Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Caregivers"], "summary": "Create Caregiver", "operationId": "create_caregiver_caregivers_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Caregiver"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/{caregiver_id}": {"get": {"tags": ["Caregivers"], "summary": "Get Caregiver", "operationId": "get_caregiver_caregivers__caregiver_id__get", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Caregiver"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Caregivers"], "summary": "Update Caregiver", "operationId": "update_caregiver_caregivers__caregiver_id__put", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Caregiver"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Caregivers"], "summary": "Delete Caregiver", "operationId": "delete_caregiver_caregivers__caregiver_id__delete", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/{caregiver_id}/shifts": {"post": {"tags": ["Shifts"], "summary": "Add Shift", "operationId": "add_shift_caregivers__caregiver_id__shifts_post", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShiftCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Add Shift Caregivers  Caregiver Id  Shifts Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Shifts"], "summary": "Get shifts for a caregiver", "description": "Fetches shifts assigned to a caregiver with optional from/to date filters.", "operationId": "get_caregiver_shifts_caregivers__caregiver_id__shifts_get", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}, {"name": "from_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter from this date", "title": "From Date"}, "description": "Filter from this date"}, {"name": "to_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter to this date", "title": "To Date"}, "description": "Filter to this date"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaregiverShift"}, "title": "Response Get Caregiver Shifts Caregivers  Caregiver Id  Shifts Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/shifts/by-dates": {"get": {"tags": ["Shifts"], "summary": "Fetch all caregiver shifts for a list of dates", "description": "Returns all caregiver shifts where the shift start date matches any of the provided dates.", "operationId": "get_shifts_by_dates_caregivers_shifts_by_dates_get", "parameters": [{"name": "dates", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "date"}, "description": "List of dates (e.g. ?dates=2025-07-18&dates=2025-07-19)", "title": "Dates"}, "description": "List of dates (e.g. ?dates=2025-07-18&dates=2025-07-19)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaregiverShiftsByDatesResponse"}, "title": "Response Get Shifts By Dates Caregivers Shifts By Dates Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/{caregiver_id}/shifts/{shift_id}": {"put": {"tags": ["Shifts"], "summary": "Update a caregiver shift", "description": "Updates an existing caregiver shift's notes, start time, and end time.", "operationId": "update_shift_caregivers__caregiver_id__shifts__shift_id__put", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}, {"name": "shift_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Shift Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShiftCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverShift"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Shifts"], "summary": "Delete a caregiver shift", "description": "Deletes a caregiver shift by its ID.", "operationId": "delete_shift_caregivers__caregiver_id__shifts__shift_id__delete", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}, {"name": "shift_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Shift Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Caregiver": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "username": {"type": "string", "minLength": 4, "title": "Username"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Zip"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["firstName", "lastName", "username", "caregiverId", "createdAt"], "title": "Caregiver"}, "CaregiverAvailability": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "availability": {"items": {"$ref": "#/components/schemas/DateAvailability"}, "type": "array", "title": "Availability"}}, "additionalProperties": false, "type": "object", "required": ["caregiverId", "availability"], "title": "CaregiverAvailability"}, "CaregiverAvailabilityByDatesRequest": {"properties": {"dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Dates", "description": "List of individual dates to check (UTC)"}, "durationMinutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Durationminutes", "description": "Slot duration in minutes"}, "caregiverIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Caregiverids", "description": "Filter by caregiver IDs"}, "serviceIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Serviceids", "description": "Filter by caregiver service IDs"}}, "additionalProperties": false, "type": "object", "required": ["dates"], "title": "CaregiverAvailabilityByDatesRequest"}, "CaregiverCreate": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "username": {"type": "string", "minLength": 4, "title": "Username"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Zip"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}}, "additionalProperties": false, "type": "object", "required": ["firstName", "lastName", "username"], "title": "CaregiverCreate"}, "CaregiverShift": {"properties": {"caregiverShiftId": {"type": "integer", "title": "Caregivershiftid"}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "periodFrom": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Periodfrom"}, "periodTo": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Periodto"}}, "additionalProperties": false, "type": "object", "required": ["caregiverShiftId", "caregiverId"], "title": "CaregiverShift"}, "CaregiverShiftsByDatesResponse": {"properties": {"caregiverShiftId": {"type": "integer", "title": "Caregivershiftid"}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "periodFrom": {"type": "string", "format": "date-time", "title": "Periodfrom"}, "periodTo": {"type": "string", "format": "date-time", "title": "Periodto"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "additionalProperties": false, "type": "object", "required": ["caregiverShiftId", "caregiverId", "periodFrom", "periodTo"], "title": "CaregiverShiftsByDatesResponse"}, "CaregiverUpdate": {"properties": {"firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Zip"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Rating"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active"}}, "additionalProperties": false, "type": "object", "title": "CaregiverUpdate"}, "DateAvailability": {"properties": {"date": {"type": "string", "format": "date", "title": "Date"}, "slots": {"items": {"$ref": "#/components/schemas/TimeSlot"}, "type": "array", "title": "Slots"}}, "additionalProperties": false, "type": "object", "required": ["date", "slots"], "title": "DateAvailability"}, "GenderEnum": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER"], "title": "GenderEnum"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Service": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}, "serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"type": "integer", "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes"}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s", "description": "Estimated cost of the service in Euros"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat", "description": "Timestamp when the service record was created"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat", "description": "Timestamp of the last service record update"}}, "additionalProperties": false, "type": "object", "required": ["serviceId", "name", "serviceTypeId", "estimatedTimeMinutes", "createdAt"], "title": "Service"}, "ShiftCreate": {"properties": {"notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "periodFrom": {"type": "string", "format": "date-time", "title": "Periodfrom"}, "periodTo": {"type": "string", "format": "date-time", "title": "Periodto"}}, "additionalProperties": false, "type": "object", "required": ["notes", "periodFrom", "periodTo"], "title": "ShiftCreate"}, "TimeSlot": {"properties": {"start": {"type": "string", "format": "date-time", "title": "Start"}, "end": {"type": "string", "format": "date-time", "title": "End"}}, "additionalProperties": false, "type": "object", "required": ["start", "end"], "title": "TimeSlot"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}