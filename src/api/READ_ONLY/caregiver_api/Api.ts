/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** GenderEnum */
export enum GenderEnum {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

/** CalendarEvent */
export interface CalendarEvent {
  /**
   * Start
   * @format date-time
   */
  start: string;
  /**
   * End
   * @format date-time
   */
  end: string;
  /** Caregivers */
  caregivers?: CaregiverAvailabilityResponse[];
  /** Isavailable */
  isAvailable: boolean;
}

/** Caregiver */
export interface Caregiver {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Userid */
  userId?: number | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: number[] | Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** CaregiverAvailability */
export interface CaregiverAvailability {
  /** Eventsbydate */
  eventsByDate?: Record<string, CalendarEvent[]>;
}

/** CaregiverAvailabilityByDatesRequest */
export interface CaregiverAvailabilityByDatesRequest {
  /**
   * Dates
   * List of individual dates to check (UTC)
   */
  dates: string[];
  /**
   * Durationminutes
   * Slot duration in minutes
   * @exclusiveMin 0
   */
  durationMinutes: number;
  /**
   * Caregiverids
   * Filter by caregiver IDs
   */
  caregiverIds?: number[] | null;
  /**
   * Serviceids
   * Filter by caregiver service IDs
   */
  serviceIds?: number[] | null;
}

/** CaregiverAvailabilityResponse */
export interface CaregiverAvailabilityResponse {
  /** Caregiverid */
  caregiverId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
}

/** CaregiverCreate */
export interface CaregiverCreate {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Userid */
  userId?: number | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /**
   * Services
   * @minLength 1
   */
  services: number[] | Service[];
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
}

/** CaregiverMinified */
export interface CaregiverMinified {
  /** Caregiverid */
  caregiverId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
}

/** CaregiverShift */
export interface CaregiverShift {
  /** Caregivershiftid */
  caregiverShiftId: number;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Periodfrom
   * @format date-time
   */
  periodFrom: string;
  /**
   * Periodto
   * @format date-time
   */
  periodTo: string;
  /** Notes */
  notes?: string | null;
}

/** CaregiverShiftsPaginatedResponse */
export interface CaregiverShiftsPaginatedResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: CaregiverShift[];
}

/** CaregiverUpdate */
export interface CaregiverUpdate {
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Userid */
  userId?: number | null;
  /** Services */
  services?: number[] | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /** Active */
  active?: boolean | null;
}

/** CaregiverWithServices */
export interface CaregiverWithServices {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Userid */
  userId?: number | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** CaregiversGetAllMinifiedPaginationResponse */
export interface CaregiversGetAllMinifiedPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: CaregiverMinified[];
}

/** CaregiversGetAllPaginationResponse */
export interface CaregiversGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: CaregiverWithServices[];
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/**
 * Service
 * Model representing a Homecare Service offering.
 */
export interface Service {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   * @default 0
   */
  estimatedTimeMinutes?: number | null;
  /**
   * Costineuros
   * Estimated cost of the service in Euros
   */
  costInEuros?: number | null;
}

/** ShiftCreate */
export interface ShiftCreate {
  /** Notes */
  notes: string | null;
  /**
   * Periodfrom
   * @format date-time
   */
  periodFrom: string;
  /**
   * Periodto
   * @format date-time
   */
  periodTo: string;
}

/** ShiftUpdate */
export interface ShiftUpdate {
  /** Notes */
  notes: string | null;
  /** Periodfrom */
  periodFrom: string | null;
  /** Periodto */
  periodTo: string | null;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/caregivers-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title caregivers
 * @version 1.0.0
 * @baseUrl /api/v1/caregivers-api/
 *
 *
 * Microservice for managing home care caregivers.
 *
 * ## Features
 * * Create, read, update, and delete caregiver records
 * * Manage caregiver shifts and services
 * * Secure endpoints with authentication and authorization
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 * Admin endpoints require additional admin permissions.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  caregivers = {
    /**
     * @description Returns a list of caregivers who are available in the specified time range. A caregiver is considered available if they do not have any assigned visits overlapping the given `from`–`to` interval. Optionally filter caregivers by `service_id` to only include those who can provide the specified service.
     *
     * @tags Caregivers
     * @name CaregiversAvailableCaregiversAvailableGet
     * @summary Get available caregivers
     * @request GET:/caregivers/available
     * @secure
     */
    caregiversAvailableCaregiversAvailableGet: (
      query: {
        /**
         * From
         * Start of the time range (inclusive, ISO8601)
         * @format date-time
         * @example "2025-09-12T13:00:00+00:00"
         */
        from: string;
        /**
         * To
         * End of the time range (exclusive, ISO8601)
         * @format date-time
         * @example "2025-09-12T15:00:00+00:00"
         */
        to: string;
        /**
         * Service Ids
         * Optional service filter
         * @example [1]
         */
        service_ids?: number[] | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<CaregiverWithServices[], void>({
        path: `/caregivers/available`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns availability for caregivers across non-continuous dates. Filters by duration, caregiver IDs, and service IDs.
     *
     * @tags Caregivers Availability
     * @name GetAvailableCaregiversByDatesCaregiversAvailabilityByDatesPost
     * @summary Get available caregivers on specific dates
     * @request POST:/caregivers/availability/by-dates
     * @secure
     */
    getAvailableCaregiversByDatesCaregiversAvailabilityByDatesPost: (
      data: CaregiverAvailabilityByDatesRequest,
      params: RequestParams = {},
    ) =>
      this.request<CaregiverAvailability, HTTPValidationError | void>({
        path: `/caregivers/availability/by-dates`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns caregiver availability within a continuous datetime range. Optionally filters by slot duration, caregiver IDs, and service IDs.
     *
     * @tags Caregivers Availability
     * @name GetAvailableCaregiversByRangeCaregiversAvailabilityByRangeGet
     * @summary Get available caregivers in a datetime range
     * @request GET:/caregivers/availability/by-range
     * @secure
     */
    getAvailableCaregiversByRangeCaregiversAvailabilityByRangeGet: (
      query: {
        /**
         * From Date
         * Start of date range (UTC)
         * @format date
         * @example "2020-10-08"
         */
        from_date: string;
        /**
         * To Date
         * End of date range (UTC)
         * @format date
         * @example "2020-10-15"
         */
        to_date: string;
        /**
         * Duration Minutes
         * Slot duration in minutes
         * @default 30
         * @example 60
         */
        duration_minutes?: number | null;
        /**
         * Caregiver Ids
         * Filter by caregiver IDs
         * @example [1,2,3]
         */
        caregiver_ids?: number[] | null;
        /**
         * Service Ids
         * Filter by caregiver service IDs
         * @example [10,20]
         */
        service_ids?: number[] | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<CaregiverAvailability, HTTPValidationError | void>({
        path: `/caregivers/availability/by-range`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a paginated list of caregivers with optional filters. Provide 'query' (min 3 chars) to perform free-text search across first/last name, email, phone, and service names, combined with filters.
     *
     * @tags Caregivers
     * @name GetCaregiversCaregiversGet
     * @summary Get caregivers (list or search)
     * @request GET:/caregivers
     * @secure
     */
    getCaregiversCaregiversGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
        /**
         * Query
         * Free text query; if not given return all; if given (>3 chars) searches
         */
        query?: string | null;
        /**
         * Services
         * Filter by service IDs
         */
        services?: number[] | null;
        /**
         * Active
         * Filter by active status
         */
        active?: boolean | null;
        /**
         * City
         * Filter by city (exact match)
         */
        city?: string | null;
        /**
         * Skills
         * Filter by skills (match any)
         */
        skills?: string[] | null;
        /**
         * Languages Spoken
         * Filter by languages spoken (match any)
         */
        languages_spoken?: string[] | null;
        /**
         * Rating Min
         * Minimum rating
         */
        rating_min?: number | null;
        /**
         * Rating Max
         * Maximum rating
         */
        rating_max?: number | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        CaregiversGetAllPaginationResponse,
        HTTPValidationError | void
      >({
        path: `/caregivers`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a new caregiver
     *
     * @tags Caregivers
     * @name CreateCaregiverCaregiversPost
     * @summary Create new caregiver
     * @request POST:/caregivers
     * @secure
     */
    createCaregiverCaregiversPost: (
      data: CaregiverCreate,
      params: RequestParams = {},
    ) =>
      this.request<Caregiver, void | HTTPValidationError>({
        path: `/caregivers`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a list of all caregivers
     *
     * @tags Caregivers
     * @name GetCaregiversMinCaregiversMinifiedGet
     * @summary Get all caregivers with minified response schema
     * @request GET:/caregivers/minified
     * @secure
     */
    getCaregiversMinCaregiversMinifiedGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
        /**
         * Services
         * Filter by service IDs
         */
        services?: number[] | null;
        /**
         * Active
         * Filter by active status
         */
        active?: boolean | null;
        /**
         * City
         * Filter by city (exact match)
         */
        city?: string | null;
        /**
         * Skills
         * Filter by skills (match any)
         */
        skills?: string[] | null;
        /**
         * Languages Spoken
         * Filter by languages spoken (match any)
         */
        languages_spoken?: string[] | null;
        /**
         * Rating Min
         * Minimum rating
         */
        rating_min?: number | null;
        /**
         * Rating Max
         * Maximum rating
         */
        rating_max?: number | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        CaregiversGetAllMinifiedPaginationResponse,
        HTTPValidationError | void
      >({
        path: `/caregivers/minified`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a caregiver by their ID
     *
     * @tags Caregivers
     * @name GetCaregiverCaregiversCaregiverIdGet
     * @summary Get caregiver by ID
     * @request GET:/caregivers/{caregiver_id}
     * @secure
     */
    getCaregiverCaregiversCaregiverIdGet: (
      caregiverId: number,
      params: RequestParams = {},
    ) =>
      this.request<Caregiver, void | HTTPValidationError>({
        path: `/caregivers/${caregiverId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing caregiver
     *
     * @tags Caregivers
     * @name UpdateCaregiverCaregiversCaregiverIdPut
     * @summary Update caregiver
     * @request PUT:/caregivers/{caregiver_id}
     * @secure
     */
    updateCaregiverCaregiversCaregiverIdPut: (
      caregiverId: number,
      data: CaregiverUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Caregiver, void | HTTPValidationError>({
        path: `/caregivers/${caregiverId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a caregiver by id :param request: The request object :type request: Request :param caregiver_id: The ID of the caregiver to delete :type caregiver_id: int :raises HTTPException: If the caregiver is not found :raises HTTPException: If an error occurs while deleting the caregiver :return: No content :rtype: None
     *
     * @tags Caregivers
     * @name DeleteCaregiverCaregiversCaregiverIdDelete
     * @summary Delete Caregiver
     * @request DELETE:/caregivers/{caregiver_id}
     * @secure
     */
    deleteCaregiverCaregiversCaregiverIdDelete: (
      caregiverId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/caregivers/${caregiverId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description Add a new shift for a caregiver
     *
     * @tags Shifts
     * @name AddShiftCaregiversCaregiverIdShiftsPost
     * @summary Add a shift on the caregiver
     * @request POST:/caregivers/{caregiver_id}/shifts
     * @secure
     */
    addShiftCaregiversCaregiverIdShiftsPost: (
      caregiverId: number,
      data: ShiftCreate,
      params: RequestParams = {},
    ) =>
      this.request<number, void | HTTPValidationError>({
        path: `/caregivers/${caregiverId}/shifts`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Fetches shifts assigned to a caregiver with optional from/to date filters.
     *
     * @tags Shifts
     * @name GetCaregiverShiftsCaregiversCaregiverIdShiftsGet
     * @summary Get shifts for a caregiver
     * @request GET:/caregivers/{caregiver_id}/shifts
     * @secure
     */
    getCaregiverShiftsCaregiversCaregiverIdShiftsGet: (
      caregiverId: number,
      query?: {
        /**
         * Offset
         * Pagination offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Pagination limit
         * @default 100
         */
        limit?: number;
        /**
         * From
         * Filter from this date
         */
        from?: string | null;
        /**
         * To
         * Filter to this date
         */
        to?: string | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        CaregiverShiftsPaginatedResponse,
        HTTPValidationError | void
      >({
        path: `/caregivers/${caregiverId}/shifts`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns all caregiver shifts where the shift start date matches any of the provided dates.
     *
     * @tags Shifts
     * @name GetShiftsByDatesCaregiversShiftsByDatesGet
     * @summary Fetch all caregiver shifts for a list of dates
     * @request GET:/caregivers/shifts/by-dates
     * @secure
     */
    getShiftsByDatesCaregiversShiftsByDatesGet: (
      query: {
        /**
         * Offset
         * Pagination offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Pagination limit
         * @default 100
         */
        limit?: number;
        /**
         * Dates
         * List of dates (e.g. ?dates=2025-07-18&dates=2025-07-19)
         */
        dates: string[];
      },
      params: RequestParams = {},
    ) =>
      this.request<
        CaregiverShiftsPaginatedResponse,
        HTTPValidationError | void
      >({
        path: `/caregivers/shifts/by-dates`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Updates an existing caregiver shift's notes, start time, and end time.
     *
     * @tags Shifts
     * @name UpdateShiftCaregiversCaregiverIdShiftsShiftIdPut
     * @summary Update a caregiver shift
     * @request PUT:/caregivers/{caregiver_id}/shifts/{shift_id}
     * @secure
     */
    updateShiftCaregiversCaregiverIdShiftsShiftIdPut: (
      caregiverId: number,
      shiftId: number,
      data: ShiftUpdate,
      params: RequestParams = {},
    ) =>
      this.request<CaregiverShift, HTTPValidationError | void>({
        path: `/caregivers/${caregiverId}/shifts/${shiftId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Deletes a caregiver shift by its ID.
     *
     * @tags Shifts
     * @name DeleteShiftCaregiversCaregiverIdShiftsShiftIdDelete
     * @summary Delete a caregiver shift
     * @request DELETE:/caregivers/{caregiver_id}/shifts/{shift_id}
     * @secure
     */
    deleteShiftCaregiversCaregiverIdShiftsShiftIdDelete: (
      caregiverId: number,
      shiftId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/caregivers/${caregiverId}/shifts/${shiftId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),
  };
  byUser = {
    /**
     * @description Retrieve a caregiver by their user ID including related services
     *
     * @tags Caregivers
     * @name GetCaregiverByUserIdByUserUserIdGet
     * @summary Get caregiver by user ID
     * @request GET:/by-user/{user_id}
     * @secure
     */
    getCaregiverByUserIdByUserUserIdGet: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<CaregiverWithServices, void | HTTPValidationError>({
        path: `/by-user/${userId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
}
