/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** GenderEnum */
export enum GenderEnum {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

/** Caregiver */
export interface Caregiver {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /**
   * Username
   * @minLength 4
   */
  username: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: number[] | Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** CaregiverAvailability */
export interface CaregiverAvailability {
  /** Caregiverid */
  caregiverId: number;
  /** Availability */
  availability: DateAvailability[];
}

/** CaregiverAvailabilityByDatesRequest */
export interface CaregiverAvailabilityByDatesRequest {
  /**
   * Dates
   * List of individual dates to check (UTC)
   */
  dates: string[];
  /**
   * Durationminutes
   * Slot duration in minutes
   */
  durationMinutes?: number | null;
  /**
   * Caregiverids
   * Filter by caregiver IDs
   */
  caregiverIds?: number[] | null;
  /**
   * Serviceids
   * Filter by caregiver service IDs
   */
  serviceIds?: number[] | null;
}

/** CaregiverCreate */
export interface CaregiverCreate {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /**
   * Username
   * @minLength 4
   */
  username: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: number[] | Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
}

/** CaregiverShift */
export interface CaregiverShift {
  /** Caregivershiftid */
  caregiverShiftId: number;
  /** Caregiverid */
  caregiverId: number;
  /** Notes */
  notes?: string | null;
  /** Periodfrom */
  periodFrom?: string | null;
  /** Periodto */
  periodTo?: string | null;
}

/** CaregiverShiftsByDatesResponse */
export interface CaregiverShiftsByDatesResponse {
  /** Caregivershiftid */
  caregiverShiftId: number;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Periodfrom
   * @format date-time
   */
  periodFrom: string;
  /**
   * Periodto
   * @format date-time
   */
  periodTo: string;
  /** Notes */
  notes?: string | null;
}

/** CaregiverUpdate */
export interface CaregiverUpdate {
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Services */
  services?: number[] | Service[] | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  /** Username */
  username?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /** Active */
  active?: boolean | null;
}

/** DateAvailability */
export interface DateAvailability {
  /**
   * Date
   * @format date
   */
  date: string;
  /** Slots */
  slots: TimeSlot[];
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** Service */
export interface Service {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   */
  estimatedTimeMinutes: number;
  /**
   * Costineuros
   * Estimated cost of the service in Euros
   */
  costInEuros?: number | null;
  /**
   * Createdat
   * Timestamp when the service record was created
   * @format date-time
   */
  createdAt: string;
  /**
   * Updatedat
   * Timestamp of the last service record update
   */
  updatedAt?: string | null;
}

/** ShiftCreate */
export interface ShiftCreate {
  /** Notes */
  notes: string | null;
  /**
   * Periodfrom
   * @format date-time
   */
  periodFrom: string;
  /**
   * Periodto
   * @format date-time
   */
  periodTo: string;
}

/** TimeSlot */
export interface TimeSlot {
  /**
   * Start
   * @format date-time
   */
  start: string;
  /**
   * End
   * @format date-time
   */
  end: string;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Caregivers-Service
 * @version 1.0.0
 *
 *
 *     Microservice for managing home care caregivers.
 *
 *     ## Features
 *     * Create, read, update, and delete caregiver records
 *     * Manage caregiver shifts and services
 *     * Secure endpoints with authentication and authorization
 *
 *     ## Authentication
 *     All endpoints require authentication using JWT tokens.
 *     Admin endpoints require additional admin permissions.
 *
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * No description
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health Check
     * @request GET:/health
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  caregivers = {
    /**
     * @description Returns availability for caregivers across non-continuous dates. Filters by duration, caregiver IDs, and service IDs.
     *
     * @tags Caregivers Availability
     * @name GetAvailableCaregiversByDatesCaregiversAvailabilityByDatesPost
     * @summary Get available caregivers on specific dates
     * @request POST:/caregivers/availability/by-dates
     */
    getAvailableCaregiversByDatesCaregiversAvailabilityByDatesPost: (
      data: CaregiverAvailabilityByDatesRequest,
      params: RequestParams = {},
    ) =>
      this.request<CaregiverAvailability[], HTTPValidationError>({
        path: `/caregivers/availability/by-dates`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns caregiver availability within a continuous datetime range. Optionally filters by slot duration, caregiver IDs, and service IDs.
     *
     * @tags Caregivers Availability
     * @name GetAvailableCaregiversByRangeCaregiversAvailabilityByRangeGet
     * @summary Get available caregivers in a datetime range
     * @request GET:/caregivers/availability/by-range
     */
    getAvailableCaregiversByRangeCaregiversAvailabilityByRangeGet: (
      query: {
        /**
         * From Datetime
         * Start of datetime range (UTC)
         * @format date-time
         */
        from_datetime: string;
        /**
         * To Datetime
         * End of datetime range (UTC)
         * @format date-time
         */
        to_datetime: string;
        /**
         * Duration Minutes
         * Slot duration in minutes
         */
        duration_minutes?: number | null;
        /**
         * Caregiver Ids
         * Filter by caregiver IDs
         */
        caregiver_ids?: number[] | null;
        /**
         * Service Ids
         * Filter by caregiver service IDs
         */
        service_ids?: number[] | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<CaregiverAvailability[], HTTPValidationError>({
        path: `/caregivers/availability/by-range`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Caregivers
     * @name GetCaregiversCaregiversGet
     * @summary Get Caregivers
     * @request GET:/caregivers
     */
    getCaregiversCaregiversGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
        /**
         * Username
         * Filter by username (partial match)
         */
        username?: string | null;
        /**
         * Services
         * Filter by service IDs
         */
        services?: number[] | null;
        /**
         * Active
         * Filter by active status
         */
        active?: boolean | null;
        /**
         * City
         * Filter by city (exact match)
         */
        city?: string | null;
        /**
         * Skills
         * Filter by skills (match any)
         */
        skills?: string[] | null;
        /**
         * Languages Spoken
         * Filter by languages spoken (match any)
         */
        languages_spoken?: string[] | null;
        /**
         * Rating Min
         * Minimum rating
         */
        rating_min?: number | null;
        /**
         * Rating Max
         * Maximum rating
         */
        rating_max?: number | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<Caregiver[], HTTPValidationError>({
        path: `/caregivers`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Caregivers
     * @name CreateCaregiverCaregiversPost
     * @summary Create Caregiver
     * @request POST:/caregivers
     */
    createCaregiverCaregiversPost: (
      data: CaregiverCreate,
      params: RequestParams = {},
    ) =>
      this.request<Caregiver, HTTPValidationError>({
        path: `/caregivers`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Caregivers
     * @name GetCaregiverCaregiversCaregiverIdGet
     * @summary Get Caregiver
     * @request GET:/caregivers/{caregiver_id}
     */
    getCaregiverCaregiversCaregiverIdGet: (
      caregiverId: number,
      params: RequestParams = {},
    ) =>
      this.request<Caregiver, HTTPValidationError>({
        path: `/caregivers/${caregiverId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Caregivers
     * @name UpdateCaregiverCaregiversCaregiverIdPut
     * @summary Update Caregiver
     * @request PUT:/caregivers/{caregiver_id}
     */
    updateCaregiverCaregiversCaregiverIdPut: (
      caregiverId: number,
      data: CaregiverUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Caregiver, HTTPValidationError>({
        path: `/caregivers/${caregiverId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Caregivers
     * @name DeleteCaregiverCaregiversCaregiverIdDelete
     * @summary Delete Caregiver
     * @request DELETE:/caregivers/{caregiver_id}
     */
    deleteCaregiverCaregiversCaregiverIdDelete: (
      caregiverId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/caregivers/${caregiverId}`,
        method: "DELETE",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Shifts
     * @name AddShiftCaregiversCaregiverIdShiftsPost
     * @summary Add Shift
     * @request POST:/caregivers/{caregiver_id}/shifts
     */
    addShiftCaregiversCaregiverIdShiftsPost: (
      caregiverId: number,
      data: ShiftCreate,
      params: RequestParams = {},
    ) =>
      this.request<number, HTTPValidationError>({
        path: `/caregivers/${caregiverId}/shifts`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Fetches shifts assigned to a caregiver with optional from/to date filters.
     *
     * @tags Shifts
     * @name GetCaregiverShiftsCaregiversCaregiverIdShiftsGet
     * @summary Get shifts for a caregiver
     * @request GET:/caregivers/{caregiver_id}/shifts
     */
    getCaregiverShiftsCaregiversCaregiverIdShiftsGet: (
      caregiverId: number,
      query?: {
        /**
         * From Date
         * Filter from this date
         */
        from_date?: string | null;
        /**
         * To Date
         * Filter to this date
         */
        to_date?: string | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<CaregiverShift[], HTTPValidationError>({
        path: `/caregivers/${caregiverId}/shifts`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns all caregiver shifts where the shift start date matches any of the provided dates.
     *
     * @tags Shifts
     * @name GetShiftsByDatesCaregiversShiftsByDatesGet
     * @summary Fetch all caregiver shifts for a list of dates
     * @request GET:/caregivers/shifts/by-dates
     */
    getShiftsByDatesCaregiversShiftsByDatesGet: (
      query: {
        /**
         * Dates
         * List of dates (e.g. ?dates=2025-07-18&dates=2025-07-19)
         */
        dates: string[];
      },
      params: RequestParams = {},
    ) =>
      this.request<CaregiverShiftsByDatesResponse[], HTTPValidationError>({
        path: `/caregivers/shifts/by-dates`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Updates an existing caregiver shift's notes, start time, and end time.
     *
     * @tags Shifts
     * @name UpdateShiftCaregiversCaregiverIdShiftsShiftIdPut
     * @summary Update a caregiver shift
     * @request PUT:/caregivers/{caregiver_id}/shifts/{shift_id}
     */
    updateShiftCaregiversCaregiverIdShiftsShiftIdPut: (
      caregiverId: number,
      shiftId: number,
      data: ShiftCreate,
      params: RequestParams = {},
    ) =>
      this.request<CaregiverShift, HTTPValidationError>({
        path: `/caregivers/${caregiverId}/shifts/${shiftId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Deletes a caregiver shift by its ID.
     *
     * @tags Shifts
     * @name DeleteShiftCaregiversCaregiverIdShiftsShiftIdDelete
     * @summary Delete a caregiver shift
     * @request DELETE:/caregivers/{caregiver_id}/shifts/{shift_id}
     */
    deleteShiftCaregiversCaregiverIdShiftsShiftIdDelete: (
      caregiverId: number,
      shiftId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/caregivers/${caregiverId}/shifts/${shiftId}`,
        method: "DELETE",
        ...params,
      }),
  };
}
