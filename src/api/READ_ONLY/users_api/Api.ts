/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** AppUser */
export interface AppUser {
  /**
   * Kcid
   * @format uuid
   */
  kcId: string;
  /**
   * Email
   * @format email
   */
  email: string;
  /** Displayname */
  displayName?: string | null;
  /** Avatarurl */
  avatarUrl?: string | null;
  /** Id */
  id: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /**
   * Updatedat
   * @format date-time
   */
  updatedAt: string;
}

/** AppUserCreate */
export interface AppUserCreate {
  /**
   * Kcid
   * @format uuid
   */
  kcId: string;
  /**
   * Email
   * @format email
   */
  email: string;
  /** Displayname */
  displayName?: string | null;
  /** Avatarurl */
  avatarUrl?: string | null;
}

/** AppUserRole */
export interface AppUserRole {
  /** Userid */
  userId: number;
  /** Roleid */
  roleId: number;
}

/** AppUserUpdate */
export interface AppUserUpdate {
  /** Displayname */
  displayName?: string | null;
  /** Avatarurl */
  avatarUrl?: string | null;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** Role */
export interface Role {
  /** Name */
  name: string;
  /** Description */
  description?: string | null;
  /** Id */
  id: number;
}

/** RoleCreate */
export interface RoleCreate {
  /** Name */
  name: string;
  /** Description */
  description?: string | null;
}

/** RoleUpdate */
export interface RoleUpdate {
  /** Name */
  name?: string | null;
  /** Description */
  description?: string | null;
}

/** UserLoginAudit */
export interface UserLoginAudit {
  /** Userid */
  userId: number;
  /**
   * Kcid
   * @format uuid
   */
  kcId: string;
  /** Ipaddr */
  ipAddr?: string | null;
  /** Useragent */
  userAgent?: string | null;
  /** Id */
  id: number;
  /**
   * Loginat
   * @format date-time
   */
  loginAt: string;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Users-Service
 * @version 1.0.0
 *
 *
 *     Microservice for managing home care users.
 *
 *     ## Features
 *     * Create, read, update, and delete user records
 *     * Secure endpoints with authentication and authorization
 *
 *     ## Authentication
 *     All endpoints require authentication using JWT tokens.
 *     Admin endpoints require additional admin permissions.
 *
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  users = {
    /**
     * @description Retrieve a list of all users with pagination support
     *
     * @tags Users
     * @name GetUsersUsersGet
     * @summary Get all users
     * @request GET:/users
     */
    getUsersUsersGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<AppUser[], HTTPValidationError>({
        path: `/users`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a new app user
     *
     * @tags Users
     * @name CreateUserUsersPost
     * @summary Create new user
     * @request POST:/users
     */
    createUserUsersPost: (data: AppUserCreate, params: RequestParams = {}) =>
      this.request<AppUser, HTTPValidationError>({
        path: `/users`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a specific user by their ID
     *
     * @tags Users
     * @name GetUserUsersUserIdGet
     * @summary Get user by ID
     * @request GET:/users/{user_id}
     */
    getUserUsersUserIdGet: (userId: number, params: RequestParams = {}) =>
      this.request<AppUser, void | HTTPValidationError>({
        path: `/users/${userId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing user's information
     *
     * @tags Users
     * @name UpdateUserUsersUserIdPut
     * @summary Update user
     * @request PUT:/users/{user_id}
     */
    updateUserUsersUserIdPut: (
      userId: number,
      data: AppUserUpdate,
      params: RequestParams = {},
    ) =>
      this.request<AppUser, HTTPValidationError>({
        path: `/users/${userId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a user by ID
     *
     * @tags Users
     * @name DeleteUserUsersUserIdDelete
     * @summary Delete user
     * @request DELETE:/users/{user_id}
     */
    deleteUserUsersUserIdDelete: (userId: number, params: RequestParams = {}) =>
      this.request<void, HTTPValidationError>({
        path: `/users/${userId}`,
        method: "DELETE",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Users
     * @name ListRolesForUserUsersUserIdRolesGet
     * @summary List roles for a user
     * @request GET:/users/{user_id}/roles
     */
    listRolesForUserUsersUserIdRolesGet: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<Role[], HTTPValidationError>({
        path: `/users/${userId}/roles`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Users
     * @name AddRoleToUserUsersUserIdRolesRoleIdPost
     * @summary Assign role to user
     * @request POST:/users/{user_id}/roles/{role_id}
     */
    addRoleToUserUsersUserIdRolesRoleIdPost: (
      userId: number,
      roleId: number,
      params: RequestParams = {},
    ) =>
      this.request<AppUserRole, HTTPValidationError>({
        path: `/users/${userId}/roles/${roleId}`,
        method: "POST",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Users
     * @name RemoveRoleFromUserUsersUserIdRolesRoleIdDelete
     * @summary Remove role from user
     * @request DELETE:/users/{user_id}/roles/{role_id}
     */
    removeRoleFromUserUsersUserIdRolesRoleIdDelete: (
      userId: number,
      roleId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/users/${userId}/roles/${roleId}`,
        method: "DELETE",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Users
     * @name GetLoginAuditUsersUserIdLoginsGet
     * @summary User login audit
     * @request GET:/users/{user_id}/logins
     */
    getLoginAuditUsersUserIdLoginsGet: (
      userId: number,
      query?: {
        /**
         * Limit
         * @default 50
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<UserLoginAudit[], HTTPValidationError>({
        path: `/users/${userId}/logins`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),
  };
  roles = {
    /**
     * No description
     *
     * @tags Roles
     * @name ListRolesRolesGet
     * @summary Get all roles
     * @request GET:/roles
     */
    listRolesRolesGet: (params: RequestParams = {}) =>
      this.request<Role[], any>({
        path: `/roles`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Roles
     * @name CreateRoleRolesPost
     * @summary Create role
     * @request POST:/roles
     */
    createRoleRolesPost: (data: RoleCreate, params: RequestParams = {}) =>
      this.request<Role, HTTPValidationError>({
        path: `/roles`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Roles
     * @name GetRoleRolesRoleIdGet
     * @summary Get role by ID
     * @request GET:/roles/{role_id}
     */
    getRoleRolesRoleIdGet: (roleId: number, params: RequestParams = {}) =>
      this.request<Role, void | HTTPValidationError>({
        path: `/roles/${roleId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Roles
     * @name UpdateRoleRolesRoleIdPut
     * @summary Update role
     * @request PUT:/roles/{role_id}
     */
    updateRoleRolesRoleIdPut: (
      roleId: number,
      data: RoleUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Role, HTTPValidationError>({
        path: `/roles/${roleId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Roles
     * @name DeleteRoleRolesRoleIdDelete
     * @summary Delete role
     * @request DELETE:/roles/{role_id}
     */
    deleteRoleRolesRoleIdDelete: (roleId: number, params: RequestParams = {}) =>
      this.request<void, HTTPValidationError>({
        path: `/roles/${roleId}`,
        method: "DELETE",
        ...params,
      }),
  };
}
