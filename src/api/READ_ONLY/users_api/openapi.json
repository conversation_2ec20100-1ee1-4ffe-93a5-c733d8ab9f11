{"openapi": "3.1.0", "info": {"title": "Users-Service", "description": "\n    Microservice for managing home care users.\n\n    ## Features\n    * Create, read, update, and delete user records\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/users": {"get": {"tags": ["Users"], "summary": "Get all users", "description": "Retrieve a list of all users with pagination support", "operationId": "get_users_users_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of user records", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AppUser"}, "title": "Response Get Users Users Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Users"], "summary": "Create new user", "description": "Create a new app user", "operationId": "create_user_users_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppUserCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/{user_id}": {"get": {"tags": ["Users"], "summary": "Get user by ID", "description": "Retrieve a specific user by their ID", "operationId": "get_user_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppUser"}}}}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user", "description": "Update an existing user's information", "operationId": "update_user_users__user_id__put", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppUserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete user", "description": "Delete a user by ID", "operationId": "delete_user_users__user_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/{user_id}/roles": {"get": {"tags": ["Users"], "summary": "List roles for a user", "operationId": "list_roles_for_user_users__user_id__roles_get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "title": "Response List Roles For User Users  User Id  Roles Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/{user_id}/roles/{role_id}": {"post": {"tags": ["Users"], "summary": "Assign role to user", "operationId": "add_role_to_user_users__user_id__roles__role_id__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppUserRole"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Remove role from user", "operationId": "remove_role_from_user_users__user_id__roles__role_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/{user_id}/logins": {"get": {"tags": ["Users"], "summary": "User login audit", "operationId": "get_login_audit_users__user_id__logins_get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserLoginAudit"}, "title": "Response Get Login Audit Users  User Id  Logins Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/roles": {"get": {"tags": ["Roles"], "summary": "Get all roles", "operationId": "list_roles_roles_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Role"}, "type": "array", "title": "Response List Roles Roles Get"}}}}}}, "post": {"tags": ["Roles"], "summary": "Create role", "operationId": "create_role_roles_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/roles/{role_id}": {"get": {"tags": ["Roles"], "summary": "Get role by ID", "operationId": "get_role_roles__role_id__get", "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "404": {"description": "Role not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Roles"], "summary": "Update role", "operationId": "update_role_roles__role_id__put", "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Roles"], "summary": "Delete role", "operationId": "delete_role_roles__role_id__delete", "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AppUser": {"properties": {"kcId": {"type": "string", "format": "uuid", "title": "Kcid"}, "email": {"type": "string", "format": "email", "title": "Email"}, "displayName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Displayname"}, "avatarUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatarurl"}, "id": {"type": "integer", "title": "Id"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"type": "string", "format": "date-time", "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["kcId", "email", "id", "createdAt", "updatedAt"], "title": "AppUser"}, "AppUserCreate": {"properties": {"kcId": {"type": "string", "format": "uuid", "title": "Kcid"}, "email": {"type": "string", "format": "email", "title": "Email"}, "displayName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Displayname"}, "avatarUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatarurl"}}, "additionalProperties": false, "type": "object", "required": ["kcId", "email"], "title": "AppUserCreate"}, "AppUserRole": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "roleId": {"type": "integer", "title": "<PERSON><PERSON>"}}, "additionalProperties": false, "type": "object", "required": ["userId", "roleId"], "title": "AppUserRole"}, "AppUserUpdate": {"properties": {"displayName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Displayname"}, "avatarUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatarurl"}}, "additionalProperties": false, "type": "object", "title": "AppUserUpdate"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Role": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "id": {"type": "integer", "title": "Id"}}, "additionalProperties": false, "type": "object", "required": ["name", "id"], "title": "Role"}, "RoleCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "RoleCreate"}, "RoleUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "RoleUpdate"}, "UserLoginAudit": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "kcId": {"type": "string", "format": "uuid", "title": "Kcid"}, "ipAddr": {"anyOf": [{"type": "string", "format": "ipv4"}, {"type": "string", "format": "ipv6"}, {"type": "null"}], "title": "<PERSON>pad<PERSON>"}, "userAgent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Useragent"}, "id": {"type": "integer", "title": "Id"}, "loginAt": {"type": "string", "format": "date-time", "title": "Loginat"}}, "additionalProperties": false, "type": "object", "required": ["userId", "kcId", "id", "loginAt"], "title": "UserLoginAudit"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}