{"openapi": "3.1.0", "info": {"title": "visit", "description": "\nMicroservice for managing home care visits.\n\n## Features\n* Create, read, update, add attachments, and delete visits records\n* Create visit services\n* Secure endpoints with authentication and authorization\n\n## Authentication\nAll endpoints require authentication using JWT tokens.\nAdmin endpoints require additional admin permissions.\n", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/visits": {"get": {"tags": ["Visits"], "summary": "Lists all visits", "description": "Fetch a paginated list of visits with optional filters.\n    You can filter by:\n    - Client ID\n    - Caregiver ID\n    - Status (e.g., scheduled, completed, cancelled)\n    - Start time range\n    - Whether visits have attachments\n    - Service IDs\n\n    Results are sorted by `start_time` ascending by default.", "operationId": "list_visits_visits_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of records to skip (for pagination)", "default": 0, "title": "Offset"}, "description": "Number of records to skip (for pagination)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max number of records to return (for pagination)", "default": 100, "title": "Limit"}, "description": "Max number of records to return (for pagination)"}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by Client ID", "title": "Client Id"}, "description": "Filter by Client ID"}, {"name": "caregiver_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by caregiver IDs", "title": "Caregiver Ids"}, "description": "Filter by caregiver IDs"}, {"name": "visit_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by visit status (e.g. scheduled, completed, cancelled)", "title": "Visit Status"}, "description": "Filter by visit status (e.g. scheduled, completed, cancelled)"}, {"name": "start_time_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Return visits starting **after or at** this datetime (ISO 8601 format)", "title": "Start Time From"}, "description": "Return visits starting **after or at** this datetime (ISO 8601 format)"}, {"name": "start_time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Return visits starting **before or at** this datetime (ISO 8601 format)", "title": "Start Time To"}, "description": "Return visits starting **before or at** this datetime (ISO 8601 format)"}, {"name": "service_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by Service IDs", "title": "Service Ids"}, "description": "Filter by Service IDs"}, {"name": "has_attachments", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by presence of attachments (True = with attachments, False = without)", "title": "Has Attachments"}, "description": "Filter by presence of attachments (True = with attachments, False = without)"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitsGetAllPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Visits"], "summary": "Create visits (batch)", "description": "Create one or more visits in a single request", "operationId": "create_visits_visits_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitCreate"}, "title": "Visits"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitPopulated"}, "title": "Response Create Visits Visits Post"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/minified": {"get": {"tags": ["Visits"], "summary": "Lists all visits with minified schema", "description": "Fetch a paginated list of visits with optional filters.\n    You can filter by:\n    - Client ID\n    - Caregiver ID\n    - Status (e.g., scheduled, completed, cancelled)\n    - Start time range\n    - Whether visits have attachments\n    - Service IDs\n\n    Results are sorted by `start_time` ascending by default.", "operationId": "list_visits_minified_visits_minified_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of records to skip (for pagination)", "default": 0, "title": "Offset"}, "description": "Number of records to skip (for pagination)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max number of records to return (for pagination)", "default": 100, "title": "Limit"}, "description": "Max number of records to return (for pagination)"}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by Client ID", "title": "Client Id"}, "description": "Filter by Client ID"}, {"name": "caregiver_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by caregiver IDs", "title": "Caregiver Ids"}, "description": "Filter by caregiver IDs"}, {"name": "visit_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by visit status (e.g. scheduled, completed, cancelled)", "title": "Visit Status"}, "description": "Filter by visit status (e.g. scheduled, completed, cancelled)"}, {"name": "start_time_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Return visits starting **after or at** this datetime (ISO 8601 format)", "title": "Start Time From"}, "description": "Return visits starting **after or at** this datetime (ISO 8601 format)"}, {"name": "start_time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Return visits starting **before or at** this datetime (ISO 8601 format)", "title": "Start Time To"}, "description": "Return visits starting **before or at** this datetime (ISO 8601 format)"}, {"name": "service_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by Service IDs", "title": "Service Ids"}, "description": "Filter by Service IDs"}, {"name": "has_attachments", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by presence of attachments (True = with attachments, False = without)", "title": "Has Attachments"}, "description": "Filter by presence of attachments (True = with attachments, False = without)"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitsGetAllMinifiedPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/search": {"get": {"tags": ["Visits"], "summary": "Search visits", "description": "Search visits in all properties by search query", "operationId": "search_visits_visits_search_get", "parameters": [{"name": "query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 3}, {"type": "null"}], "description": "Free text query", "title": "Query"}, "description": "Free text query"}, {"name": "from_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter visits starting from this datetime (ISO 8601 format)", "title": "From Date"}, "description": "Filter visits starting from this datetime (ISO 8601 format)"}, {"name": "to_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter visits up to this datetime (ISO 8601 format)", "title": "To Date"}, "description": "Filter visits up to this datetime (ISO 8601 format)"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Offset for pagination", "default": 0, "title": "Offset"}, "description": "Offset for pagination"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Limit for pagination", "default": 100, "title": "Limit"}, "description": "Limit for pagination"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitsGetAllPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}": {"get": {"tags": ["Visits"], "summary": "Get visit by ID", "description": "Retrieve a specific visit by its ID", "operationId": "get_visit_visits__visit_id__get", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitPopulated"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Visits"], "summary": "Update an existing visit", "description": "Update an existing visit", "operationId": "update_visit_visits__visit_id__put", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitUpdate"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitPopulated"}}}}, "400": {"description": "Bad request"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Visits"], "summary": "Update an existing visit", "description": "Update an existing visit", "operationId": "delete_visit_visits__visit_id__delete", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "responses": {"204": {"description": "Successful response"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/by-caregivers-and-dates": {"post": {"tags": ["Visits"], "summary": "Get visits for caregivers on specific dates", "description": "Returns visit times for the given caregiver IDs and dates.", "operationId": "post_visits_by_caregivers_and_dates_visits_by_caregivers_and_dates_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitQueryByCaregiversAndDates"}}}, "required": true}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/VisitPopulated"}, "type": "array", "title": "Response Post Visits By Caregivers And Dates Visits By Caregivers And Dates Post"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/suggested-from-availability": {"post": {"tags": ["Visits"], "summary": "Suggest visits based on caregiver availability", "description": "Returns suggested visit slots (not persisted) for a client based on available caregivers matching a time slot, service IDs, and optional caregiver filters.", "operationId": "post_suggested_visits_from_availability_visits_suggested_from_availability_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestedVisitsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SuggestedVisitGroup"}, "type": "array", "title": "Response Post Suggested Visits From Availability Visits Suggested From Availability Post"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/attachments": {"get": {"tags": ["Visit Attachments"], "summary": "List all attachments for a visit", "description": "Lists all attachments for a specific visit.", "operationId": "list_attachments_visits__visit_id__attachments_get", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the visit", "title": "Visit Id"}, "description": "The ID of the visit"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "The number of records to skip", "default": 0, "title": "Offset"}, "description": "The number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "The max number of records to return", "default": 100, "title": "Limit"}, "description": "The max number of records to return"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitAttachmentsPaginatedResponse"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Visit Attachments"], "summary": "Add an attachment to a visit", "description": "Adds an attachment to a specific visit.", "operationId": "add_visit_attachment_visits__visit_id__attachments_post", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the visit", "title": "Visit Id"}, "description": "The ID of the visit"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_add_visit_attachment_visits__visit_id__attachments_post"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachmentCreateResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/attachments/{attachment_id}/download": {"get": {"tags": ["Visit Attachments"], "summary": "Download a visit attachment", "description": "Downloads the content of a visit attachment.", "operationId": "download_attachment_visits__visit_id__attachments__attachment_id__download_get", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the visit", "title": "Visit Id"}, "description": "The ID of the visit"}, {"name": "attachment_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the attachment", "title": "Attachment Id"}, "description": "The ID of the attachment"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachmentWithContent"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/attachments/{attachment_id}": {"delete": {"tags": ["Visit Attachments"], "summary": "Delete an attachment by ID", "description": "Deletes a visit attachment by its ID.", "operationId": "delete_attachment_visits__visit_id__attachments__attachment_id__delete", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the visit", "title": "Visit Id"}, "description": "The ID of the visit"}, {"name": "attachment_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the attachment", "title": "Attachment Id"}, "description": "The ID of the attachment"}], "responses": {"204": {"description": "Successful response"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/services": {"post": {"tags": ["Visit Services"], "summary": "Add a service to a visit", "description": "Add a service to a visit", "operationId": "add_visit_service_visits__visit_id__services_post", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the visit to add the service to", "title": "Visit Id"}, "description": "The ID of the visit to add the service to"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddVisitService"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Visit Services"], "summary": "Lists services of a visit", "description": "Lists services of a visit", "operationId": "list_visit_services_visits__visit_id__services_get", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the visit to add the service to", "title": "Visit Id"}, "description": "The ID of the visit to add the service to"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "The number of records to skip", "default": 0, "title": "Offset"}, "description": "The number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "The number of records to return", "default": 100, "title": "Limit"}, "description": "The number of records to return"}], "responses": {"200": {"description": "The paginated list of the services", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitServicesPaginatedResponse"}}}}, "201": {"description": "Successful response"}, "400": {"description": "Bad request"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Visit Services"], "summary": "Upsert (update or create) multiple visit services", "description": "Upsert (update or create) multiple visit services for a specific visit", "operationId": "upsert_visit_services_visits__visit_id__services_put", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitServiceUpdateOrCreate"}, "title": "Services"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitService"}, "title": "Response Upsert Visit Services Visits  Visit Id  Services Put"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visit-services/{visit_service_id}": {"put": {"tags": ["Visit Services"], "summary": "Update a visit service", "description": "Update a visit service", "operationId": "update_visit_service_visit_services__visit_service_id__put", "parameters": [{"name": "visit_service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Service Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitServiceUpdate"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/services/{visit_service_id}": {"delete": {"tags": ["Visit Services"], "summary": "Delete a visit service", "description": "Delete a visit service", "operationId": "delete_visit_service_visits__visit_id__services__visit_service_id__delete", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}, {"name": "visit_service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Service Id"}}], "responses": {"204": {"description": "No content"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AddVisitService": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}}, "type": "object", "required": ["serviceId"], "title": "AddVisitService"}, "AttachmentBase": {"properties": {"attachmentId": {"type": "integer", "title": "Attachmentid"}, "filename": {"type": "string", "title": "Filename"}, "contentType": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contenttype"}, "uploadedAt": {"type": "string", "format": "date-time", "title": "Uploadedat"}}, "type": "object", "required": ["attachmentId", "filename", "uploadedAt"], "title": "AttachmentBase"}, "AttachmentCreateResponse": {"properties": {"attachmentId": {"type": "integer", "title": "Attachmentid"}}, "type": "object", "required": ["attachmentId"], "title": "AttachmentCreateResponse"}, "AttachmentWithContent": {"properties": {"attachmentId": {"type": "integer", "title": "Attachmentid"}, "filename": {"type": "string", "title": "Filename"}, "contentType": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contenttype"}, "uploadedAt": {"type": "string", "format": "date-time", "title": "Uploadedat"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "Base64-encoded file content"}, "visitId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Visitid"}}, "type": "object", "required": ["attachmentId", "filename", "uploadedAt"], "title": "AttachmentWithContent"}, "Body_add_visit_attachment_visits__visit_id__attachments_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_add_visit_attachment_visits__visit_id__attachments_post"}, "Caregiver": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["firstName", "lastName", "caregiverId", "createdAt"], "title": "Caregiver"}, "CaregiverAvailabilityResponse": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}}, "type": "object", "required": ["caregiverId", "firstName", "lastName"], "title": "CaregiverAvailabilityResponse"}, "CaregiverServiceRequestResponse": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "type": "object", "required": ["caregiverId", "firstName", "lastName"], "title": "CaregiverServiceRequestResponse"}, "CaregiverWithServices": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["firstName", "lastName", "caregiverId", "createdAt"], "title": "CaregiverWithServices"}, "Client": {"properties": {"firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"type": "string", "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"type": "string", "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "medicalHistory": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Medicalhistory"}, "medications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Medications"}, "allergies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Allergies"}, "mobility": {"anyOf": [{"$ref": "#/components/schemas/MobilityEnum"}, {"type": "null"}]}, "preferredLanguage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferredlanguage"}, "favoriteCaregivers": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Favoritecaregivers"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "active": {"type": "boolean", "title": "Active", "default": true}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "createdAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["firstName", "lastName", "phone", "city", "clientId"], "title": "Client", "description": "Model returned from DB (e.g. INSERT … RETURNING * or SELECT)"}, "ClientMinified": {"properties": {"firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}}, "type": "object", "title": "ClientMinified"}, "ClientServiceRequestResponse": {"properties": {"clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "firstName": {"type": "string", "minLength": 1, "title": "Firstname"}, "lastName": {"type": "string", "minLength": 1, "title": "Lastname"}}, "type": "object", "required": ["clientId", "firstName", "lastName"], "title": "ClientServiceRequestResponse", "description": "Client with service request details"}, "GenderEnum": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER"], "title": "GenderEnum"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MobilityEnum": {"type": "string", "enum": ["INDEPENDENT", "INDEPENDENT WITH ASSISTIVE DEVICE", "ASSISTED", "WHEELCHAIR", "BEDBOUND"], "title": "MobilityEnum"}, "RequestStatusEnum": {"type": "string", "enum": ["SUBMITTED", "UNFULFILLED", "CANCELLED"], "title": "RequestStatusEnum"}, "Service": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}, "serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes", "default": 0}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s", "description": "Estimated cost of the service in Euros"}}, "type": "object", "required": ["serviceId", "name", "serviceTypeId"], "title": "Service", "description": "Model representing a Homecare Service offering."}, "ServiceRequestResponse": {"properties": {"fromDate": {"type": "string", "format": "date-time", "title": "Fromdate"}, "toDate": {"type": "string", "format": "date-time", "title": "Todate"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "rrule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "status": {"$ref": "#/components/schemas/RequestStatusEnum"}, "serviceRequestId": {"type": "integer", "title": "Servicerequestid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "client": {"$ref": "#/components/schemas/ClientServiceRequestResponse"}, "preferredCaregiver": {"anyOf": [{"$ref": "#/components/schemas/CaregiverServiceRequestResponse"}, {"type": "null"}]}, "services": {"items": {"$ref": "#/components/schemas/ServiceServiceRequestResponse"}, "type": "array", "title": "Services"}}, "type": "object", "required": ["fromDate", "toDate", "status", "serviceRequestId", "createdAt", "client", "services"], "title": "ServiceRequestResponse", "description": "Model for service requests response body"}, "ServiceServiceRequestResponse": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}}, "type": "object", "required": ["serviceId", "name"], "title": "ServiceServiceRequestResponse"}, "SuggestedVisitGroup": {"properties": {"date": {"type": "string", "format": "date", "title": "Date", "description": "Date of the visit"}, "startTime": {"type": "string", "format": "time", "title": "Starttime", "description": "Visit start time"}, "endTime": {"type": "string", "format": "time", "title": "Endtime", "description": "Visit end time"}, "caregivers": {"items": {"$ref": "#/components/schemas/Caregiver"}, "type": "array", "title": "Caregivers", "description": "Caregivers available for this slot"}}, "type": "object", "required": ["date", "startTime", "endTime", "caregivers"], "title": "SuggestedVisitGroup"}, "SuggestedVisitsRequest": {"properties": {"dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Dates", "description": "List of dates for which to schedule visits"}, "timeSlot": {"$ref": "#/components/schemas/TimeSlotRequest", "description": "Time range for each visit (e.g. 09:00–10:00)"}, "serviceIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Serviceids", "description": "Only suggest caregivers who offer these services"}, "caregiverIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Caregiverids", "description": "Only include caregivers from this list"}}, "type": "object", "required": ["dates", "timeSlot"], "title": "SuggestedVisitsRequest"}, "TimeSlotRequest": {"properties": {"startTime": {"type": "string", "format": "time", "title": "Starttime", "example": "09:00"}, "endTime": {"type": "string", "format": "time", "title": "Endtime", "example": "10:00"}}, "type": "object", "required": ["startTime", "endTime"], "title": "TimeSlotRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VisitAttachmentsPaginatedResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/AttachmentBase"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "VisitAttachmentsPaginatedResponse"}, "VisitCreate": {"properties": {"clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "caregiverIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Caregiverids"}, "serviceRequestId": {"type": "integer", "title": "Servicerequestid"}, "startTime": {"type": "string", "format": "date-time", "title": "Starttime"}, "endTime": {"type": "string", "format": "date-time", "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "street": {"type": "string", "title": "Street"}, "city": {"type": "string", "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "heartRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Heartrate"}, "oxygenSaturation": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Oxygensaturation"}, "respiratoryRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Respiratoryrate"}, "co2Level": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Co2Level"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}, "bloodPressureSystolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressuresystolic"}, "bloodPressureDiastolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressurediastolic"}, "bloodGlucose": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodglucose"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "bmi": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bmi"}, "painScale": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Painscale"}}, "type": "object", "required": ["clientId", "serviceRequestId", "startTime", "endTime", "street", "city"], "title": "VisitCreate"}, "VisitMinified": {"properties": {"id": {"type": "integer", "title": "Id"}, "startTime": {"type": "string", "format": "date-time", "title": "Starttime"}, "endTime": {"type": "string", "format": "date-time", "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "client": {"anyOf": [{"$ref": "#/components/schemas/ClientMinified"}, {"type": "null"}]}, "caregivers": {"anyOf": [{"items": {"$ref": "#/components/schemas/CaregiverAvailabilityResponse"}, "type": "array"}, {"type": "null"}], "title": "Caregivers", "default": []}}, "type": "object", "required": ["id", "startTime", "endTime"], "title": "VisitMinified"}, "VisitPopulated": {"properties": {"id": {"type": "integer", "title": "Id"}, "client": {"$ref": "#/components/schemas/Client"}, "caregivers": {"anyOf": [{"items": {"$ref": "#/components/schemas/CaregiverWithServices"}, "type": "array"}, {"type": "null"}], "title": "Caregivers"}, "serviceRequestId": {"type": "integer", "title": "Servicerequestid"}, "services": {"items": {"$ref": "#/components/schemas/VisitServicePopulated"}, "type": "array", "title": "Services"}, "startTime": {"type": "string", "format": "date-time", "title": "Starttime"}, "endTime": {"type": "string", "format": "date-time", "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "street": {"type": "string", "title": "Street"}, "city": {"type": "string", "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "status": {"anyOf": [{"$ref": "#/components/schemas/VisitStatusEnum"}, {"type": "null"}]}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "heartRate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Heartrate"}, "oxygenSaturation": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Oxygensaturation"}, "respiratoryRate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Respiratoryrate"}, "co2Level": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Co2Level"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}, "bloodPressureSystolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressuresystolic"}, "bloodPressureDiastolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressurediastolic"}, "bloodGlucose": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodglucose"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "bmi": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bmi"}, "painScale": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Painscale"}, "attachments": {"items": {"$ref": "#/components/schemas/AttachmentBase"}, "type": "array", "title": "Attachments", "default": []}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "serviceRequest": {"$ref": "#/components/schemas/ServiceRequestResponse"}}, "type": "object", "required": ["id", "client", "serviceRequestId", "services", "startTime", "endTime", "street", "city", "createdAt", "serviceRequest"], "title": "VisitPopulated"}, "VisitQueryByCaregiversAndDates": {"properties": {"caregiverIds": {"items": {"type": "integer"}, "type": "array", "title": "Caregiverids", "description": "Caregiver IDs to filter"}, "dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Dates", "description": "List of dates to include"}}, "type": "object", "required": ["caregiverIds", "dates"], "title": "VisitQueryByCaregiversAndDates"}, "VisitService": {"properties": {"id": {"type": "integer", "title": "Id"}, "service": {"type": "integer", "title": "Service"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["id", "service"], "title": "VisitService"}, "VisitServicePopulated": {"properties": {"id": {"type": "integer", "title": "Id"}, "service": {"$ref": "#/components/schemas/Service"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["id", "service"], "title": "VisitServicePopulated"}, "VisitServiceUpdate": {"properties": {"serviceId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Serviceid"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}}, "type": "object", "title": "VisitServiceUpdate"}, "VisitServiceUpdateOrCreate": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}, "service": {"type": "integer", "title": "Service"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}}, "type": "object", "required": ["service"], "title": "VisitServiceUpdateOrCreate"}, "VisitServicesPaginatedResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/VisitServicePopulated"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "VisitServicesPaginatedResponse"}, "VisitStatusEnum": {"type": "string", "enum": ["SCHEDULED", "IN PROGRESS", "COMPLETED", "CANCELLED"], "title": "VisitStatusEnum"}, "VisitUpdate": {"properties": {"clientId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "caregiverIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Caregiverids"}, "serviceRequestId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Servicerequestid"}, "startTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Starttime"}, "endTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "heartRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Heartrate"}, "oxygenSaturation": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Oxygensaturation"}, "respiratoryRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Respiratoryrate"}, "co2Level": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Co2Level"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}, "bloodPressureSystolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressuresystolic"}, "bloodPressureDiastolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressurediastolic"}, "bloodGlucose": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodglucose"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "bmi": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bmi"}, "painScale": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Painscale"}}, "type": "object", "title": "VisitUpdate"}, "VisitsGetAllMinifiedPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/VisitMinified"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "VisitsGetAllMinifiedPaginationResponse"}, "VisitsGetAllPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/VisitPopulated"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "VisitsGetAllPaginationResponse"}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/auth", "tokenUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/token", "scopes": {}}}}}}, "servers": [{"url": "/api/v1/visits-api/"}], "security": [{"oauth2": []}]}