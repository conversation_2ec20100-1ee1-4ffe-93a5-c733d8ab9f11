{"openapi": "3.1.0", "info": {"title": "Visits-Service", "description": "\n    Microservice for managing home care visits.\n\n    ## Features\n    * Create, read, update, add attachments, and delete visits records\n    * Create visit services\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/visits": {"post": {"tags": ["Visits"], "summary": "Create Visit", "operationId": "create_visit_visits_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Visits"], "summary": "List all visits", "description": "Fetch a paginated list of visits with optional filters.  \n    You can filter by:\n    - Client ID\n    - Caregiver ID\n    - Status (e.g., scheduled, completed, cancelled)\n    - Start time range\n    - Whether visits have attachments\n    - Service IDs\n\n    Results are sorted by `start_time` ascending by default.", "operationId": "get_all_visits_endpoint_visits_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of records to skip (for pagination)", "default": 0, "title": "Offset"}, "description": "Number of records to skip (for pagination)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max number of records to return (for pagination)", "default": 100, "title": "Limit"}, "description": "Max number of records to return (for pagination)"}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by Client ID", "title": "Client Id"}, "description": "Filter by Client ID"}, {"name": "caregiver_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by Caregiver ID", "title": "Caregiver Id"}, "description": "Filter by Caregiver ID"}, {"name": "visit_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by visit status (e.g. scheduled, completed, cancelled)", "title": "Visit Status"}, "description": "Filter by visit status (e.g. scheduled, completed, cancelled)"}, {"name": "start_time_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Return visits starting **after or at** this datetime (ISO 8601 format)", "title": "Start Time From"}, "description": "Return visits starting **after or at** this datetime (ISO 8601 format)"}, {"name": "start_time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Return visits starting **before or at** this datetime (ISO 8601 format)", "title": "Start Time To"}, "description": "Return visits starting **before or at** this datetime (ISO 8601 format)"}, {"name": "service_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter visits containing these service IDs (one or more). Uses OR logic.", "title": "Service Ids"}, "description": "Filter visits containing these service IDs (one or more). Uses OR logic."}, {"name": "has_attachments", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by presence of attachments (True = with attachments, False = without)", "title": "Has Attachments"}, "description": "Filter by presence of attachments (True = with attachments, False = without)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Visit"}, "title": "Response Get All Visits Endpoint Visits Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}": {"get": {"tags": ["Visits"], "summary": "Get Visit", "operationId": "get_visit_visits__visit_id__get", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Visits"], "summary": "Update Visit", "operationId": "update_visit_visits__visit_id__put", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Visits"], "summary": "Delete Visit", "operationId": "delete_visit_visits__visit_id__delete", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/services": {"post": {"tags": ["Visit Services"], "summary": "Add Visit Service", "operationId": "add_visit_service_visits__visit_id__services_post", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddVisitService"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Visit Services"], "summary": "Upsert Visit Services", "operationId": "upsert_visit_services_visits__visit_id__services_put", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitServiceUpdateOrCreate"}, "title": "Services"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/attachments": {"post": {"tags": ["Visit Attachments"], "summary": "Add Visit Attachment", "operationId": "add_visit_attachment_visits__visit_id__attachments_post", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_add_visit_attachment_visits__visit_id__attachments_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/attachments/{attachment_id}/download": {"get": {"tags": ["Visit Attachments"], "summary": "Download Attachment", "operationId": "download_attachment_attachments__attachment_id__download_get", "parameters": [{"name": "attachment_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Attachment Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachmentWithContent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/by-caregivers-and-dates": {"post": {"tags": ["Visits"], "summary": "Get visits for caregivers on specific dates", "description": "Returns visit times for the given caregiver IDs and dates.", "operationId": "post_visits_by_caregivers_and_dates_visits_by_caregivers_and_dates_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitQueryByCaregiversAndDates"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/suggested-from-availability": {"post": {"tags": ["Visits"], "summary": "Suggest visits based on caregiver availability", "description": "Returns suggested visit slots (not persisted) for a client based on available caregivers matching a time slot, service IDs, and optional caregiver filters.", "operationId": "post_suggested_visits_from_availability_visits_suggested_from_availability_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestedVisitsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SuggestedVisitSlot"}, "type": "array", "title": "Response Post Suggested Visits From Availability Visits Suggested From Availability Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/search": {"post": {"tags": ["Visits"], "summary": "Search visits", "description": "Search visits by client name, caregiver name, or service name.", "operationId": "search_visits_visits_search_post", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Offset for pagination", "default": 0, "title": "Offset"}, "description": "Offset for pagination"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "description": "Limit for pagination", "default": 100, "title": "Limit"}, "description": "Limit for pagination"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchVisitsRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SearchVisitResponse"}, "title": "Response Search Visits Visits Search Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/attachments/{attachment_id}": {"delete": {"tags": ["Visit Attachments"], "summary": "Delete an attachment by ID", "operationId": "delete_attachment_attachments__attachment_id__delete", "parameters": [{"name": "attachment_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Attachment Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visit-services/{visit_service_id}": {"put": {"tags": ["Visit Services"], "summary": "Update Visit Service", "operationId": "update_visit_service_visit_services__visit_service_id__put", "parameters": [{"name": "visit_service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Service Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitServiceUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/visits/{visit_id}/services/{visit_service_id}": {"delete": {"tags": ["Visit Services"], "summary": "Delete Visit Service", "operationId": "delete_visit_service_visits__visit_id__services__visit_service_id__delete", "parameters": [{"name": "visit_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Id"}}, {"name": "visit_service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Visit Service Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AddVisitService": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}}, "additionalProperties": false, "type": "object", "required": ["serviceId"], "title": "AddVisitService"}, "AttachmentBase": {"properties": {"id": {"type": "integer", "title": "Id"}, "filename": {"type": "string", "title": "Filename"}, "contentType": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contenttype"}, "uploadedAt": {"type": "string", "format": "date-time", "title": "Uploadedat"}}, "additionalProperties": false, "type": "object", "required": ["id", "filename", "uploadedAt"], "title": "AttachmentBase"}, "AttachmentWithContent": {"properties": {"id": {"type": "integer", "title": "Id"}, "filename": {"type": "string", "title": "Filename"}, "contentType": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contenttype"}, "uploadedAt": {"type": "string", "format": "date-time", "title": "Uploadedat"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "Base64-encoded file content"}, "visitId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Visitid"}}, "additionalProperties": false, "type": "object", "required": ["id", "filename", "uploadedAt"], "title": "AttachmentWithContent"}, "Body_add_visit_attachment_visits__visit_id__attachments_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_add_visit_attachment_visits__visit_id__attachments_post"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "SearchVisitResponse": {"properties": {"visitId": {"type": "integer", "title": "Visitid"}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "caregiverId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Caregiverid"}, "startTime": {"type": "string", "format": "date-time", "title": "Starttime"}, "endTime": {"type": "string", "format": "date-time", "title": "Endtime"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "serviceIds": {"items": {"$ref": "#/components/schemas/VisitService"}, "type": "array", "title": "Serviceids", "default": []}}, "additionalProperties": false, "type": "object", "required": ["visitId", "clientId", "startTime", "endTime", "createdAt"], "title": "SearchVisitResponse"}, "SearchVisitsRequest": {"properties": {"fromDate": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Fromdate", "description": "Start date for search range"}, "toDate": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Todate", "description": "End date for search range"}, "assigned": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Assigned", "description": "Filter by assigned visits"}, "clientId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Filter by specific client ID"}, "caregiverId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Caregiverid", "description": "Filter by specific caregiver ID"}, "serviceId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Serviceid", "description": "Filter by specific service ID"}}, "additionalProperties": false, "type": "object", "title": "SearchVisitsRequest"}, "SuggestedVisitSlot": {"properties": {"date": {"type": "string", "format": "date", "title": "Date", "description": "Date of the visit"}, "startTime": {"type": "string", "format": "time", "title": "Starttime", "description": "Visit start time"}, "endTime": {"type": "string", "format": "time", "title": "Endtime", "description": "Visit end time"}, "caregiverId": {"type": "integer", "title": "Caregiverid", "description": "ID of the caregiver"}, "serviceIds": {"items": {"type": "integer"}, "type": "array", "title": "Serviceids", "description": "Requested service IDs"}, "isAvailable": {"type": "boolean", "title": "Isavailable", "description": "Whether the caregiver is available for this slot"}}, "additionalProperties": false, "type": "object", "required": ["date", "startTime", "endTime", "caregiverId", "serviceIds", "isAvailable"], "title": "SuggestedVisitSlot"}, "SuggestedVisitsRequest": {"properties": {"dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Dates", "description": "List of dates for which to schedule visits"}, "timeSlot": {"$ref": "#/components/schemas/TimeSlotRequest", "description": "Time range for each visit (e.g. 09:00–10:00)"}, "serviceIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Serviceids", "description": "Only suggest caregivers who offer these services"}, "caregiverIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Caregiverids", "description": "Only include caregivers from this list"}}, "additionalProperties": false, "type": "object", "required": ["dates", "timeSlot"], "title": "SuggestedVisitsRequest"}, "TimeSlotRequest": {"properties": {"startTime": {"type": "string", "format": "time", "title": "Starttime", "example": "09:00"}, "endTime": {"type": "string", "format": "time", "title": "Endtime", "example": "10:00"}}, "additionalProperties": false, "type": "object", "required": ["startTime", "endTime"], "title": "TimeSlotRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "Visit": {"properties": {"id": {"type": "integer", "title": "Id"}, "client": {"type": "integer", "title": "Client"}, "caregiver": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Caregiver"}, "startTime": {"type": "string", "format": "date-time", "title": "Starttime"}, "endTime": {"type": "string", "format": "date-time", "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "heartRate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Heartrate"}, "oxygenSaturation": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Oxygensaturation"}, "respiratoryRate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Respiratoryrate"}, "co2Level": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Co2Level"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}, "bloodPressureSystolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressuresystolic"}, "bloodPressureDiastolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressurediastolic"}, "bloodGlucose": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodglucose"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "bmi": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bmi"}, "painScale": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Painscale"}, "services": {"items": {"$ref": "#/components/schemas/VisitService"}, "type": "array", "title": "Services"}, "attachments": {"items": {"$ref": "#/components/schemas/AttachmentBase"}, "type": "array", "title": "Attachments", "default": []}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["id", "client", "startTime", "endTime", "services", "createdAt"], "title": "Visit"}, "VisitCreate": {"properties": {"clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "caregiverId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Caregiverid"}, "startTime": {"type": "string", "format": "date-time", "title": "Starttime"}, "endTime": {"type": "string", "format": "date-time", "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "heartRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Heartrate"}, "oxygenSaturation": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Oxygensaturation"}, "respiratoryRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Respiratoryrate"}, "co2Level": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Co2Level"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}, "bloodPressureSystolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressuresystolic"}, "bloodPressureDiastolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressurediastolic"}, "bloodGlucose": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodglucose"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "bmi": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bmi"}, "painScale": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Painscale"}}, "additionalProperties": false, "type": "object", "required": ["clientId", "startTime", "endTime"], "title": "VisitCreate"}, "VisitQueryByCaregiversAndDates": {"properties": {"caregiverIds": {"items": {"type": "integer"}, "type": "array", "title": "Caregiverids", "description": "Caregiver IDs to filter"}, "dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Dates", "description": "List of dates to include"}}, "additionalProperties": false, "type": "object", "required": ["caregiverIds", "dates"], "title": "VisitQueryByCaregiversAndDates"}, "VisitService": {"properties": {"id": {"type": "integer", "title": "Id"}, "service": {"type": "integer", "title": "Service"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "additionalProperties": false, "type": "object", "required": ["id", "service"], "title": "VisitService"}, "VisitServiceUpdate": {"properties": {"serviceId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Serviceid"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}}, "additionalProperties": false, "type": "object", "title": "VisitServiceUpdate"}, "VisitServiceUpdateOrCreate": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}, "service": {"type": "integer", "title": "Service"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "isCompleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Iscompleted"}}, "additionalProperties": false, "type": "object", "required": ["service"], "title": "VisitServiceUpdateOrCreate"}, "VisitUpdate": {"properties": {"clientId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "caregiverId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Caregiverid"}, "startTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Starttime"}, "endTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Endtime"}, "completedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completedat"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Zip"}, "geoLat": {"anyOf": [{"type": "number", "maximum": 90.0, "minimum": -90.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "geoLng": {"anyOf": [{"type": "number", "maximum": 180.0, "minimum": -180.0}, {"type": "null"}], "title": "Geolng"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "cancelDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Canceldate"}, "cancelReason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "heartRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Heartrate"}, "oxygenSaturation": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Oxygensaturation"}, "respiratoryRate": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Respiratoryrate"}, "co2Level": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Co2Level"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}, "bloodPressureSystolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressuresystolic"}, "bloodPressureDiastolic": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodpressurediastolic"}, "bloodGlucose": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bloodglucose"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "bmi": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Bmi"}, "painScale": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Painscale"}}, "additionalProperties": false, "type": "object", "title": "VisitUpdate"}}}}