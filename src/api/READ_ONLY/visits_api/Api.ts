/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** VisitStatusEnum */
export enum VisitStatusEnum {
  SCHEDULED = "SCHEDULED",
  INPROGRESS = "IN PROGRESS",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
}

/** RequestStatusEnum */
export enum RequestStatusEnum {
  SUBMITTED = "SUBMITTED",
  UNFULFILLED = "UNFULFILLED",
  CANCELLED = "CANCELLED",
}

/** MobilityEnum */
export enum MobilityEnum {
  INDEPENDENT = "INDEPENDENT",
  INDEPENDENTWITHASSISTIVEDEVICE = "INDEPENDENT WITH ASSISTIVE DEVICE",
  ASSISTED = "ASSISTED",
  WHEELCHAIR = "WHEELCHAIR",
  BEDBOUND = "BEDBOUND",
}

/** GenderEnum */
export enum GenderEnum {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

/** AddVisitService */
export interface AddVisitService {
  /** Serviceid */
  serviceId: number;
  /** Notes */
  notes?: string | null;
  /** Iscompleted */
  isCompleted?: boolean | null;
}

/** AttachmentBase */
export interface AttachmentBase {
  /** Attachmentid */
  attachmentId: number;
  /** Filename */
  filename: string;
  /** Contenttype */
  contentType?: string | null;
  /**
   * Uploadedat
   * @format date-time
   */
  uploadedAt: string;
}

/** AttachmentCreateResponse */
export interface AttachmentCreateResponse {
  /** Attachmentid */
  attachmentId: number;
}

/** AttachmentWithContent */
export interface AttachmentWithContent {
  /** Attachmentid */
  attachmentId: number;
  /** Filename */
  filename: string;
  /** Contenttype */
  contentType?: string | null;
  /**
   * Uploadedat
   * @format date-time
   */
  uploadedAt: string;
  /**
   * Content
   * Base64-encoded file content
   */
  content?: string | null;
  /** Visitid */
  visitId?: number | null;
}

/** Body_add_visit_attachment_visits__visit_id__attachments_post */
export interface BodyAddVisitAttachmentVisitsVisitIdAttachmentsPost {
  /**
   * File
   * @format binary
   */
  file: File;
}

/** Caregiver */
export interface Caregiver {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Userid */
  userId?: number | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: number[] | Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** CaregiverAvailabilityResponse */
export interface CaregiverAvailabilityResponse {
  /** Caregiverid */
  caregiverId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
}

/** CaregiverServiceRequestResponse */
export interface CaregiverServiceRequestResponse {
  /** Caregiverid */
  caregiverId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Phone */
  phone?: string | null;
}

/** CaregiverWithServices */
export interface CaregiverWithServices {
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Userid */
  userId?: number | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone?: string | null;
  /** Email */
  email?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Coverageareas */
  coverageAreas?: string[] | null;
  /** Travelradiuskm */
  travelRadiusKm?: number | null;
  /** Services */
  services?: Service[] | null;
  /** Certifications */
  certifications?: string[] | null;
  /** Skills */
  skills?: string[] | null;
  /** Specialties */
  specialties?: string[] | null;
  /** Languagesspoken */
  languagesSpoken?: string[] | null;
  /** Rating */
  rating?: number | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Caregiverid */
  caregiverId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/**
 * Client
 * Model returned from DB (e.g. INSERT … RETURNING * or SELECT)
 */
export interface Client {
  /**
   * Firstname
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * @minLength 1
   */
  lastName: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone: string;
  /** Email */
  email?: string | null;
  /** Userid */
  userId?: number | null;
  /** Street */
  street?: string | null;
  /** City */
  city: string;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Medicalhistory */
  medicalHistory?: string | null;
  /** Medications */
  medications?: string[] | null;
  /** Allergies */
  allergies?: string[] | null;
  mobility?: MobilityEnum | null;
  /** Preferredlanguage */
  preferredLanguage?: string | null;
  /** Favoritecaregivers */
  favoriteCaregivers?: string[] | null;
  /** Notes */
  notes?: string | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Clientid */
  clientId: number;
  /** Createdat */
  createdAt?: string | null;
  /** Updatedat */
  updatedAt?: string | null;
}

/** ClientMinified */
export interface ClientMinified {
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** City */
  city?: string | null;
  /** Street */
  street?: string | null;
  /** Postalcode */
  postalCode?: string | null;
}

/**
 * ClientServiceRequestResponse
 * Client with service request details
 */
export interface ClientServiceRequestResponse {
  /** Clientid */
  clientId: number;
  /**
   * Firstname
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * @minLength 1
   */
  lastName: string;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/**
 * Service
 * Model representing a Homecare Service offering.
 */
export interface Service {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   * @default 0
   */
  estimatedTimeMinutes?: number | null;
  /**
   * Costineuros
   * Estimated cost of the service in Euros
   */
  costInEuros?: number | null;
}

/**
 * ServiceRequestResponse
 * Model for service requests response body
 */
export interface ServiceRequestResponse {
  /**
   * Fromdate
   * @format date-time
   */
  fromDate: string;
  /**
   * Todate
   * @format date-time
   */
  toDate: string;
  /** Notes */
  notes?: string | null;
  /** Rrule */
  rrule?: string | null;
  status: RequestStatusEnum;
  /** Servicerequestid */
  serviceRequestId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
  /** Client with service request details */
  client: ClientServiceRequestResponse;
  preferredCaregiver?: CaregiverServiceRequestResponse | null;
  /** Services */
  services: ServiceServiceRequestResponse[];
}

/** ServiceServiceRequestResponse */
export interface ServiceServiceRequestResponse {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
}

/** SuggestedVisitGroup */
export interface SuggestedVisitGroup {
  /**
   * Date
   * Date of the visit
   * @format date
   */
  date: string;
  /**
   * Starttime
   * Visit start time
   * @format time
   */
  startTime: string;
  /**
   * Endtime
   * Visit end time
   * @format time
   */
  endTime: string;
  /**
   * Caregivers
   * Caregivers available for this slot
   */
  caregivers: Caregiver[];
}

/** SuggestedVisitsRequest */
export interface SuggestedVisitsRequest {
  /**
   * Dates
   * List of dates for which to schedule visits
   */
  dates: string[];
  /** Time range for each visit (e.g. 09:00–10:00) */
  timeSlot: TimeSlotRequest;
  /**
   * Serviceids
   * Only suggest caregivers who offer these services
   */
  serviceIds?: number[] | null;
  /**
   * Caregiverids
   * Only include caregivers from this list
   */
  caregiverIds?: number[] | null;
}

/** TimeSlotRequest */
export interface TimeSlotRequest {
  /**
   * Starttime
   * @format time
   * @example "09:00"
   */
  startTime: string;
  /**
   * Endtime
   * @format time
   * @example "10:00"
   */
  endTime: string;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

/** VisitAttachmentsPaginatedResponse */
export interface VisitAttachmentsPaginatedResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: AttachmentBase[];
}

/** VisitCreate */
export interface VisitCreate {
  /** Clientid */
  clientId: number;
  /** Caregiverids */
  caregiverIds?: number[] | null;
  /** Servicerequestid */
  serviceRequestId: number;
  /**
   * Starttime
   * @format date-time
   */
  startTime: string;
  /**
   * Endtime
   * @format date-time
   */
  endTime: string;
  /** Completedat */
  completedAt?: string | null;
  /** Notes */
  notes?: string | null;
  /** Street */
  street: string;
  /** City */
  city: string;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Status */
  status?: string | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /** Heartrate */
  heartRate?: number | null;
  /** Oxygensaturation */
  oxygenSaturation?: number | null;
  /** Respiratoryrate */
  respiratoryRate?: number | null;
  /** Co2Level */
  co2Level?: number | null;
  /** Temperature */
  temperature?: number | null;
  /** Bloodpressuresystolic */
  bloodPressureSystolic?: number | null;
  /** Bloodpressurediastolic */
  bloodPressureDiastolic?: number | null;
  /** Bloodglucose */
  bloodGlucose?: number | null;
  /** Weight */
  weight?: number | null;
  /** Height */
  height?: number | null;
  /** Bmi */
  bmi?: number | null;
  /** Painscale */
  painScale?: number | null;
}

/** VisitMinified */
export interface VisitMinified {
  /** Id */
  id: number;
  /**
   * Starttime
   * @format date-time
   */
  startTime: string;
  /**
   * Endtime
   * @format date-time
   */
  endTime: string;
  /** Completedat */
  completedAt?: string | null;
  /** Status */
  status?: string | null;
  client?: ClientMinified | null;
  /**
   * Caregivers
   * @default []
   */
  caregivers?: CaregiverAvailabilityResponse[] | null;
}

/** VisitPopulated */
export interface VisitPopulated {
  /** Id */
  id: number;
  /** Model returned from DB (e.g. INSERT … RETURNING * or SELECT) */
  client: Client;
  /** Caregivers */
  caregivers?: CaregiverWithServices[] | null;
  /** Servicerequestid */
  serviceRequestId: number;
  /** Services */
  services: VisitServicePopulated[];
  /**
   * Starttime
   * @format date-time
   */
  startTime: string;
  /**
   * Endtime
   * @format date-time
   */
  endTime: string;
  /** Completedat */
  completedAt?: string | null;
  /** Notes */
  notes?: string | null;
  /** Street */
  street: string;
  /** City */
  city: string;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  status?: VisitStatusEnum | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /** Heartrate */
  heartRate?: number | null;
  /** Oxygensaturation */
  oxygenSaturation?: number | null;
  /** Respiratoryrate */
  respiratoryRate?: number | null;
  /** Co2Level */
  co2Level?: number | null;
  /** Temperature */
  temperature?: number | null;
  /** Bloodpressuresystolic */
  bloodPressureSystolic?: number | null;
  /** Bloodpressurediastolic */
  bloodPressureDiastolic?: number | null;
  /** Bloodglucose */
  bloodGlucose?: number | null;
  /** Weight */
  weight?: number | null;
  /** Height */
  height?: number | null;
  /** Bmi */
  bmi?: number | null;
  /** Painscale */
  painScale?: number | null;
  /**
   * Attachments
   * @default []
   */
  attachments?: AttachmentBase[];
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
  /** Model for service requests response body */
  serviceRequest: ServiceRequestResponse;
}

/** VisitQueryByCaregiversAndDates */
export interface VisitQueryByCaregiversAndDates {
  /**
   * Caregiverids
   * Caregiver IDs to filter
   */
  caregiverIds: number[];
  /**
   * Dates
   * List of dates to include
   */
  dates: string[];
}

/** VisitService */
export interface VisitService {
  /** Id */
  id: number;
  /** Service */
  service: number;
  /** Iscompleted */
  isCompleted?: boolean | null;
  /** Notes */
  notes?: string | null;
}

/** VisitServicePopulated */
export interface VisitServicePopulated {
  /** Id */
  id: number;
  /** Model representing a Homecare Service offering. */
  service: Service;
  /** Iscompleted */
  isCompleted?: boolean | null;
  /** Notes */
  notes?: string | null;
}

/** VisitServiceUpdate */
export interface VisitServiceUpdate {
  /** Serviceid */
  serviceId?: number | null;
  /** Notes */
  notes?: string | null;
  /** Iscompleted */
  isCompleted?: boolean | null;
}

/** VisitServiceUpdateOrCreate */
export interface VisitServiceUpdateOrCreate {
  /** Id */
  id?: number | null;
  /** Service */
  service: number;
  /** Notes */
  notes?: string | null;
  /** Iscompleted */
  isCompleted?: boolean | null;
}

/** VisitServicesPaginatedResponse */
export interface VisitServicesPaginatedResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: VisitServicePopulated[];
}

/** VisitUpdate */
export interface VisitUpdate {
  /** Clientid */
  clientId?: number | null;
  /** Caregiverids */
  caregiverIds?: number[] | null;
  /** Servicerequestid */
  serviceRequestId?: number | null;
  /** Starttime */
  startTime?: string | null;
  /** Endtime */
  endTime?: string | null;
  /** Completedat */
  completedAt?: string | null;
  /** Notes */
  notes?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Status */
  status?: string | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /** Heartrate */
  heartRate?: number | null;
  /** Oxygensaturation */
  oxygenSaturation?: number | null;
  /** Respiratoryrate */
  respiratoryRate?: number | null;
  /** Co2Level */
  co2Level?: number | null;
  /** Temperature */
  temperature?: number | null;
  /** Bloodpressuresystolic */
  bloodPressureSystolic?: number | null;
  /** Bloodpressurediastolic */
  bloodPressureDiastolic?: number | null;
  /** Bloodglucose */
  bloodGlucose?: number | null;
  /** Weight */
  weight?: number | null;
  /** Height */
  height?: number | null;
  /** Bmi */
  bmi?: number | null;
  /** Painscale */
  painScale?: number | null;
}

/** VisitsGetAllMinifiedPaginationResponse */
export interface VisitsGetAllMinifiedPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: VisitMinified[];
}

/** VisitsGetAllPaginationResponse */
export interface VisitsGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: VisitPopulated[];
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/visits-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title visit
 * @version 1.0.0
 * @baseUrl /api/v1/visits-api/
 *
 *
 * Microservice for managing home care visits.
 *
 * ## Features
 * * Create, read, update, add attachments, and delete visits records
 * * Create visit services
 * * Secure endpoints with authentication and authorization
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 * Admin endpoints require additional admin permissions.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  visits = {
    /**
     * @description Fetch a paginated list of visits with optional filters. You can filter by: - Client ID - Caregiver ID - Status (e.g., scheduled, completed, cancelled) - Start time range - Whether visits have attachments - Service IDs Results are sorted by `start_time` ascending by default.
     *
     * @tags Visits
     * @name ListVisitsVisitsGet
     * @summary Lists all visits
     * @request GET:/visits
     * @secure
     */
    listVisitsVisitsGet: (
      query?: {
        /**
         * Offset
         * Number of records to skip (for pagination)
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Max number of records to return (for pagination)
         * @default 100
         */
        limit?: number;
        /**
         * Client Id
         * Filter by Client ID
         */
        client_id?: number | null;
        /**
         * Caregiver Ids
         * Filter by caregiver IDs
         */
        caregiver_ids?: number[] | null;
        /**
         * Visit Status
         * Filter by visit status (e.g. scheduled, completed, cancelled)
         */
        visit_status?: string | null;
        /**
         * Start Time From
         * Return visits starting **after or at** this datetime (ISO 8601 format)
         */
        start_time_from?: string | null;
        /**
         * Start Time To
         * Return visits starting **before or at** this datetime (ISO 8601 format)
         */
        start_time_to?: string | null;
        /**
         * Service Ids
         * Filter by Service IDs
         */
        service_ids?: number[] | null;
        /**
         * Has Attachments
         * Filter by presence of attachments (True = with attachments, False = without)
         */
        has_attachments?: boolean | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<VisitsGetAllPaginationResponse, HTTPValidationError | void>({
        path: `/visits`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Create one or more visits in a single request
     *
     * @tags Visits
     * @name CreateVisitsVisitsPost
     * @summary Create visits (batch)
     * @request POST:/visits
     * @secure
     */
    createVisitsVisitsPost: (data: VisitCreate[], params: RequestParams = {}) =>
      this.request<VisitPopulated[], void | HTTPValidationError>({
        path: `/visits`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Fetch a paginated list of visits with optional filters. You can filter by: - Client ID - Caregiver ID - Status (e.g., scheduled, completed, cancelled) - Start time range - Whether visits have attachments - Service IDs Results are sorted by `start_time` ascending by default.
     *
     * @tags Visits
     * @name ListVisitsMinifiedVisitsMinifiedGet
     * @summary Lists all visits with minified schema
     * @request GET:/visits/minified
     * @secure
     */
    listVisitsMinifiedVisitsMinifiedGet: (
      query?: {
        /**
         * Offset
         * Number of records to skip (for pagination)
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Max number of records to return (for pagination)
         * @default 100
         */
        limit?: number;
        /**
         * Client Id
         * Filter by Client ID
         */
        client_id?: number | null;
        /**
         * Caregiver Ids
         * Filter by caregiver IDs
         */
        caregiver_ids?: number[] | null;
        /**
         * Visit Status
         * Filter by visit status (e.g. scheduled, completed, cancelled)
         */
        visit_status?: string | null;
        /**
         * Start Time From
         * Return visits starting **after or at** this datetime (ISO 8601 format)
         */
        start_time_from?: string | null;
        /**
         * Start Time To
         * Return visits starting **before or at** this datetime (ISO 8601 format)
         */
        start_time_to?: string | null;
        /**
         * Service Ids
         * Filter by Service IDs
         */
        service_ids?: number[] | null;
        /**
         * Has Attachments
         * Filter by presence of attachments (True = with attachments, False = without)
         */
        has_attachments?: boolean | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        VisitsGetAllMinifiedPaginationResponse,
        HTTPValidationError | void
      >({
        path: `/visits/minified`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Search visits in all properties by search query
     *
     * @tags Visits
     * @name SearchVisitsVisitsSearchGet
     * @summary Search visits
     * @request GET:/visits/search
     * @secure
     */
    searchVisitsVisitsSearchGet: (
      query?: {
        /**
         * Query
         * Free text query
         */
        query?: string | null;
        /**
         * From Date
         * Filter visits starting from this datetime (ISO 8601 format)
         */
        from_date?: string | null;
        /**
         * To Date
         * Filter visits up to this datetime (ISO 8601 format)
         */
        to_date?: string | null;
        /**
         * Offset
         * Offset for pagination
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Limit for pagination
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<VisitsGetAllPaginationResponse, HTTPValidationError | void>({
        path: `/visits/search`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a specific visit by its ID
     *
     * @tags Visits
     * @name GetVisitVisitsVisitIdGet
     * @summary Get visit by ID
     * @request GET:/visits/{visit_id}
     * @secure
     */
    getVisitVisitsVisitIdGet: (visitId: number, params: RequestParams = {}) =>
      this.request<VisitPopulated, void | HTTPValidationError>({
        path: `/visits/${visitId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing visit
     *
     * @tags Visits
     * @name UpdateVisitVisitsVisitIdPut
     * @summary Update an existing visit
     * @request PUT:/visits/{visit_id}
     * @secure
     */
    updateVisitVisitsVisitIdPut: (
      visitId: number,
      data: VisitUpdate,
      params: RequestParams = {},
    ) =>
      this.request<VisitPopulated, void | HTTPValidationError>({
        path: `/visits/${visitId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing visit
     *
     * @tags Visits
     * @name DeleteVisitVisitsVisitIdDelete
     * @summary Update an existing visit
     * @request DELETE:/visits/{visit_id}
     * @secure
     */
    deleteVisitVisitsVisitIdDelete: (
      visitId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/visits/${visitId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description Returns visit times for the given caregiver IDs and dates.
     *
     * @tags Visits
     * @name PostVisitsByCaregiversAndDatesVisitsByCaregiversAndDatesPost
     * @summary Get visits for caregivers on specific dates
     * @request POST:/visits/by-caregivers-and-dates
     * @secure
     */
    postVisitsByCaregiversAndDatesVisitsByCaregiversAndDatesPost: (
      data: VisitQueryByCaregiversAndDates,
      params: RequestParams = {},
    ) =>
      this.request<VisitPopulated[], HTTPValidationError | void>({
        path: `/visits/by-caregivers-and-dates`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns suggested visit slots (not persisted) for a client based on available caregivers matching a time slot, service IDs, and optional caregiver filters.
     *
     * @tags Visits
     * @name PostSuggestedVisitsFromAvailabilityVisitsSuggestedFromAvailabilityPost
     * @summary Suggest visits based on caregiver availability
     * @request POST:/visits/suggested-from-availability
     * @secure
     */
    postSuggestedVisitsFromAvailabilityVisitsSuggestedFromAvailabilityPost: (
      data: SuggestedVisitsRequest,
      params: RequestParams = {},
    ) =>
      this.request<SuggestedVisitGroup[], HTTPValidationError | void>({
        path: `/visits/suggested-from-availability`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Lists all attachments for a specific visit.
     *
     * @tags Visit Attachments
     * @name ListAttachmentsVisitsVisitIdAttachmentsGet
     * @summary List all attachments for a visit
     * @request GET:/visits/{visit_id}/attachments
     * @secure
     */
    listAttachmentsVisitsVisitIdAttachmentsGet: (
      visitId: number,
      query?: {
        /**
         * Offset
         * The number of records to skip
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * The max number of records to return
         * @min 1
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        VisitAttachmentsPaginatedResponse,
        void | HTTPValidationError
      >({
        path: `/visits/${visitId}/attachments`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Adds an attachment to a specific visit.
     *
     * @tags Visit Attachments
     * @name AddVisitAttachmentVisitsVisitIdAttachmentsPost
     * @summary Add an attachment to a visit
     * @request POST:/visits/{visit_id}/attachments
     * @secure
     */
    addVisitAttachmentVisitsVisitIdAttachmentsPost: (
      visitId: number,
      data: BodyAddVisitAttachmentVisitsVisitIdAttachmentsPost,
      params: RequestParams = {},
    ) =>
      this.request<AttachmentCreateResponse, HTTPValidationError | void>({
        path: `/visits/${visitId}/attachments`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: "json",
        ...params,
      }),

    /**
     * @description Downloads the content of a visit attachment.
     *
     * @tags Visit Attachments
     * @name DownloadAttachmentVisitsVisitIdAttachmentsAttachmentIdDownloadGet
     * @summary Download a visit attachment
     * @request GET:/visits/{visit_id}/attachments/{attachment_id}/download
     * @secure
     */
    downloadAttachmentVisitsVisitIdAttachmentsAttachmentIdDownloadGet: (
      visitId: number,
      attachmentId: number,
      params: RequestParams = {},
    ) =>
      this.request<AttachmentWithContent, void | HTTPValidationError>({
        path: `/visits/${visitId}/attachments/${attachmentId}/download`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Deletes a visit attachment by its ID.
     *
     * @tags Visit Attachments
     * @name DeleteAttachmentVisitsVisitIdAttachmentsAttachmentIdDelete
     * @summary Delete an attachment by ID
     * @request DELETE:/visits/{visit_id}/attachments/{attachment_id}
     * @secure
     */
    deleteAttachmentVisitsVisitIdAttachmentsAttachmentIdDelete: (
      visitId: number,
      attachmentId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError | void>({
        path: `/visits/${visitId}/attachments/${attachmentId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description Add a service to a visit
     *
     * @tags Visit Services
     * @name AddVisitServiceVisitsVisitIdServicesPost
     * @summary Add a service to a visit
     * @request POST:/visits/{visit_id}/services
     * @secure
     */
    addVisitServiceVisitsVisitIdServicesPost: (
      visitId: number,
      data: AddVisitService,
      params: RequestParams = {},
    ) =>
      this.request<any, void | HTTPValidationError>({
        path: `/visits/${visitId}/services`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Lists services of a visit
     *
     * @tags Visit Services
     * @name ListVisitServicesVisitsVisitIdServicesGet
     * @summary Lists services of a visit
     * @request GET:/visits/{visit_id}/services
     * @secure
     */
    listVisitServicesVisitsVisitIdServicesGet: (
      visitId: number,
      query?: {
        /**
         * Offset
         * The number of records to skip
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * The number of records to return
         * @min 1
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<VisitServicesPaginatedResponse, void | HTTPValidationError>({
        path: `/visits/${visitId}/services`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Upsert (update or create) multiple visit services for a specific visit
     *
     * @tags Visit Services
     * @name UpsertVisitServicesVisitsVisitIdServicesPut
     * @summary Upsert (update or create) multiple visit services
     * @request PUT:/visits/{visit_id}/services
     * @secure
     */
    upsertVisitServicesVisitsVisitIdServicesPut: (
      visitId: number,
      data: VisitServiceUpdateOrCreate[],
      params: RequestParams = {},
    ) =>
      this.request<VisitService[], void | HTTPValidationError>({
        path: `/visits/${visitId}/services`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a visit service
     *
     * @tags Visit Services
     * @name DeleteVisitServiceVisitsVisitIdServicesVisitServiceIdDelete
     * @summary Delete a visit service
     * @request DELETE:/visits/{visit_id}/services/{visit_service_id}
     * @secure
     */
    deleteVisitServiceVisitsVisitIdServicesVisitServiceIdDelete: (
      visitId: number,
      visitServiceId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/visits/${visitId}/services/${visitServiceId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),
  };
  visitServices = {
    /**
     * @description Update a visit service
     *
     * @tags Visit Services
     * @name UpdateVisitServiceVisitServicesVisitServiceIdPut
     * @summary Update a visit service
     * @request PUT:/visit-services/{visit_service_id}
     * @secure
     */
    updateVisitServiceVisitServicesVisitServiceIdPut: (
      visitServiceId: number,
      data: VisitServiceUpdate,
      params: RequestParams = {},
    ) =>
      this.request<any, void | HTTPValidationError>({
        path: `/visit-services/${visitServiceId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),
  };
}
