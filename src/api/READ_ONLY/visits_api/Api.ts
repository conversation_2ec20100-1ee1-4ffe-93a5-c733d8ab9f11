/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** AddVisitService */
export interface AddVisitService {
  /** Serviceid */
  serviceId: number;
  /** Notes */
  notes?: string | null;
  /** Iscompleted */
  isCompleted?: boolean | null;
}

/** AttachmentBase */
export interface AttachmentBase {
  /** Id */
  id: number;
  /** Filename */
  filename: string;
  /** Contenttype */
  contentType?: string | null;
  /**
   * Uploadedat
   * @format date-time
   */
  uploadedAt: string;
}

/** AttachmentWithContent */
export interface AttachmentWithContent {
  /** Id */
  id: number;
  /** Filename */
  filename: string;
  /** Contenttype */
  contentType?: string | null;
  /**
   * Uploadedat
   * @format date-time
   */
  uploadedAt: string;
  /**
   * Content
   * Base64-encoded file content
   */
  content?: string | null;
  /** Visitid */
  visitId?: number | null;
}

/** Body_add_visit_attachment_visits__visit_id__attachments_post */
export interface BodyAddVisitAttachmentVisitsVisitIdAttachmentsPost {
  /**
   * File
   * @format binary
   */
  file: File;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** SearchVisitResponse */
export interface SearchVisitResponse {
  /** Visitid */
  visitId: number;
  /** Clientid */
  clientId: number;
  /** Caregiverid */
  caregiverId?: number | null;
  /**
   * Starttime
   * @format date-time
   */
  startTime: string;
  /**
   * Endtime
   * @format date-time
   */
  endTime: string;
  /** Status */
  status?: string | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /**
   * Serviceids
   * @default []
   */
  serviceIds?: VisitService[];
}

/** SearchVisitsRequest */
export interface SearchVisitsRequest {
  /**
   * Fromdate
   * Start date for search range
   */
  fromDate?: string | null;
  /**
   * Todate
   * End date for search range
   */
  toDate?: string | null;
  /**
   * Assigned
   * Filter by assigned visits
   */
  assigned?: boolean | null;
  /**
   * Clientid
   * Filter by specific client ID
   */
  clientId?: number | null;
  /**
   * Caregiverid
   * Filter by specific caregiver ID
   */
  caregiverId?: number | null;
  /**
   * Serviceid
   * Filter by specific service ID
   */
  serviceId?: number | null;
}

/** SuggestedVisitSlot */
export interface SuggestedVisitSlot {
  /**
   * Date
   * Date of the visit
   * @format date
   */
  date: string;
  /**
   * Starttime
   * Visit start time
   * @format time
   */
  startTime: string;
  /**
   * Endtime
   * Visit end time
   * @format time
   */
  endTime: string;
  /**
   * Caregiverid
   * ID of the caregiver
   */
  caregiverId: number;
  /**
   * Serviceids
   * Requested service IDs
   */
  serviceIds: number[];
  /**
   * Isavailable
   * Whether the caregiver is available for this slot
   */
  isAvailable: boolean;
}

/** SuggestedVisitsRequest */
export interface SuggestedVisitsRequest {
  /**
   * Dates
   * List of dates for which to schedule visits
   */
  dates: string[];
  /** Time range for each visit (e.g. 09:00–10:00) */
  timeSlot: TimeSlotRequest;
  /**
   * Serviceids
   * Only suggest caregivers who offer these services
   */
  serviceIds?: number[] | null;
  /**
   * Caregiverids
   * Only include caregivers from this list
   */
  caregiverIds?: number[] | null;
}

/** TimeSlotRequest */
export interface TimeSlotRequest {
  /**
   * Starttime
   * @format time
   * @example "09:00"
   */
  startTime: string;
  /**
   * Endtime
   * @format time
   * @example "10:00"
   */
  endTime: string;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

/** Visit */
export interface Visit {
  /** Id */
  id: number;
  /** Client */
  client: number;
  /** Caregiver */
  caregiver?: number | null;
  /**
   * Starttime
   * @format date-time
   */
  startTime: string;
  /**
   * Endtime
   * @format date-time
   */
  endTime: string;
  /** Completedat */
  completedAt?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Status */
  status?: string | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /** Heartrate */
  heartRate?: number | null;
  /** Oxygensaturation */
  oxygenSaturation?: number | null;
  /** Respiratoryrate */
  respiratoryRate?: number | null;
  /** Co2Level */
  co2Level?: number | null;
  /** Temperature */
  temperature?: number | null;
  /** Bloodpressuresystolic */
  bloodPressureSystolic?: number | null;
  /** Bloodpressurediastolic */
  bloodPressureDiastolic?: number | null;
  /** Bloodglucose */
  bloodGlucose?: number | null;
  /** Weight */
  weight?: number | null;
  /** Height */
  height?: number | null;
  /** Bmi */
  bmi?: number | null;
  /** Painscale */
  painScale?: number | null;
  /** Services */
  services: VisitService[];
  /**
   * Attachments
   * @default []
   */
  attachments?: AttachmentBase[];
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** VisitCreate */
export interface VisitCreate {
  /** Clientid */
  clientId: number;
  /** Caregiverid */
  caregiverId?: number | null;
  /**
   * Starttime
   * @format date-time
   */
  startTime: string;
  /**
   * Endtime
   * @format date-time
   */
  endTime: string;
  /** Completedat */
  completedAt?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Status */
  status?: string | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /** Heartrate */
  heartRate?: number | null;
  /** Oxygensaturation */
  oxygenSaturation?: number | null;
  /** Respiratoryrate */
  respiratoryRate?: number | null;
  /** Co2Level */
  co2Level?: number | null;
  /** Temperature */
  temperature?: number | null;
  /** Bloodpressuresystolic */
  bloodPressureSystolic?: number | null;
  /** Bloodpressurediastolic */
  bloodPressureDiastolic?: number | null;
  /** Bloodglucose */
  bloodGlucose?: number | null;
  /** Weight */
  weight?: number | null;
  /** Height */
  height?: number | null;
  /** Bmi */
  bmi?: number | null;
  /** Painscale */
  painScale?: number | null;
}

/** VisitQueryByCaregiversAndDates */
export interface VisitQueryByCaregiversAndDates {
  /**
   * Caregiverids
   * Caregiver IDs to filter
   */
  caregiverIds: number[];
  /**
   * Dates
   * List of dates to include
   */
  dates: string[];
}

/** VisitService */
export interface VisitService {
  /** Id */
  id: number;
  /** Service */
  service: number;
  /** Iscompleted */
  isCompleted?: boolean | null;
  /** Notes */
  notes?: string | null;
}

/** VisitServiceUpdate */
export interface VisitServiceUpdate {
  /** Serviceid */
  serviceId?: number | null;
  /** Notes */
  notes?: string | null;
  /** Iscompleted */
  isCompleted?: boolean | null;
}

/** VisitServiceUpdateOrCreate */
export interface VisitServiceUpdateOrCreate {
  /** Id */
  id?: number | null;
  /** Service */
  service: number;
  /** Notes */
  notes?: string | null;
  /** Iscompleted */
  isCompleted?: boolean | null;
}

/** VisitUpdate */
export interface VisitUpdate {
  /** Clientid */
  clientId?: number | null;
  /** Caregiverid */
  caregiverId?: number | null;
  /** Starttime */
  startTime?: string | null;
  /** Endtime */
  endTime?: string | null;
  /** Completedat */
  completedAt?: string | null;
  /** Street */
  street?: string | null;
  /** City */
  city?: string | null;
  /** Zip */
  zip?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Status */
  status?: string | null;
  /** Canceldate */
  cancelDate?: string | null;
  /** Cancelreason */
  cancelReason?: string | null;
  /** Heartrate */
  heartRate?: number | null;
  /** Oxygensaturation */
  oxygenSaturation?: number | null;
  /** Respiratoryrate */
  respiratoryRate?: number | null;
  /** Co2Level */
  co2Level?: number | null;
  /** Temperature */
  temperature?: number | null;
  /** Bloodpressuresystolic */
  bloodPressureSystolic?: number | null;
  /** Bloodpressurediastolic */
  bloodPressureDiastolic?: number | null;
  /** Bloodglucose */
  bloodGlucose?: number | null;
  /** Weight */
  weight?: number | null;
  /** Height */
  height?: number | null;
  /** Bmi */
  bmi?: number | null;
  /** Painscale */
  painScale?: number | null;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Visits-Service
 * @version 1.0.0
 *
 *
 *     Microservice for managing home care visits.
 *
 *     ## Features
 *     * Create, read, update, add attachments, and delete visits records
 *     * Create visit services
 *     * Secure endpoints with authentication and authorization
 *
 *     ## Authentication
 *     All endpoints require authentication using JWT tokens.
 *     Admin endpoints require additional admin permissions.
 *
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * No description
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health Check
     * @request GET:/health
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  visits = {
    /**
     * No description
     *
     * @tags Visits
     * @name CreateVisitVisitsPost
     * @summary Create Visit
     * @request POST:/visits
     */
    createVisitVisitsPost: (data: VisitCreate, params: RequestParams = {}) =>
      this.request<Visit, HTTPValidationError>({
        path: `/visits`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Fetch a paginated list of visits with optional filters. You can filter by: - Client ID - Caregiver ID - Status (e.g., scheduled, completed, cancelled) - Start time range - Whether visits have attachments - Service IDs Results are sorted by `start_time` ascending by default.
     *
     * @tags Visits
     * @name GetAllVisitsEndpointVisitsGet
     * @summary List all visits
     * @request GET:/visits
     */
    getAllVisitsEndpointVisitsGet: (
      query?: {
        /**
         * Offset
         * Number of records to skip (for pagination)
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Max number of records to return (for pagination)
         * @default 100
         */
        limit?: number;
        /**
         * Client Id
         * Filter by Client ID
         */
        client_id?: number | null;
        /**
         * Caregiver Id
         * Filter by Caregiver ID
         */
        caregiver_id?: number | null;
        /**
         * Visit Status
         * Filter by visit status (e.g. scheduled, completed, cancelled)
         */
        visit_status?: string | null;
        /**
         * Start Time From
         * Return visits starting **after or at** this datetime (ISO 8601 format)
         */
        start_time_from?: string | null;
        /**
         * Start Time To
         * Return visits starting **before or at** this datetime (ISO 8601 format)
         */
        start_time_to?: string | null;
        /**
         * Service Ids
         * Filter visits containing these service IDs (one or more). Uses OR logic.
         */
        service_ids?: number[] | null;
        /**
         * Has Attachments
         * Filter by presence of attachments (True = with attachments, False = without)
         */
        has_attachments?: boolean | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<Visit[], HTTPValidationError>({
        path: `/visits`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visits
     * @name GetVisitVisitsVisitIdGet
     * @summary Get Visit
     * @request GET:/visits/{visit_id}
     */
    getVisitVisitsVisitIdGet: (visitId: number, params: RequestParams = {}) =>
      this.request<Visit, HTTPValidationError>({
        path: `/visits/${visitId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visits
     * @name UpdateVisitVisitsVisitIdPut
     * @summary Update Visit
     * @request PUT:/visits/{visit_id}
     */
    updateVisitVisitsVisitIdPut: (
      visitId: number,
      data: VisitUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Visit, HTTPValidationError>({
        path: `/visits/${visitId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visits
     * @name DeleteVisitVisitsVisitIdDelete
     * @summary Delete Visit
     * @request DELETE:/visits/{visit_id}
     */
    deleteVisitVisitsVisitIdDelete: (
      visitId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/visits/${visitId}`,
        method: "DELETE",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visit Services
     * @name AddVisitServiceVisitsVisitIdServicesPost
     * @summary Add Visit Service
     * @request POST:/visits/{visit_id}/services
     */
    addVisitServiceVisitsVisitIdServicesPost: (
      visitId: number,
      data: AddVisitService,
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/visits/${visitId}/services`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visit Services
     * @name UpsertVisitServicesVisitsVisitIdServicesPut
     * @summary Upsert Visit Services
     * @request PUT:/visits/{visit_id}/services
     */
    upsertVisitServicesVisitsVisitIdServicesPut: (
      visitId: number,
      data: VisitServiceUpdateOrCreate[],
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/visits/${visitId}/services`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visit Attachments
     * @name AddVisitAttachmentVisitsVisitIdAttachmentsPost
     * @summary Add Visit Attachment
     * @request POST:/visits/{visit_id}/attachments
     */
    addVisitAttachmentVisitsVisitIdAttachmentsPost: (
      visitId: number,
      data: BodyAddVisitAttachmentVisitsVisitIdAttachmentsPost,
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/visits/${visitId}/attachments`,
        method: "POST",
        body: data,
        type: ContentType.FormData,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns visit times for the given caregiver IDs and dates.
     *
     * @tags Visits
     * @name PostVisitsByCaregiversAndDatesVisitsByCaregiversAndDatesPost
     * @summary Get visits for caregivers on specific dates
     * @request POST:/visits/by-caregivers-and-dates
     */
    postVisitsByCaregiversAndDatesVisitsByCaregiversAndDatesPost: (
      data: VisitQueryByCaregiversAndDates,
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/visits/by-caregivers-and-dates`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns suggested visit slots (not persisted) for a client based on available caregivers matching a time slot, service IDs, and optional caregiver filters.
     *
     * @tags Visits
     * @name PostSuggestedVisitsFromAvailabilityVisitsSuggestedFromAvailabilityPost
     * @summary Suggest visits based on caregiver availability
     * @request POST:/visits/suggested-from-availability
     */
    postSuggestedVisitsFromAvailabilityVisitsSuggestedFromAvailabilityPost: (
      data: SuggestedVisitsRequest,
      params: RequestParams = {},
    ) =>
      this.request<SuggestedVisitSlot[], HTTPValidationError>({
        path: `/visits/suggested-from-availability`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Search visits by client name, caregiver name, or service name.
     *
     * @tags Visits
     * @name SearchVisitsVisitsSearchPost
     * @summary Search visits
     * @request POST:/visits/search
     */
    searchVisitsVisitsSearchPost: (
      data: SearchVisitsRequest,
      query?: {
        /**
         * Offset
         * Offset for pagination
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * Limit for pagination
         * @max 1000
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<SearchVisitResponse[], HTTPValidationError>({
        path: `/visits/search`,
        method: "POST",
        query: query,
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visit Services
     * @name DeleteVisitServiceVisitsVisitIdServicesVisitServiceIdDelete
     * @summary Delete Visit Service
     * @request DELETE:/visits/{visit_id}/services/{visit_service_id}
     */
    deleteVisitServiceVisitsVisitIdServicesVisitServiceIdDelete: (
      visitId: number,
      visitServiceId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/visits/${visitId}/services/${visitServiceId}`,
        method: "DELETE",
        ...params,
      }),
  };
  attachments = {
    /**
     * No description
     *
     * @tags Visit Attachments
     * @name DownloadAttachmentAttachmentsAttachmentIdDownloadGet
     * @summary Download Attachment
     * @request GET:/attachments/{attachment_id}/download
     */
    downloadAttachmentAttachmentsAttachmentIdDownloadGet: (
      attachmentId: number,
      params: RequestParams = {},
    ) =>
      this.request<AttachmentWithContent, HTTPValidationError>({
        path: `/attachments/${attachmentId}/download`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Visit Attachments
     * @name DeleteAttachmentAttachmentsAttachmentIdDelete
     * @summary Delete an attachment by ID
     * @request DELETE:/attachments/{attachment_id}
     */
    deleteAttachmentAttachmentsAttachmentIdDelete: (
      attachmentId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, HTTPValidationError>({
        path: `/attachments/${attachmentId}`,
        method: "DELETE",
        ...params,
      }),
  };
  visitServices = {
    /**
     * No description
     *
     * @tags Visit Services
     * @name UpdateVisitServiceVisitServicesVisitServiceIdPut
     * @summary Update Visit Service
     * @request PUT:/visit-services/{visit_service_id}
     */
    updateVisitServiceVisitServicesVisitServiceIdPut: (
      visitServiceId: number,
      data: VisitServiceUpdate,
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/visit-services/${visitServiceId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),
  };
}
