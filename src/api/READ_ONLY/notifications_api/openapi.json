{"openapi": "3.1.0", "info": {"title": "Notifications-Service", "description": "\n    Microservice for managing home care notifications.\n\n    ## Features\n    * Register tokens for users and send notifications\n    * Manage services and attachments\n    * Register device tokens and send push notifications\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    ", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/notifications/register": {"post": {"tags": ["NotificationToken"], "summary": "Register", "operationId": "register_notifications_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTokenCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationToken"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/{token_id}": {"get": {"tags": ["NotificationToken"], "summary": "Get Token", "operationId": "get_token_notifications__token_id__get", "parameters": [{"name": "token_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Token Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationToken"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/user/{user_id}": {"get": {"tags": ["NotificationToken"], "summary": "Get Tokens", "operationId": "get_tokens_notifications_user__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationToken"}, "title": "Response Get Tokens Notifications User  User Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/send/{user_id}": {"post": {"tags": ["NotificationToken"], "summary": "Send Notification", "operationId": "send_notification_notifications_send__user_id__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "title", "in": "query", "required": true, "schema": {"type": "string", "title": "Title"}}, {"name": "body", "in": "query", "required": true, "schema": {"type": "string", "title": "Body"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/user/{user_id}/all": {"get": {"tags": ["Notifications"], "summary": "List User Notifications", "operationId": "list_user_notifications_notifications_user__user_id__all_get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}, "title": "Response List User Notifications Notifications User  User Id  All Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/item/{notification_id}": {"get": {"tags": ["Notifications"], "summary": "Get Notification", "operationId": "get_notification_notifications_item__notification_id__get", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Notifications"], "summary": "Update User Notification", "operationId": "update_user_notification_notifications_item__notification_id__put", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Notifications"], "summary": "Delete User Notification", "operationId": "delete_user_notification_notifications_item__notification_id__delete", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Notification": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "title": {"type": "string", "title": "Title"}, "message": {"type": "string", "title": "Message"}, "type": {"type": "string", "enum": ["push", "email", "in_app"], "title": "Type"}, "isRead": {"type": "boolean", "title": "Isread", "default": false}, "sentAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Sentat"}, "readAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Readat"}, "targetUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "id": {"type": "integer", "title": "Id"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["userId", "title", "message", "type", "id", "createdAt"], "title": "Notification"}, "NotificationToken": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "deviceToken": {"type": "string", "minLength": 1, "title": "Devicetoken", "description": "FCM/APNs/Web push token"}, "platform": {"anyOf": [{"type": "string", "enum": ["ios", "android", "web"]}, {"type": "null"}], "title": "Platform", "description": "Platform type: ios, android, or web"}, "deviceInfo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deviceinfo", "description": "Optional device info, e.g., \"iPhone 14, iOS 17.3\""}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}, "lastUsedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Lastusedat"}, "id": {"type": "integer", "title": "Id"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "additionalProperties": false, "type": "object", "required": ["userId", "deviceToken", "id", "createdAt"], "title": "NotificationToken"}, "NotificationTokenCreate": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "deviceToken": {"type": "string", "minLength": 1, "title": "Devicetoken", "description": "FCM/APNs/Web push token"}, "platform": {"anyOf": [{"type": "string", "enum": ["ios", "android", "web"]}, {"type": "null"}], "title": "Platform", "description": "Platform type: ios, android, or web"}, "deviceInfo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deviceinfo", "description": "Optional device info, e.g., \"iPhone 14, iOS 17.3\""}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}, "lastUsedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Lastusedat"}}, "additionalProperties": false, "type": "object", "required": ["userId", "deviceToken"], "title": "NotificationTokenCreate"}, "NotificationUpdate": {"properties": {"isRead": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Isread"}, "readAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Readat"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "additionalProperties": false, "type": "object", "title": "NotificationUpdate"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}