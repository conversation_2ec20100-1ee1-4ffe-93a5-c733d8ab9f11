/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** MarkSelectedReadRequest */
export interface MarkSelectedReadRequest {
  /**
   * Notificationids
   * Notification IDs to mark as read
   * @minItems 1
   */
  notificationIds: number[];
}

/** MarkSelectedReadResponse */
export interface MarkSelectedReadResponse {
  /**
   * Userid
   * The user ID
   */
  userId: number;
  /**
   * Updatedcount
   * Number of notifications updated
   */
  updatedCount: number;
  /**
   * Updatedids
   * IDs that were marked as read
   */
  updatedIds?: number[];
  /**
   * Skippedids
   * IDs that were not updated (already read, not found, or not owned by user)
   */
  skippedIds?: number[];
}

/** Notification */
export interface Notification {
  /** Userid */
  userId: number;
  /** Title */
  title: string;
  /** Message */
  message: string;
  /** Type */
  type: "push" | "email" | "in_app";
  /**
   * Isread
   * @default false
   */
  isRead?: boolean;
  /** Sentat */
  sentAt?: string | null;
  /** Readat */
  readAt?: string | null;
  /** Targeturl */
  targetUrl?: string | null;
  /** Metadata for push notifications */
  metadata?: NotificationActionMetadata | null;
  /**
   * Actionat
   * Timestamp the caregiver clicked approve/reject
   */
  actionAt?: string | null;
  /**
   * Actionvalue
   * Decision value for actionable notification
   */
  actionValue?: "accept" | "reject" | null;
  /** Notificationid */
  notificationId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** NotificationActionMetadata */
export interface NotificationActionMetadata {
  /** Address */
  address: string;
  /** Clientfirstname */
  clientFirstName: string;
  /** Clientlastname */
  clientLastName: string;
  /** Caregiverfirstname */
  caregiverFirstName: string;
  /** Caregiverlastname */
  caregiverLastName: string;
  /** Visitstarttime */
  visitStartTime: string;
  /** Visitendtime */
  visitEndTime: string;
  /** Clientid */
  clientId: number;
  /** Visitid */
  visitId: number;
  /** Caregiverid */
  caregiverId: number;
}

/** NotificationActionRequest */
export interface NotificationActionRequest {
  /** Actionvalue */
  actionValue: "accept" | "reject";
  /** Visitid */
  visitId: number;
  /** Caregiverid */
  caregiverId: number;
  /** Metadata for push notifications */
  data?: NotificationActionMetadata | null;
}

/** NotificationActionResponse */
export interface NotificationActionResponse {
  /** Notificationid */
  notificationId: number;
  /** Action */
  action: "accept" | "reject";
  /** Visitid */
  visitId: number;
  /**
   * Actionat
   * @format date-time
   */
  actionAt: string;
  /** Adminsnotified */
  adminsNotified: number;
}

/** NotificationGetAllMinifiedPaginationResponse */
export interface NotificationGetAllMinifiedPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: NotificationMinified[];
  /** Totalunread */
  totalUnread: number;
}

/** NotificationGetAllPaginationResponse */
export interface NotificationGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: Notification[];
  /** Totalunread */
  totalUnread: number;
}

/** NotificationMinified */
export interface NotificationMinified {
  /** Notificationid */
  notificationId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Userid */
  userId: number;
  /** Title */
  title: string;
  /** Message */
  message: string;
  /**
   * Isread
   * @default false
   */
  isRead?: boolean;
  /**
   * Actionvalue
   * Decision value for actionable notification
   */
  actionValue?: "accept" | "reject" | null;
  /**
   * Caregiverfirstname
   * @default ""
   */
  caregiverFirstName?: string | null;
  /**
   * Caregiverlastname
   * @default ""
   */
  caregiverLastName?: string | null;
  /** Visitid */
  visitId?: number | null;
}

/** NotificationSendRequest */
export interface NotificationSendRequest {
  /**
   * Title
   * Notification title
   */
  title: string;
  /**
   * Body
   * Notification body
   */
  body: string;
  /**
   * Data
   * Extra metadata for deep-links or client handling
   */
  data?: Record<string, any> | null;
}

/** NotificationSendResponse */
export interface NotificationSendResponse {
  /**
   * Sent
   * Number of successfully sent push messages
   */
  sent: number;
  /**
   * Responses
   * FCM message IDs
   */
  responses?: string[];
}

/** NotificationToken */
export interface NotificationToken {
  /** Userid */
  userId: number;
  /**
   * Devicetoken
   * FCM/APNs/Web push token
   * @minLength 1
   */
  deviceToken: string;
  /**
   * Platform
   * Platform type: ios, android, or web
   */
  platform?: "ios" | "android" | "web" | null;
  /**
   * Deviceinfo
   * Optional device info, e.g., "iPhone 14, iOS 17.3"
   */
  deviceInfo?: string | null;
  /**
   * Isactive
   * @default true
   */
  isActive?: boolean;
  /** Lastusedat */
  lastUsedAt?: string | null;
  /** Notificationtokenid */
  notificationTokenId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** NotificationTokenCreate */
export interface NotificationTokenCreate {
  /** Userid */
  userId: number;
  /**
   * Devicetoken
   * FCM/APNs/Web push token
   * @minLength 1
   */
  deviceToken: string;
  /**
   * Platform
   * Platform type: ios, android, or web
   */
  platform?: "ios" | "android" | "web" | null;
  /**
   * Deviceinfo
   * Optional device info, e.g., "iPhone 14, iOS 17.3"
   */
  deviceInfo?: string | null;
  /**
   * Isactive
   * @default true
   */
  isActive?: boolean;
  /** Lastusedat */
  lastUsedAt?: string | null;
}

/** NotificationUpdate */
export interface NotificationUpdate {
  /** Isread */
  isRead?: boolean | null;
  /** Readat */
  readAt?: string | null;
  /** Metadata for push notifications */
  metadata?: NotificationActionMetadata | null;
  /** Actionat */
  actionAt?: string | null;
  /** Actionvalue */
  actionValue?: "accept" | "reject" | null;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/notifications-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title notifications
 * @version 1.0.0
 * @baseUrl /api/v1/notifications-api/
 *
 *
 * Microservice for managing home care notifications.
 *
 * ## Features
 * * Register tokens for users and send notifications
 * * Manage services and attachments
 * * Register device tokens and send push notifications
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  notifications = {
    /**
     * @description This endpoint allows a user to register a new notification token.
     *
     * @tags NotificationToken
     * @name RegisterNotificationsTokenRegisterPost
     * @summary Register a new notification token
     * @request POST:/notifications/token/register
     * @secure
     */
    registerNotificationsTokenRegisterPost: (
      data: NotificationTokenCreate,
      params: RequestParams = {},
    ) =>
      this.request<NotificationToken, HTTPValidationError | void>({
        path: `/notifications/token/register`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint retrieves a notification token by its ID.
     *
     * @tags NotificationToken
     * @name GetTokenNotificationsTokenTokenIdGet
     * @summary Get a notification token by its ID
     * @request GET:/notifications/token/{token_id}
     * @secure
     */
    getTokenNotificationsTokenTokenIdGet: (
      tokenId: number,
      params: RequestParams = {},
    ) =>
      this.request<NotificationToken, void | HTTPValidationError>({
        path: `/notifications/token/${tokenId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint retrieves all notification tokens for a specific user.
     *
     * @tags NotificationToken
     * @name GetTokensNotificationsTokenUserUserIdGet
     * @summary Get all notification tokens for a user
     * @request GET:/notifications/token/user/{user_id}
     * @secure
     */
    getTokensNotificationsTokenUserUserIdGet: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<NotificationToken[], HTTPValidationError | void>({
        path: `/notifications/token/user/${userId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Sends a push notification to a specific user and logs it to the DB.
     *
     * @tags Notifications
     * @name SendNotificationNotificationsSendUserIdPost
     * @summary Send a notification to a user
     * @request POST:/notifications/send/{user_id}
     * @secure
     */
    sendNotificationNotificationsSendUserIdPost: (
      userId: number,
      data: NotificationSendRequest,
      params: RequestParams = {},
    ) =>
      this.request<NotificationSendResponse, void | HTTPValidationError>({
        path: `/notifications/send/${userId}`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Single endpoint to list/search notifications with pagination and filters. Searches `title`/`message` with `q`, and filters by `userId`, `isRead`, `visitId`, and `action_values` (any of: accept, reject, null).
     *
     * @tags Notifications
     * @name ListNotificationsFullNotificationsGet
     * @summary List notifications paginated with search and filters
     * @request GET:/notifications
     * @secure
     */
    listNotificationsFullNotificationsGet: (
      query?: {
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 1
         * @default 100
         */
        limit?: number;
        /**
         * Q
         * Free-text search on title/message
         */
        q?: string | null;
        /**
         * User Id
         * Filter by user id
         */
        user_id?: number | null;
        /**
         * Is Read
         * Filter by read status
         */
        is_read?: boolean | null;
        /**
         * Visit Ids
         * Filter by multiple visit ids
         */
        visit_ids?: number[] | null;
        /**
         * Action Values
         * Filter by one or more action values. Repeat the param for multiple.
         */
        action_values?: ("accept" | "reject" | "null")[] | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        NotificationGetAllPaginationResponse,
        void | HTTPValidationError
      >({
        path: `/notifications`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Single endpoint to list/search notifications with pagination and filters. Searches `title`/`message` with `q`, and filters by `userId`, `isRead`, `visitId`, and `action_values` (any of: accept, reject, null).
     *
     * @tags Notifications
     * @name ListNotificationsMinifiedNotificationsMinifiedGet
     * @summary List notifications paginated with search and filters with minified schema
     * @request GET:/notifications/minified
     * @secure
     */
    listNotificationsMinifiedNotificationsMinifiedGet: (
      query?: {
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 1
         * @default 100
         */
        limit?: number;
        /**
         * Q
         * Free-text search on title/message
         */
        q?: string | null;
        /**
         * User Id
         * Filter by user id
         */
        user_id?: number | null;
        /**
         * Is Read
         * Filter by read status
         */
        is_read?: boolean | null;
        /**
         * Visit Ids
         * Filter by multiple visit ids
         */
        visit_ids?: number[] | null;
        /**
         * Action Values
         * Filter by one or more action values. Repeat the param for multiple.
         */
        action_values?: ("accept" | "reject" | "null")[] | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<
        NotificationGetAllMinifiedPaginationResponse,
        void | HTTPValidationError
      >({
        path: `/notifications/minified`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint retrieves a specific notification by its ID.
     *
     * @tags Notifications
     * @name GetNotificationNotificationsItemNotificationIdGet
     * @summary Get a notification by ID
     * @request GET:/notifications/item/{notification_id}
     * @secure
     */
    getNotificationNotificationsItemNotificationIdGet: (
      notificationId: number,
      params: RequestParams = {},
    ) =>
      this.request<Notification, void | HTTPValidationError>({
        path: `/notifications/item/${notificationId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Updates a notification
     *
     * @tags Notifications
     * @name UpdateUserNotificationNotificationsItemNotificationIdPut
     * @summary Updates a notification
     * @request PUT:/notifications/item/{notification_id}
     * @secure
     */
    updateUserNotificationNotificationsItemNotificationIdPut: (
      notificationId: number,
      data: NotificationUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Notification, void | HTTPValidationError>({
        path: `/notifications/item/${notificationId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Deletes a notification
     *
     * @tags Notifications
     * @name DeleteUserNotificationNotificationsItemNotificationIdDelete
     * @summary Deletes a notification
     * @request DELETE:/notifications/item/{notification_id}
     * @secure
     */
    deleteUserNotificationNotificationsItemNotificationIdDelete: (
      notificationId: number,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/notifications/item/${notificationId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description Updates all unread notifications for a user and marks them as read.
     *
     * @tags Notifications
     * @name MarkAllNotificationsReadNotificationsUserUserIdMarkAllReadPost
     * @summary Mark all notifications as read for a user
     * @request POST:/notifications/user/{user_id}/mark-all-read
     * @secure
     */
    markAllNotificationsReadNotificationsUserUserIdMarkAllReadPost: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<Record<string, any>, void | HTTPValidationError>({
        path: `/notifications/user/${userId}/mark-all-read`,
        method: "POST",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Marks the specified notification IDs as read for the given user.
     *
     * @tags Notifications
     * @name MarkSelectedNotificationsReadNotificationsUserUserIdMarkReadPost
     * @summary Mark selected notifications as read
     * @request POST:/notifications/user/{user_id}/mark-read
     * @secure
     */
    markSelectedNotificationsReadNotificationsUserUserIdMarkReadPost: (
      userId: number,
      data: MarkSelectedReadRequest,
      params: RequestParams = {},
    ) =>
      this.request<MarkSelectedReadResponse, void | HTTPValidationError>({
        path: `/notifications/user/${userId}/mark-read`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Updates the notification's `action_value` and `action_at`. If the value is **reject**, unassigns caregivers from the given visit and sends a broadcast notification to all administrators.
     *
     * @tags Notifications
     * @name ActOnNotificationNotificationsNotificationIdActionPost
     * @summary Record action on a notification (accept/reject) and send notification to admins
     * @request POST:/notifications/{notification_id}/action
     * @secure
     */
    actOnNotificationNotificationsNotificationIdActionPost: (
      notificationId: number,
      data: NotificationActionRequest,
      params: RequestParams = {},
    ) =>
      this.request<NotificationActionResponse, HTTPValidationError>({
        path: `/notifications/${notificationId}/action`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),
  };
}
