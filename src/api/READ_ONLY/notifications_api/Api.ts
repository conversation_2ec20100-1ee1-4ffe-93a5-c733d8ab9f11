/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** Notification */
export interface Notification {
  /** Userid */
  userId: number;
  /** Title */
  title: string;
  /** Message */
  message: string;
  /** Type */
  type: "push" | "email" | "in_app";
  /**
   * Isread
   * @default false
   */
  isRead?: boolean;
  /** Sentat */
  sentAt?: string | null;
  /** Readat */
  readAt?: string | null;
  /** Targeturl */
  targetUrl?: string | null;
  /** Metadata */
  metadata?: Record<string, any> | null;
  /** Id */
  id: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** NotificationToken */
export interface NotificationToken {
  /** Userid */
  userId: number;
  /**
   * Devicetoken
   * FCM/APNs/Web push token
   * @minLength 1
   */
  deviceToken: string;
  /**
   * Platform
   * Platform type: ios, android, or web
   */
  platform?: "ios" | "android" | "web" | null;
  /**
   * Deviceinfo
   * Optional device info, e.g., "iPhone 14, iOS 17.3"
   */
  deviceInfo?: string | null;
  /**
   * Isactive
   * @default true
   */
  isActive?: boolean;
  /** Lastusedat */
  lastUsedAt?: string | null;
  /** Id */
  id: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt?: string | null;
}

/** NotificationTokenCreate */
export interface NotificationTokenCreate {
  /** Userid */
  userId: number;
  /**
   * Devicetoken
   * FCM/APNs/Web push token
   * @minLength 1
   */
  deviceToken: string;
  /**
   * Platform
   * Platform type: ios, android, or web
   */
  platform?: "ios" | "android" | "web" | null;
  /**
   * Deviceinfo
   * Optional device info, e.g., "iPhone 14, iOS 17.3"
   */
  deviceInfo?: string | null;
  /**
   * Isactive
   * @default true
   */
  isActive?: boolean;
  /** Lastusedat */
  lastUsedAt?: string | null;
}

/** NotificationUpdate */
export interface NotificationUpdate {
  /** Isread */
  isRead?: boolean | null;
  /** Readat */
  readAt?: string | null;
  /** Metadata */
  metadata?: Record<string, any> | null;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Notifications-Service
 * @version 1.0.0
 *
 *
 *     Microservice for managing home care notifications.
 *
 *     ## Features
 *     * Register tokens for users and send notifications
 *     * Manage services and attachments
 *     * Register device tokens and send push notifications
 *
 *     ## Authentication
 *     All endpoints require authentication using JWT tokens.
 *
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * No description
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health Check
     * @request GET:/health
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  notifications = {
    /**
     * No description
     *
     * @tags NotificationToken
     * @name RegisterNotificationsRegisterPost
     * @summary Register
     * @request POST:/notifications/register
     */
    registerNotificationsRegisterPost: (
      data: NotificationTokenCreate,
      params: RequestParams = {},
    ) =>
      this.request<NotificationToken, HTTPValidationError>({
        path: `/notifications/register`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags NotificationToken
     * @name GetTokenNotificationsTokenIdGet
     * @summary Get Token
     * @request GET:/notifications/{token_id}
     */
    getTokenNotificationsTokenIdGet: (
      tokenId: number,
      params: RequestParams = {},
    ) =>
      this.request<NotificationToken, HTTPValidationError>({
        path: `/notifications/${tokenId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags NotificationToken
     * @name GetTokensNotificationsUserUserIdGet
     * @summary Get Tokens
     * @request GET:/notifications/user/{user_id}
     */
    getTokensNotificationsUserUserIdGet: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<NotificationToken[], HTTPValidationError>({
        path: `/notifications/user/${userId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags NotificationToken
     * @name SendNotificationNotificationsSendUserIdPost
     * @summary Send Notification
     * @request POST:/notifications/send/{user_id}
     */
    sendNotificationNotificationsSendUserIdPost: (
      userId: number,
      query: {
        /** Title */
        title: string;
        /** Body */
        body: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/notifications/send/${userId}`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notifications
     * @name ListUserNotificationsNotificationsUserUserIdAllGet
     * @summary List User Notifications
     * @request GET:/notifications/user/{user_id}/all
     */
    listUserNotificationsNotificationsUserUserIdAllGet: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<Notification[], HTTPValidationError>({
        path: `/notifications/user/${userId}/all`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notifications
     * @name GetNotificationNotificationsItemNotificationIdGet
     * @summary Get Notification
     * @request GET:/notifications/item/{notification_id}
     */
    getNotificationNotificationsItemNotificationIdGet: (
      notificationId: number,
      params: RequestParams = {},
    ) =>
      this.request<Notification, HTTPValidationError>({
        path: `/notifications/item/${notificationId}`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notifications
     * @name UpdateUserNotificationNotificationsItemNotificationIdPut
     * @summary Update User Notification
     * @request PUT:/notifications/item/{notification_id}
     */
    updateUserNotificationNotificationsItemNotificationIdPut: (
      notificationId: number,
      data: NotificationUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Notification, HTTPValidationError>({
        path: `/notifications/item/${notificationId}`,
        method: "PUT",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notifications
     * @name DeleteUserNotificationNotificationsItemNotificationIdDelete
     * @summary Delete User Notification
     * @request DELETE:/notifications/item/{notification_id}
     */
    deleteUserNotificationNotificationsItemNotificationIdDelete: (
      notificationId: number,
      params: RequestParams = {},
    ) =>
      this.request<any, HTTPValidationError>({
        path: `/notifications/item/${notificationId}`,
        method: "DELETE",
        format: "json",
        ...params,
      }),
  };
}
