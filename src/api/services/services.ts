import { Service } from '@app/types/service.types';
import { queryOptions } from '@tanstack/react-query';

export default function getServicesQueryParams(state: Service, id: string) {
  return queryOptions({
    queryKey: ['service', id],
    queryFn: () => getService(id),
    enabled: !state,
  });
}

const getService = async (id: string) => {
  console.log(id);
  const response = await fetch('https://jsonplaceholder.typicode.com/todos');
  return response.json();
};
