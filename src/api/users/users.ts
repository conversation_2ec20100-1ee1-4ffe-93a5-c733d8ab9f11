/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Dummy API
 * OpenAPI spec version: 1.0.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import type { User } from '../generated.schemas';

import getUsersMutator from '../../lib/axios';
import createUserMutator from '../../lib/axios';
import getUserByIdMutator from '../../lib/axios';
import updateUserMutator from '../../lib/axios';
import deleteUserMutator from '../../lib/axios';

/**
 * @summary Get all users
 */
export const getUsers = (signal?: AbortSignal) => {
  return getUsersMutator<User[]>({ url: `/users`, method: 'GET', signal });
};

export const getGetUsersQueryKey = () => {
  return [`/users`] as const;
};

export const getGetUsersQueryOptions = <TData = Awaited<ReturnType<typeof getUsers>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>>;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUsersQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsers>>> = ({ signal }) => getUsers(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUsers>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUsersQueryResult = NonNullable<Awaited<ReturnType<typeof getUsers>>>;
export type GetUsersQueryError = unknown;

export function useGetUsers<TData = Awaited<ReturnType<typeof getUsers>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<Awaited<ReturnType<typeof getUsers>>, TError, Awaited<ReturnType<typeof getUsers>>>,
        'initialData'
      >;
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUsers<TData = Awaited<ReturnType<typeof getUsers>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<Awaited<ReturnType<typeof getUsers>>, TError, Awaited<ReturnType<typeof getUsers>>>,
        'initialData'
      >;
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUsers<TData = Awaited<ReturnType<typeof getUsers>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get all users
 */

export function useGetUsers<TData = Awaited<ReturnType<typeof getUsers>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getGetUsersQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Create a new user
 */
export const createUser = (user: User, signal?: AbortSignal) => {
  return createUserMutator<void>({
    url: `/users`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: user,
    signal,
  });
};

export const getCreateUserMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof createUser>>, TError, { data: User }, TContext>;
}): UseMutationOptions<Awaited<ReturnType<typeof createUser>>, TError, { data: User }, TContext> => {
  const mutationKey = ['createUser'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createUser>>, { data: User }> = (props) => {
    const { data } = props ?? {};

    return createUser(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateUserMutationResult = NonNullable<Awaited<ReturnType<typeof createUser>>>;
export type CreateUserMutationBody = User;
export type CreateUserMutationError = unknown;

/**
 * @summary Create a new user
 */
export const useCreateUser = <TError = unknown, TContext = unknown>(
  options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createUser>>, TError, { data: User }, TContext> },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof createUser>>, TError, { data: User }, TContext> => {
  const mutationOptions = getCreateUserMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary Get a user by ID
 */
export const getUserById = (id: number, signal?: AbortSignal) => {
  return getUserByIdMutator<User>({ url: `/users/${id}`, method: 'GET', signal });
};

export const getGetUserByIdQueryKey = (id: number) => {
  return [`/users/${id}`] as const;
};

export const getGetUserByIdQueryOptions = <TData = Awaited<ReturnType<typeof getUserById>>, TError = void>(
  id: number,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserById>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserByIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserById>>> = ({ signal }) => getUserById(id, signal);

  return { queryKey, queryFn, enabled: !!id, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserById>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserByIdQueryResult = NonNullable<Awaited<ReturnType<typeof getUserById>>>;
export type GetUserByIdQueryError = void;

export function useGetUserById<TData = Awaited<ReturnType<typeof getUserById>>, TError = void>(
  id: number,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserById>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserById>>,
          TError,
          Awaited<ReturnType<typeof getUserById>>
        >,
        'initialData'
      >;
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserById<TData = Awaited<ReturnType<typeof getUserById>>, TError = void>(
  id: number,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserById>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserById>>,
          TError,
          Awaited<ReturnType<typeof getUserById>>
        >,
        'initialData'
      >;
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserById<TData = Awaited<ReturnType<typeof getUserById>>, TError = void>(
  id: number,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserById>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get a user by ID
 */

export function useGetUserById<TData = Awaited<ReturnType<typeof getUserById>>, TError = void>(
  id: number,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserById>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getGetUserByIdQueryOptions(id, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Update a user by ID
 */
export const updateUser = (id: number, user: User) => {
  return updateUserMutator<void>({
    url: `/users/${id}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: user,
  });
};

export const getUpdateUserMutationOptions = <TError = void, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof updateUser>>, TError, { id: number; data: User }, TContext>;
}): UseMutationOptions<Awaited<ReturnType<typeof updateUser>>, TError, { id: number; data: User }, TContext> => {
  const mutationKey = ['updateUser'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof updateUser>>, { id: number; data: User }> = (props) => {
    const { id, data } = props ?? {};

    return updateUser(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateUserMutationResult = NonNullable<Awaited<ReturnType<typeof updateUser>>>;
export type UpdateUserMutationBody = User;
export type UpdateUserMutationError = void;

/**
 * @summary Update a user by ID
 */
export const useUpdateUser = <TError = void, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<Awaited<ReturnType<typeof updateUser>>, TError, { id: number; data: User }, TContext>;
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof updateUser>>, TError, { id: number; data: User }, TContext> => {
  const mutationOptions = getUpdateUserMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary Delete a user by ID
 */
export const deleteUser = (id: number) => {
  return deleteUserMutator<void>({ url: `/users/${id}`, method: 'DELETE' });
};

export const getDeleteUserMutationOptions = <TError = void, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteUser>>, TError, { id: number }, TContext>;
}): UseMutationOptions<Awaited<ReturnType<typeof deleteUser>>, TError, { id: number }, TContext> => {
  const mutationKey = ['deleteUser'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteUser>>, { id: number }> = (props) => {
    const { id } = props ?? {};

    return deleteUser(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteUserMutationResult = NonNullable<Awaited<ReturnType<typeof deleteUser>>>;

export type DeleteUserMutationError = void;

/**
 * @summary Delete a user by ID
 */
export const useDeleteUser = <TError = void, TContext = unknown>(
  options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteUser>>, TError, { id: number }, TContext> },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof deleteUser>>, TError, { id: number }, TContext> => {
  const mutationOptions = getDeleteUserMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
