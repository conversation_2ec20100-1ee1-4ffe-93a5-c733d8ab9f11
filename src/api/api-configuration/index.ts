import { Api as UsersApi } from '@api/READ_ONLY//users_api/Api';
import { Api as CaregiverApi } from '@api/READ_ONLY/caregiver_api/Api';
import { Api as ClientApi } from '@api/READ_ONLY/client_api/Api';
import { Api as DoctorsApi } from '@api/READ_ONLY/doctors_api/Api';
import { Api as NotificationsApi } from '@api/READ_ONLY/notifications_api/Api';
import { Api as ServiceRequestApi } from '@api/READ_ONLY/service_request_api/Api';
import { Api as ServicesApi } from '@api/READ_ONLY/services_api/Api';
import { Api as VisitsApi } from '@api/READ_ONLY/visits_api/Api';
import { securityWorker } from './securityWorker';
const BASE_URL = 'https://test.disco.inlecom.gr:7443';

export const ApiClient = {
  clientApi: new ClientApi({
    baseURL: `${BASE_URL}/client-api`,
    securityWorker,
    secure: true,
  }),
  caregiverApi: new CaregiverApi({
    baseURL: `${BASE_URL}/caregiver-api`,
    securityWorker,
    secure: true,
  }),
  serviceApi: new ServicesApi({
    baseURL: `${BASE_URL}/service-api`,
    securityWorker,
    secure: true,
  }),
  visitsApi: new VisitsApi({
    baseURL: `${BASE_URL}/visit-api`,
    securityWorker,
    secure: true,
  }),
  serviceRequestsApi: new ServiceRequestApi({
    baseURL: `${BASE_URL}/service-request-api`,
    securityWorker,
    secure: true,
  }),
  usersApi: new UsersApi({
    baseURL: `${BASE_URL}/user-api`,
    securityWorker,
    secure: true,
  }),
  notificationsApi: new NotificationsApi({
    baseURL: `${BASE_URL}/notification-api`,
    securityWorker,
    secure: true,
  }),
  doctorsApi: new DoctorsApi({
    baseURL: `${BASE_URL}/doctor-api`,
    securityWorker,
    secure: true,
  }),
};
