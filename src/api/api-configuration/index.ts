import { setupResponseInterceptor } from '@api/inteceptors/capitalize.interceptor';
import { Api as UsersApi } from '@api/READ_ONLY//users_api/Api';
import { Api as CaregiverApi } from '@api/READ_ONLY/caregiver_api/Api';
import { Api as ClientApi } from '@api/READ_ONLY/client_api/Api';
import { Api as DoctorsApi } from '@api/READ_ONLY/doctors_api/Api';
import { Api as NotificationsApi } from '@api/READ_ONLY/notifications_api/Api';
import { Api as ServiceRequestApi } from '@api/READ_ONLY/service_request_api/Api';
import { Api as ServicesApi } from '@api/READ_ONLY/services_api/Api';
import { Api as VisitsApi } from '@api/READ_ONLY/visits_api/Api';
import envConfig from '@app/enviroment/enviroment';
import { securityWorker } from './securityWorker';
const BASE_URL = envConfig.getEnvKey('API_URL') as string;

const _ApiClient = {
  clientApi: new ClientApi({
    baseURL: `${BASE_URL}/api/v1/clients-api`,
    securityWorker,
    secure: true,
  }),
  caregiverApi: new CaregiverApi({
    baseURL: `${BASE_URL}/api/v1/caregivers-api`,
    securityWorker,
    secure: true,
  }),
  serviceApi: new ServicesApi({
    baseURL: `${BASE_URL}/api/v1/services-api`,
    securityWorker,
    secure: true,
  }),
  visitsApi: new VisitsApi({
    baseURL: `${BASE_URL}/api/v1/visits-api`,
    securityWorker,
    secure: true,
  }),
  serviceRequestsApi: new ServiceRequestApi({
    baseURL: `${BASE_URL}/api/v1/requests-api`,
    securityWorker,
    secure: true,
  }),
  usersApi: new UsersApi({
    baseURL: `${BASE_URL}/api/v1/users-api`,
    securityWorker,
    secure: true,
  }),
  notificationsApi: new NotificationsApi({
    baseURL: `${BASE_URL}/api/v1/notifications-api`,
    securityWorker,
    secure: true,
  }),
  doctorsApi: new DoctorsApi({
    baseURL: `${BASE_URL}/api/v1/doctors-api`,
    securityWorker,
    secure: true,
  }),
};

// Setup response interceptors for all API instances
Object.values(_ApiClient).forEach((api, index) => {
  const clientKey = Object.keys(_ApiClient)[index];
  setupResponseInterceptor(clientKey, api.instance);
});
console.info(`Capitalize Interceptor Set for all apis`);

export const ApiClient = _ApiClient;
