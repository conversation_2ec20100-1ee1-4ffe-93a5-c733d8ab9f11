import { GenericApiResponse } from '@api/types/genertic-api-response';
import { capitalizeFirst } from '@app/utils/capilatizeFirst';
import { AxiosInstance } from 'axios';

export const setupResponseInterceptor = (clientKey: string, axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.response.use(
    (response) => {
      response.data = processResponse(response.data);
      return response;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

function processResponse(responseData: unknown): unknown {
  // Handle null/undefined responses
  if (!responseData) {
    return responseData;
  }

  // Handle GenericApiResponse structure
  if (isGenericApiResponse(responseData)) {
    const hasDataAndArray = responseData.data && Array.isArray(responseData.data);

    if (hasDataAndArray) {
      responseData.data = (responseData.data as object[]).map((item) => processObject(item) as object);
    } else if (responseData.data && typeof responseData.data === 'object') {
      responseData.data = processObject(responseData.data) as object;
    }

    return responseData;
  }

  // Handle direct array responses
  if (Array.isArray(responseData)) {
    return responseData.map((item) => processObject(item));
  }

  // Handle direct object responses
  if (typeof responseData === 'object') {
    return processObject(responseData);
  }

  return responseData;
}

// Type guard to check if response follows GenericApiResponse structure
function isGenericApiResponse(data: unknown): data is GenericApiResponse {
  return Boolean(data && typeof data === 'object' && Object.prototype.hasOwnProperty.call(data, 'data'));
}

// Words/fields to capitalize
const capitalizeWords = [
  'firstname',
  'lastname',
  'city',
  'country',
  'address',
  'state',
  'street',
  'name',
  'title',
  'description',
  'mobility',
];

function processObject(obj: unknown): unknown {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays within objects
  if (Array.isArray(obj)) {
    return obj.map(processObject);
  }

  const newObj = { ...obj } as Record<string, unknown>;

  // Process all properties recursively
  Object.keys(newObj).forEach((key) => {
    const value = newObj[key];

    // Recursively process nested objects/arrays
    if (Array.isArray(value)) {
      newObj[key] = value.map(processObject);
    } else if (value && typeof value === 'object') {
      newObj[key] = processObject(value);
    } else if (capitalizeWords.includes(key.toLowerCase()) && typeof value === 'string') {
      // Capitalize specific fields
      newObj[key] = capitalizeFirst(value);
    }
  });

  return newObj;
}
