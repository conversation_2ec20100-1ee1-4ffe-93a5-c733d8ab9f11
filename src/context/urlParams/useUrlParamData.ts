import { useEffect, useState } from 'react';
import { useUrlParams } from './UrlParamProvider';

/**
 * useUrlParamData - fetches data based on current URL params
 * @param fetchFn - async function that accepts params and returns data
 * @returns { data, loading, error }
 */
export function UseUrlParamData<T>(fetchFn: (params: Record<string, string>) => Promise<T>) {
  const { params } = useUrlParams();
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<unknown>(null);

  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    setError(null);
    fetchFn(params)
      .then((result) => {
        if (isMounted) setData(result);
      })
      .catch((err) => {
        if (isMounted) setError(err);
      })
      .finally(() => {
        if (isMounted) setLoading(false);
      });
    return () => {
      isMounted = false;
    };
  }, [params]);

  return { data, loading, error };
}
