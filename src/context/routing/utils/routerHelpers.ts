import AvailabilityRoutes from '@app/routes/AvailabilityRoutes';
import CaregiverRoutes from '@app/routes/CaregiverRoutes';
import ClientRoutes from '@app/routes/ClientRoutes';
import DashboardRoutes from '@app/routes/DashboardRoutes';
import DoctorRoutes from '@app/routes/DoctorRoutes';
import SchedulingRoutes from '@app/routes/SchedulingRoutes';
import ServiceRequestsRoutes from '@app/routes/ServiceRequestsRoutes';
import ServiceRoutes from '@app/routes/ServiceRoutes';
import ServiceTypesRoutes from '@app/routes/ServiceTypesRoutes';
import ShiftsRoutes from '@app/routes/ShiftsRoutes';
import VisitRoutes from '@app/routes/VisitRoutes';
import { MenuItem, MenuStructure, NestedRoutes, Route, RoutesStructure } from '@app/types/routing.types';
import { addFullPathToRoutes } from './addFullPathsRoutes';

import {
  AVAILABILITY_PAGE,
  CAREGIVERS_LIST,
  CAREGIVERS_PREFIX,
  CLIENT_PAGE,
  CLIENTS_LIST,
  DASHBOARD_PAGE,
  DOCTOR_LIST,
  DOCTOR_PAGE,
  EDIT_CAREGIVER,
  EDIT_CLIENT,
  EDIT_DOCTOR,
  EDIT_SERVICE,
  EDIT_USER,
  NEW_CAREGIVER,
  NEW_CLIENT,
  NEW_DOCTOR,
  NEW_SERVICE,
  NEW_USER,
  SCHEDULING_PAGE,
  SERVICE_LIST,
  SERVICE_PAGE,
  SERVICE_REQUESTS_MANAGEMENT_PAGE,
  SERVICE_REQUESTS_MANAGEMENT_PAGE_LIST,
  SERVICE_REQUESTS_PAGE,
  SERVICE_TYPE_PAGE,
  SHIFTS_PAGE,
  USERS_LIST,
  USERS_PREFIX,
  VISITS_PAGE,
} from '@app/routes/urls';
import UserRoutes from '@app/routes/UserRoutes';

const Caregivers = {
  [CAREGIVERS_PREFIX]: {
    full_path: '',
    show: true,
    routes: {
      [NEW_CAREGIVER]: {
        full_path: '',
        route: CaregiverRoutes.CaregiverFormPage,
        show: true,
      },
      [CAREGIVERS_LIST]: {
        full_path: '',
        route: CaregiverRoutes.CaregiverTablePage,
        show: true,
      },
      [EDIT_CAREGIVER]: {
        full_path: '',
        route: CaregiverRoutes.CaregiverEditPage,
        show: true,
      },
    },
  },
} as const;

const Users = {
  [USERS_PREFIX]: {
    full_path: '',
    show: true,
    routes: {
      [USERS_LIST]: {
        full_path: '',
        route: UserRoutes.UsersTablePage,
        show: true,
      },
      [NEW_USER]: {
        full_path: '',
        route: UserRoutes.UserCreatePage,
        show: true,
      },
      [EDIT_USER]: {
        full_path: '',
        route: UserRoutes.UserEditPage,
        show: true,
      },
    },
  },
} as const;

export const generateRouteStructure = (): RoutesStructure => {
  const obj: RoutesStructure = {
    [DASHBOARD_PAGE]: {
      full_path: '',
      show: true,
      route: DashboardRoutes.DashboardPage,
    },
    ...Caregivers,
    ...Users,
    [CLIENT_PAGE]: {
      full_path: '',
      show: true,
      routes: {
        [NEW_CLIENT]: {
          full_path: '',
          route: ClientRoutes.ClientFormPage,
          show: true,
        },
        [CLIENTS_LIST]: {
          full_path: '',
          route: ClientRoutes.ClientTablePage,
          show: true,
        },
        [EDIT_CLIENT]: {
          full_path: '',
          route: ClientRoutes.ClientEditPage,
          show: true,
        },
      },
    },
    [DOCTOR_PAGE]: {
      full_path: '',
      show: true,
      routes: {
        [NEW_DOCTOR]: {
          full_path: '',
          route: DoctorRoutes.DoctorFormPage,
          show: true,
        },
        [DOCTOR_LIST]: {
          full_path: '',
          route: DoctorRoutes.DoctorTablePage,
          show: true,
        },
        [EDIT_DOCTOR]: {
          full_path: '',
          route: DoctorRoutes.DoctorEditPage,
          show: true,
        },
      },
    },
    [SERVICE_PAGE]: {
      full_path: '',
      show: true,
      routes: {
        [NEW_SERVICE]: {
          full_path: '',
          route: ServiceRoutes.ServiceFormPage,
          show: true,
        },
        [SERVICE_LIST]: {
          full_path: '',
          route: ServiceRoutes.ServiceTablePage,
          show: true,
        },
        [EDIT_SERVICE]: {
          full_path: '',
          route: ServiceRoutes.ServiceEditPage,
          show: true,
        },
      },
    },
    [SERVICE_TYPE_PAGE]: {
      full_path: '',
      show: true,
      route: ServiceTypesRoutes.ServiceTypeTablePage,
    },
    [SERVICE_REQUESTS_PAGE]: {
      full_path: '',
      show: true,
      route: ServiceRequestsRoutes.ServiceRequestsPage,
    },
    [AVAILABILITY_PAGE]: {
      full_path: '',
      show: true,
      route: AvailabilityRoutes.AvailabilityPage,
    },
    [SHIFTS_PAGE]: {
      full_path: '',
      show: true,
      route: ShiftsRoutes.ShiftsPage,
    },
    [SCHEDULING_PAGE]: {
      full_path: '',
      show: true,
      route: SchedulingRoutes.SchedulingRouteContainer,
    },
    [VISITS_PAGE]: {
      full_path: '',
      show: true,
      route: VisitRoutes.VisitTablePage,
    },
    [SERVICE_REQUESTS_MANAGEMENT_PAGE]: {
      full_path: '',
      show: true,
      routes: {
        [SERVICE_REQUESTS_MANAGEMENT_PAGE_LIST]: {
          full_path: '',
          route: ServiceRequestsRoutes.ServiceRequestsTablePage,
          show: true,
        },
      },
    },
  };

  addFullPathToRoutes(obj);
  return obj;
};

export const getAllPaths = (routes: RoutesStructure | NestedRoutes): string[] => {
  let paths: string[] = [];
  Object.values(routes).forEach((route: Route) => {
    if (route.full_path && route.show) paths.push(route.full_path);
    if ('routes' in route && route.routes) {
      paths = paths.concat(getAllPaths(route.routes));
    }
  });
  return paths;
};

export const getIndexRouteKey = (menu: MenuStructure, hasPermissions: boolean): string | null => {
  if (!hasPermissions || menu.length === 0) return null;
  const firstMenuItem = menu[0];
  return firstMenuItem?.children?.[0]?.key ?? firstMenuItem?.key ?? null;
};

export const getParentKey = (menu: MenuStructure, key: string): string | null => {
  const struct: Record<string, string> = {};
  menu.forEach((item: MenuItem) => {
    item.children?.forEach((child: MenuItem) => {
      struct[child.key] = item.key;
    });
  });
  return struct[key] || null;
};
