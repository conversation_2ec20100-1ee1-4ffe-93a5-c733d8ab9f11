import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Y_PAGE,
  <PERSON>ASH<PERSON>ARD_PAGE,
  SCHEDULING_PAGE,
  SERVICE_PAGE,
  SERVICE_REQUESTS_PAGE,
  SERVICE_TYPE_PAGE,
  SHIFTS_PAGE,
  VISITS_PAGE,
} from '@app/routes/urls';
import { FaUsersViewfinder } from 'react-icons/fa6';
import { LuClipboardPenLine } from 'react-icons/lu';
import { TbCalendarPlus, TbClock24 } from 'react-icons/tb';

import { MdScheduleSend } from 'react-icons/md';
import { RxDashboard } from 'react-icons/rx';

import { RoutesStructure } from '../../../types/routing.types';

// import { TShowFeature } from '@app/types/features.types';
// export default (RouteConsruct: RoutesStructure, showFeature: TShowFeature) => [
export default (RouteConsruct: RoutesStructure) => {
  console.log({ RouteConsruct });
  return [
    {
      label: 'Dashboard',
      key: RouteConsruct[DASHBOARD_PAGE].full_path,
      show: RouteConsruct[DASHBOARD_PAGE].show,
      icon: {
        filled: RxDashboard,
        outline: RxDashboard,
      },
    },
    {
      label: 'Management',
      key: 'management',
      show: true,
      children: [
        {
          label: 'Users',
          key: RouteConsruct.users.routes.list.full_path,
          show: RouteConsruct.users.routes.list.show,
          // icon: {
          //   filled: MdAdminPanelSettings,
          //   outline: MdAdminPanelSettings,
          // },
        },
        {
          label: 'Patients',
          key: RouteConsruct.clients.routes['list'].full_path,
          show: RouteConsruct.clients.routes['list'].show,
          // icon: {
          //   filled: MdAdminPanelSettings,
          //   outline: MdAdminPanelSettings,
          // },
        },
        {
          label: 'Caregivers',
          key: RouteConsruct.caregivers.routes.list.full_path,
          show: RouteConsruct.caregivers.routes.list.show,
          // icon: {
          //   filled: TbLayoutDashboardFilled,
          //   outline: TbLayoutDashboard,
          // },
        },
        {
          label: 'Visits',
          key: RouteConsruct[VISITS_PAGE].full_path,
          show: RouteConsruct[VISITS_PAGE].show,
        },
        {
          label: 'Service Requests',
          key: RouteConsruct['service-requests-management'].routes['list'].full_path,
          // key: RouteConsruct[SERVICE_REQUESTS_MANAGEMENT_PAGE].full_path,
          show: true,
        },
        {
          label: 'Doctors',
          key: RouteConsruct.doctors.routes['list'].full_path,
          show: true,
        },
        // {
        //   label: 'Doctors',
        //   key: RouteConsruct.doctor.routes['list'].full_path,
        //   show: RouteConsruct.doctor.routes['list'].show,
        //   // icon: {
        //   //   filled: TbUserFilled,
        //   //   outline: TbUser,
        //   // },
        // },
        {
          label: 'Services',
          key: 'services_root',
          show: true,
          // icon: {
          //   filled: TbLayoutDashboardFilled,
          //   outline: TbLayoutDashboard,
          // },
          children: [
            {
              label: 'Services',
              key: RouteConsruct[SERVICE_PAGE].routes['list'].full_path,
              show: RouteConsruct[SERVICE_PAGE].routes['list'].show,
              // icon: {
              //   filled: FaSuitcaseMedical,
              //   outline: LuBriefcaseMedical,
              // },
            },
            {
              label: 'Service Types',
              key: RouteConsruct[SERVICE_TYPE_PAGE].full_path,
              show: RouteConsruct[SERVICE_TYPE_PAGE].show,
              // icon: {
              //   filled: FaNotesMedical,
              //   outline: LiaNotesMedicalSolid,
              // },
            },
          ],
        },
      ],
      icon: {
        filled: FaUsersViewfinder,
        outline: FaUsersViewfinder,
      },
    },

    {
      label: 'Availability',
      key: RouteConsruct[AVAILABILITY_PAGE].full_path,
      show: RouteConsruct[AVAILABILITY_PAGE].show,
      icon: {
        filled: TbClock24,
        outline: TbClock24,
      },
    },
    {
      label: 'Service Request',
      key: RouteConsruct[SERVICE_REQUESTS_PAGE].full_path,
      show: RouteConsruct[SERVICE_REQUESTS_PAGE].show,
      icon: {
        filled: TbCalendarPlus,
        outline: TbCalendarPlus,
      },
    },
    {
      label: 'Shifts',
      key: RouteConsruct[SHIFTS_PAGE].full_path,
      show: RouteConsruct[SHIFTS_PAGE].show,
      icon: {
        filled: LuClipboardPenLine,
        outline: LuClipboardPenLine,
      },
    },

    {
      label: 'Scheduling',
      key: RouteConsruct[SCHEDULING_PAGE].full_path,
      show: RouteConsruct[SCHEDULING_PAGE].show,
      icon: {
        filled: MdScheduleSend,
        outline: MdScheduleSend,
      },
    },
    // {
    //   label: 'Timesheets',
    //   key: RouteConsruct[TIMESHEET_PREFIX].full_path,
    //   show: RouteConsruct.timesheet.show,
    //   icon: {
    //     filled: MdOutlineAccessTime,
    //     outline: MdAccessTime,
    //   },
    //   children: [
    //     {
    //       label: 'Print Timesheet',
    //       key: RouteConsruct.timesheet.routes['print-timesheet'].full_path,
    //       show: RouteConsruct.timesheet.routes['print-timesheet'].show,
    //     },
    //     {
    //       label: 'Timesheet List',
    //       key: RouteConsruct.timesheet.routes.list.full_path,
    //       show: RouteConsruct.timesheet.routes.list.show,
    //     },
    //     {
    //       label: 'Timesheet per Work Package',
    //       key: RouteConsruct.timesheet.routes['list/wp'].full_path,
    //       show: RouteConsruct.timesheet.routes['list/wp'].show,
    //     },
    //   ],
    // },
  ];
};
