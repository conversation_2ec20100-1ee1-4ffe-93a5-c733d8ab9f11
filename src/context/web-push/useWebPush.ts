import { WebPushContext } from '@web-push/WebPushContext';
import { useContext } from 'react';

/**
 * Hook to access WebPush context
 *
 * Provides access to web push notification functionality including:
 * - Enable/disable notifications
 * - Show/hide permission prompts
 * - Listen for messages (foreground and background)
 * - <PERSON>le remind later and close actions
 *
 * Must be used within a WebPushProvider
 */
export const useWebPush = () => {
  const context = useContext(WebPushContext);

  if (!context) {
    throw new Error('useWebPush must be used within a WebPushProvider');
  }

  return context;
};

export default useWebPush;
