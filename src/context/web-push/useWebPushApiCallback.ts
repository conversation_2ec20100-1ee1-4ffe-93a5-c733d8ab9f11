import { ApiClient } from '@api/api-configuration';
import { useAuthContext } from '@context/auth/useAuthContext';
import { useCallback } from 'react';

/**
 * Hook that provides a callback function for registering FCM tokens with the backend API
 *
 * This hook creates a callback that will be called by the WebPushProvider whenever
 * an FCM token is received. It handles the API call to register the token with the
 * user's account.
 */
export const useWebPushApiCallback = () => {
  const { user } = useAuthContext();

  const onToken = useCallback(
    async (token: string): Promise<void> => {
      try {
        console.log('WebPush: Registering token for user', user.userId);

        await ApiClient.notificationsApi.notifications.registerNotificationsTokenRegisterPost({
          userId: user.userId,
          deviceToken: token,
          platform: 'web',
        });

        console.log('WebPush: Token registered successfully');
      } catch (error) {
        console.error('WebPush: Error registering token:', error);
        throw error; // Re-throw so the provider can handle the error
      }
    },
    [user.userId]
  );

  return onToken;
};
