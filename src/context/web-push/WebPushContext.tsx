import { createContext } from 'react';

// Types
export interface WebPushMessage {
  title: string;
  body: string;
  type?: string;
}

export interface WebPushContextValue {
  /** Whether notifications are enabled (permission granted + token received) */
  enabled: boolean;
  /** Whether to show the permission prompt */
  showPrompt: boolean;
  /** The last received message (foreground or background) */
  lastMessage: WebPushMessage | null;
  /** Any error that occurred during enable process */
  error: string | null;
  /** Enable notifications: request permission, register SW, get FCM token */
  enable: () => Promise<void>;
  /** Refresh token (if permission already granted) */
  refreshToken: () => Promise<void>;
  /** Hide prompt and remind later */
  remindLater: (delayMs?: number) => void;
  /** Hide prompt permanently */
  closePrompt: () => void;
  /** Clear the last message */
  clearMessage: () => void;
  /** Clear any error */
  clearError: () => void;
}

// Context
export const WebPushContext = createContext<WebPushContextValue | null>(null);
