import { TMessage } from '@app/service-workers/firebase-messaging-sw';
import { getToken, Messaging } from 'firebase/messaging';
import { del, get } from 'idb-keyval';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { WebPushContext, WebPushContextValue, WebPushMessage } from './WebPushContext';

export interface WebPushProviderProps {
  children: React.ReactNode;
  /** Firebase messaging instance */
  messaging: Messaging;
  /** VAPID key for FCM */
  vapidKey: string;
  /** Service worker path (default: '/firebase-messaging-sw.js') */
  swPath?: string;
  /** IndexedDB key for background notifications (default: 'notification') */
  dbKey?: string;
  /** localStorage key for remind later (default: 'webpush_remind_later') */
  remindKey?: string;
  /** localStorage key for close (default: 'webpush_closed') */
  closeKey?: string;
  /** Callback when FCM token is received */
  onToken?: (token: string) => Promise<void>;
}

// Internal helpers
const getOrRegisterSW = async (swPath: string): Promise<ServiceWorkerRegistration> => {
  if (!('serviceWorker' in navigator)) {
    throw new Error('Service Worker not supported in this browser');
  }

  // Try to get existing registration first
  const existing = await navigator.serviceWorker.getRegistration('/firebase-push-notification-scope');
  if (existing) {
    return existing;
  }

  // Register new service worker
  return navigator.serviceWorker.register(swPath, {
    scope: '/firebase-push-notification-scope',
    updateViaCache: 'none', // Always check for updates
  });
};

const readAndClearDb = async (dbKey: string): Promise<WebPushMessage | null> => {
  try {
    const data = await get(dbKey);
    if (!data) return null;

    await del(dbKey);
    return data as WebPushMessage;
  } catch (error) {
    console.error('Error reading background notification from DB:', error);
    return null;
  }
};

const shouldShowPrompt = (permission: NotificationPermission, remindUntil: number | null, closed: boolean): boolean => {
  if (permission === 'granted') return false;
  if (permission === 'denied') return false;
  if (closed) return false;
  if (remindUntil && Date.now() < remindUntil) return false;

  return true;
};

// Provider Component
export const WebPushProvider: React.FC<WebPushProviderProps> = ({
  children,
  messaging,
  vapidKey,
  swPath = '/firebase-messaging-sw.js',
  dbKey = 'notification',
  remindKey = 'webpush_remind_later',
  closeKey = 'webpush_closed',
  onToken,
}) => {
  // State
  const [enabled, setEnabled] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebPushMessage | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Refs to avoid recreating listeners
  const messageHandlerRef = useRef<((event: MessageEvent) => void) | null>(null);

  // Helper to get localStorage values
  const getRemindUntil = useCallback((): number | null => {
    const value = localStorage.getItem(remindKey);
    return value ? parseInt(value, 10) : null;
  }, [remindKey]);

  const getClosed = useCallback((): boolean => {
    return localStorage.getItem(closeKey) === 'true';
  }, [closeKey]);

  const ParseMessage = useCallback((message: TMessage) => {
    console.log('WebPushProvider: Parsing message', message);

    let messageData: WebPushMessage | null = null;

    messageData = {
      title: message.notification.title || 'N/A',
      body: message.notification.body || 'N/A',
    };
    if (messageData) {
      setLastMessage(messageData);
    }
  }, []);

  // Handle service worker messages
  const handleSWMessage = useCallback(
    (event: MessageEvent) => {
      const Message = event.data as TMessage;
      ParseMessage(Message);
    },
    [ParseMessage]
  );

  // Setup service worker message listener
  useEffect(() => {
    if (!('serviceWorker' in navigator)) return;

    // Remove old listener if exists
    if (messageHandlerRef.current) {
      navigator.serviceWorker.removeEventListener('message', messageHandlerRef.current);
    }

    // Add new listener
    messageHandlerRef.current = handleSWMessage;
    navigator.serviceWorker.addEventListener('message', messageHandlerRef.current);

    return () => {
      if (messageHandlerRef.current) {
        navigator.serviceWorker.removeEventListener('message', messageHandlerRef.current);
      }
    };
  }, [handleSWMessage]);

  // Initialize on mount: check permission, read background notification, set prompt visibility
  useEffect(() => {
    const initialize = async () => {
      const permission = Notification.permission;
      const remindUntil = getRemindUntil();
      const closed = getClosed();

      console.log('WebPushProvider: Initializing', { permission, remindUntil, closed });

      // If already granted, we're enabled (but we'll need to get token when enable() is called)
      if (permission === 'granted') {
        setEnabled(true);
      }

      // Check if we should show the prompt
      setShowPrompt(shouldShowPrompt(permission, remindUntil, closed));

      // Read any background notification from IndexedDB
      const backgroundMessage = await readAndClearDb(dbKey);
      if (backgroundMessage) {
        console.log('WebPushProvider: Found background notification', backgroundMessage);
        ParseMessage(backgroundMessage as unknown as TMessage);
      }
    };

    initialize();
  }, [dbKey, getRemindUntil, getClosed, ParseMessage]);

  // Enable notifications: request permission → register SW → get token
  const enable = useCallback(async (): Promise<void> => {
    try {
      setError(null);

      console.log('WebPushProvider: Starting enable process');

      // 1. Request permission if not granted
      let permission = Notification.permission;
      if (permission !== 'granted') {
        permission = await Notification.requestPermission();
        console.log('WebPushProvider: Permission result:', permission);
        if (permission == 'granted') {
          window.location.reload();
        }
      }

      if (permission !== 'granted') {
        throw new Error(`Notification permission ${permission}`);
      }

      // 2. Register service worker and wait for it to be ready
      console.log('WebPushProvider: Registering service worker');
      const registration = await getOrRegisterSW(swPath);

      console.log('WebPushProvider: Waiting for service worker ready');
      await navigator.serviceWorker.ready;

      // 3. Get FCM token
      console.log('WebPushProvider: Getting FCM token');
      const token = await getToken(messaging, {
        vapidKey,
        serviceWorkerRegistration: registration,
      });

      if (!token) {
        throw new Error('Failed to get FCM token');
      }

      console.log('WebPushProvider: FCM token received');

      // 4. Call onToken callback if provided
      if (onToken) {
        console.log('WebPushProvider: Calling onToken callback');
        await onToken(token);
      }

      // 5. Update state
      setEnabled(true);
      setShowPrompt(false);

      console.log('WebPushProvider: Enable process completed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('WebPushProvider: Enable failed:', errorMessage);
      setError(errorMessage);

      // Update prompt visibility based on final permission state
      const finalPermission = Notification.permission;
      const remindUntil = getRemindUntil();
      const closed = getClosed();
      setShowPrompt(shouldShowPrompt(finalPermission, remindUntil, closed));
    }
  }, [messaging, vapidKey, swPath, onToken, getRemindUntil, getClosed]);

  // Refresh token (for already granted permissions)
  const refreshToken = useCallback(async (): Promise<void> => {
    if (Notification.permission !== 'granted') {
      console.warn('WebPushProvider: Cannot refresh token, permission not granted');
      return;
    }

    try {
      setError(null);

      console.log('WebPushProvider: Refreshing FCM token');

      const registration = await getOrRegisterSW(swPath);
      await navigator.serviceWorker.ready;

      const token = await getToken(messaging, {
        vapidKey,
        serviceWorkerRegistration: registration,
      });

      if (token && onToken) {
        await onToken(token);
      }

      setEnabled(true);
      console.log('WebPushProvider: Token refresh completed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh token';
      console.error('WebPushProvider: Token refresh failed:', errorMessage);
      setError(errorMessage);
    }
  }, [messaging, vapidKey, swPath, onToken]);

  // Remind later: hide prompt for specified duration
  const remindLater = useCallback(
    (delayMs: number = 24 * 60 * 60 * 1000): void => {
      const remindTime = Date.now() + delayMs;
      localStorage.setItem(remindKey, remindTime.toString());
      setShowPrompt(false);
      console.log('WebPushProvider: Remind later set for', new Date(remindTime));
    },
    [remindKey]
  );

  // Close prompt permanently
  const closePrompt = useCallback((): void => {
    localStorage.setItem(closeKey, 'true');
    setShowPrompt(false);
    console.log('WebPushProvider: Prompt closed permanently');
  }, [closeKey]);

  // Clear last message
  const clearMessage = useCallback((): void => {
    setLastMessage(null);
  }, []);

  // Clear error
  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  const contextValue: WebPushContextValue = {
    enabled,
    showPrompt,
    lastMessage,
    error,
    enable,
    refreshToken,
    remindLater,
    closePrompt,
    clearMessage,
    clearError,
  };

  return <WebPushContext.Provider value={contextValue}>{children}</WebPushContext.Provider>;
};
