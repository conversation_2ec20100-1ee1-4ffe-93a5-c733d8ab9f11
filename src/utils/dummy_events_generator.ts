import dayjs from 'dayjs';

type RandomEvent = {
  id: string;
  title: string;
  start: string;
  end: string;
  durationFormatted: string;
};

/**
 * Generates random non-overlapping events for the current week
 * Each event is at least 30 minutes and in multiples of 30 minutes
 * @param count number of events to generate
 */
export const generateRandomEventsForWeek = (
  count: number = 20,
  fixedDuration: number = 30 // in minutes, e.g., 60
): RandomEvent[] => {
  const events: RandomEvent[] = [];
  const scheduled: Record<string, { start: dayjs.Dayjs; end: dayjs.Dayjs }[]> = {};
  const now = dayjs();
  const startOfWeek = now.startOf('week').add(1, 'day'); // Monday

  const possibleDurations = fixedDuration ? [fixedDuration] : [30, 60, 90, 120, 150, 180];

  const maxAttempts = 1000;
  let attempts = 0;
  let eventId = 1;

  while (events.length < count && attempts < maxAttempts) {
    attempts++;

    const randomDayIndex = Math.floor(Math.random() * 7); // 0 (Mon) to 6 (Sun)
    const day = startOfWeek.add(randomDayIndex, 'day');
    const dayKey = day.format('YYYY-MM-DD');

    const randomHour = 8 + Math.floor(Math.random() * 10); // 8 to 17
    const randomMinute = Math.floor(Math.random() * 2) * 30; // 0 or 30

    const start = day.hour(randomHour).minute(randomMinute).second(0).millisecond(0);

    const durationMinutes = possibleDurations[Math.floor(Math.random() * possibleDurations.length)];
    const end = start.add(durationMinutes, 'minute');

    // Skip if event ends after 20:00
    if (end.hour() > 20 || (end.hour() === 20 && end.minute() > 0)) continue;

    // Check for overlap
    const existing = scheduled[dayKey] || [];
    const overlaps = existing.some((e) => start.isBefore(e.end) && end.isAfter(e.start));
    if (overlaps) continue;

    if (!scheduled[dayKey]) scheduled[dayKey] = [];
    scheduled[dayKey].push({ start, end });

    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    const durationFormatted = [hours > 0 ? `${hours}h` : '', minutes > 0 ? `${minutes}m` : '']
      .filter(Boolean)
      .join(' ');

    events.push({
      id: `${eventId++}`,
      title: `Event ${eventId}`,
      start: start.toISOString(),
      end: end.toISOString(),
      durationFormatted,
    });
  }

  return events;
};
