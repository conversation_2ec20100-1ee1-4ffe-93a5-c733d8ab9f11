import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

// Extend dayjs with duration plugin
dayjs.extend(duration);

export const formatMinutesToHours = (minutes: number) => {
  const dur = dayjs.duration(minutes, 'minutes');
  const hours = dur.hours();
  const mins = dur.minutes();

  if (hours && mins) return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minute${mins > 1 ? 's' : ''}`;
  if (hours) return `${hours} hour${hours > 1 ? 's' : ''}`;
  return `${mins} minute${mins > 1 ? 's' : ''}`;
};
