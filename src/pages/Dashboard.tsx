import { Visit } from '@app/api/READ_ONLY/visits_api/Api';
import { useEffect, useState } from 'react';

export const Dashboard = () => {
  const [data, setData] = useState<Visit[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        // const response = await apiFactory.visits_api.visits.getAllVisitsVisitsGet({ offset: 0, limit: 100 }); // assuming your api client has this method
        // const response = await ApiClient.visitsApi.visits.getAllVisitsVisitsGet({ offset: 0, limit: 100 });
        // if (response) setData(response.data);
        setData([]);
      } catch (err) {
        setError('Failed to fetch dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) return <div>Loading dashboard...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>Dashboard</h1>
      {data ? (
        <div>
          <p>{data[0]?.id}</p>
          {/* Render other data here */}
        </div>
      ) : (
        <p>No data available.</p>
      )}
    </div>
  );
};

export default Dashboard;
