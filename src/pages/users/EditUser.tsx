import { ApiClient } from '@api/api-configuration';
import { RoleResponse, UserResponse, UserUpdateReq } from '@api/READ_ONLY/users_api/Api';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { UserForm } from '@feat-users/components/UserForm/UserForm';
import { UserFormData } from '@feat-users/types';
import { Alert, Card, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

export const EditUser: React.FC = () => {
  const { openNotification } = useNotifications();
  const { id } = useParams<{ id: string }>();
  const [user, setUser] = useState<UserResponse | null>(null);
  const [roles, setRoles] = useState<RoleResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!id) {
        setError('User ID is required');
        setLoading(false);
        return;
      }

      try {
        // Fetch both user data and roles in parallel
        const [userResponse, rolesResponse] = await Promise.all([
          ApiClient.usersApi.users.getUserUsersUserIdGet(Number(id)),
          ApiClient.usersApi.users.getRolesUsersRolesGet(),
        ]);

        setUser(userResponse.data);
        setRoles(rolesResponse.data);
      } catch (err) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load user data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const handleSubmit = async (formData: UserFormData) => {
    try {
      // For now, we'll just show an error since we only have create API
      // When update API becomes available, we can implement it here using formData

      const payload: UserUpdateReq = {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        avatarUrl: formData.avatarUrl || null,
        roles: formData.roles || [],
      };
      await ApiClient.usersApi.users.updateUserUsersUserIdPut(Number(id), payload);

      // This code will run when update API is available:
      openNotification('topRight', {
        title: 'User',
        description: 'User updated successfully.',
        type: 'Success',
      });

      // Navigate back to users list after successful update
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: 'User',
          description: 'User update failed. ' + errorData.detail,
          type: 'Warning',
        });
      } else {
        openNotification('topRight', {
          title: 'User',
          description: 'User update failed. Please try again.',
          type: 'Warning',
        });
      }
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card title="Edit User" bordered={false}>
          <div className="flex justify-center">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card title="Edit User" bordered={false}>
          <Alert message="Error" description={error} type="error" showIcon />
        </Card>
      </div>
    );
  }

  return <UserForm data={user || undefined} roles={roles} onSubmit={handleSubmit} />;
};
