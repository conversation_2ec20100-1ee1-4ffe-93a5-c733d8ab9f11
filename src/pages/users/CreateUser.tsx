import { ApiClient } from '@api/api-configuration';
import { RoleResponse } from '@api/READ_ONLY/users_api/Api';
import { APP_PREFIX, EDIT_USER, USERS_PREFIX } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { UserForm } from '@feat-users/components/UserForm/UserForm';
import { CreateUserPayload, UserFormData } from '@feat-users/types';
import { Alert, Card, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

export const CreateUser: React.FC = () => {
  const navigate = useNavigate();
  const { openNotification } = useNotifications();
  const [roles, setRoles] = useState<RoleResponse[]>([]);
  const [rolesLoading, setRolesLoading] = useState(true);
  const [rolesError, setRolesError] = useState<string | null>(null);

  // Fetch roles on component mount
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await ApiClient.usersApi.users.getRolesUsersRolesGet();
        setRoles(response.data);
      } catch (error) {
        console.error('Failed to fetch roles:', error);
        setRolesError('Failed to load user roles');
      } finally {
        setRolesLoading(false);
      }
    };

    fetchRoles();
  }, []);

  const handleSubmit = async (formData: UserFormData) => {
    try {
      const payload: CreateUserPayload = {
        email: formData.email,
        firstName: formData.firstName || null,
        lastName: formData.lastName || null,
        avatarUrl: formData.avatarUrl || null,
        roles: formData.roles || [],
      };

      const res = await ApiClient.usersApi.users.createUserUsersPost(payload);

      openNotification('topRight', {
        title: 'User',
        description: 'User created successfully.',
        type: 'Success',
      });

      // Navigate to edit page with the new user ID
      navigate(`/${APP_PREFIX}/${USERS_PREFIX}/${EDIT_USER.replace(':id', String(res.data.userId))}`);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: 'User',
          description: 'User creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      } else {
        openNotification('topRight', {
          title: 'User',
          description: 'User creation failed. Please try again.',
          type: 'Warning',
        });
      }
    }
  };

  if (rolesLoading) {
    return (
      <div className="p-6">
        <Card title="Create New User" bordered={false}>
          <div className="flex justify-center">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    );
  }

  if (rolesError) {
    return (
      <div className="p-6">
        <Card title="Create New User" bordered={false}>
          <Alert message="Error" description={rolesError} type="error" showIcon />
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <Card title="Create New User" bordered={false}>
        <UserForm roles={roles} onSubmit={handleSubmit} />
      </Card>
    </div>
  );
};
