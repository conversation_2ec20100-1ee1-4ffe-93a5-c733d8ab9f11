import { ApiClient } from '@api/api-configuration';
import { UserResponse } from '@api/READ_ONLY/users_api/Api';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, EDIT_USER, USERS_PREFIX } from '@app/routes/urls';
import QuickCreateUserModal from '@feat-users/components/UserTable/QuickCreateUserModal';
import { UserRow, getUserTableColumns } from '@feat-users/components/UserTable/userTableColumns.config';
import { App as AntdApp } from 'antd';
import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

// URL param configuration for users
const USER_URL_PARAM_CONFIG = {
  external: {
    searchTerm: 'search',
  },
} as const;

const UsersList: React.FC = () => {
  const navigate = useNavigate();
  const [reloadKey, setReloadKey] = React.useState(0);
  const [isCreateOpen, setIsCreateOpen] = React.useState(false);
  const { modal, message } = AntdApp.useApp();

  const transformUserToRow = (user: UserResponse): UserRow => ({
    ...user,
    id: user.userId,
  });

  const fetchUsers = async (params: { limit: number; offset: number; query?: string }) => {
    const searchQuery = { ...(params.query ? { query: params.query } : {}) };
    const response = await ApiClient.usersApi.users.getUsersUsersGet({
      ...searchQuery,
      limit: params.limit,
      offset: params.offset,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const editNavigation = useCallback(
    (userId: number) => {
      const path = `/${APP_PREFIX}/${USERS_PREFIX}/${EDIT_USER.replace(':id', String(userId))}`;
      navigate(path);
    },
    [navigate]
  );

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'users',
      urlParamConfig: USER_URL_PARAM_CONFIG,
      fetchData: fetchUsers,
      transformData: transformUserToRow,
      columns: getUserTableColumns(editNavigation),
      searchPlaceholder: 'Search users...',
      addButton: {
        text: 'Add User',
        onClick: () => setIsCreateOpen(true),
      },
      actions: {
        onEdit: (row: UserRow) => {
          editNavigation(row.userId);
        },
        onDelete: async (row: UserRow) => {
          modal.warning({
            title: 'Delete user',
            content: `Are you sure you want to delete ${row.firstName || ''} ${row.lastName || ''}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            okCancel: true,
            cancelText: 'Cancel',
            onOk: async () => {
              try {
                await ApiClient.usersApi.users.deleteUserUsersUserIdDelete(row.userId);
                message.success('User deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete user');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [editNavigation, message, modal]
  );

  return (
    <>
      <GenericDataTable
        key={reloadKey}
        config={tableConfig}
        suffixHoverInfo={<>Search by firstname, lastname, email</>}
      />
      <QuickCreateUserModal
        open={isCreateOpen}
        onCancel={() => setIsCreateOpen(false)}
        onCreated={() => {
          setIsCreateOpen(false);
          setReloadKey((k) => k + 1);
        }}
        onNavigateToEdit={(userId) => {
          setIsCreateOpen(false);
          editNavigation(userId);
        }}
      />
    </>
  );
};

export default UsersList;
