import getServicesQueryParams from '@app/api/services/services';
import { ServiceForm } from '@app/features/services/components/ServiceForm/ServiceForm';
import { useQuery } from '@tanstack/react-query';
import { useLocation, useParams } from 'react-router-dom';

export const EditService = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit

  const { isPending, data } = useQuery(getServicesQueryParams(state, id ? id : ''));
  const serviceData = state || data;

  if (!serviceData && isPending) return <div>Loading...</div>;
  if (!serviceData) return <div>Error loading client</div>;
  return <ServiceForm data={serviceData} />;
};
