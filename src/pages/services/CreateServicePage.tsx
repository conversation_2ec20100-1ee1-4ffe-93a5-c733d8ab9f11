import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import { ServiceForm } from '@app/features/services/components/ServiceForm/ServiceForm';
import useNotifications from '@context/notifications/useNotificationContext';
import { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

export const EditService = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit
  const { openNotification } = useNotifications();

  const [data, setData] = useState<Service>();
  useEffect(() => {
    const fetchCaregiver = async () => {
      try {
        if (id) {
          const response = await ApiClient.serviceApi.services.getServiceServicesServiceIdGet(Number(id));
          setData(response.data);
        }
      } catch (err) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Failed to fetch caregiver',
          type: 'Warning',
        });
      }
    };
    console.log('state', state);

    fetchCaregiver();
  }, [id, openNotification, state]);
  const serviceData = data;

  if (!serviceData) return <div>Loading...</div>;
  if (!serviceData) return <div>Error loading client</div>;
  return <ServiceForm data={serviceData} />;
};
