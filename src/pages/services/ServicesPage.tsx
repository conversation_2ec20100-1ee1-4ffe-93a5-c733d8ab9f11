import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import {
  getServiceTableColumns,
  ServiceRow,
} from '@app/features/services/components/ServicesTable/servicesTableColumns.config';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, CREATE, EDIT_SERVICE, SERVICE_PAGE } from '@app/routes/urls';
import { App as AntdApp } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const ServicesListPage: React.FC = () => {
  const navigate = useNavigate();
  const { modal, message } = AntdApp.useApp();
  const [reloadKey, setReloadKey] = React.useState(0);

  const editNavigation = React.useCallback(
    (serviceId: number) => {
      const path = `/${APP_PREFIX}/${SERVICE_PAGE}/${EDIT_SERVICE.replace(':id', String(serviceId))}`;
      navigate(path);
    },
    [navigate]
  );

  const fetchServices = async ({ limit, offset, query }: { limit: number; offset: number; query?: string }) => {
    const response = await ApiClient.serviceApi.services.getServicesServicesGet({
      limit,
      offset,
      query,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'services',
      urlParamConfig: { external: { searchTerm: 'search' } },
      fetchData: fetchServices,
      transformData: (service: Service) => ({ ...service, id: service.serviceId }),
      columns: getServiceTableColumns([], editNavigation),
      searchPlaceholder: 'Search services...',
      addButton: {
        text: 'Add Service',
        onClick: () => navigate(`/${APP_PREFIX}/${SERVICE_PAGE}/${CREATE}`),
      },
      actions: {
        onEdit: (row: ServiceRow) => {
          editNavigation(row.serviceId);
        },
        onDelete: async (row: ServiceRow) => {
          modal.confirm({
            title: 'Delete service',
            content: `Are you sure you want to delete ${row.name || 'this service'}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            onOk: async () => {
              try {
                await ApiClient.serviceApi.services.deleteServiceServicesServiceIdDelete(row.serviceId);
                message.success('Service deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete service');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [navigate, modal, message, editNavigation]
  );

  return <GenericDataTable key={reloadKey} config={tableConfig} suffixHoverInfo={<>Search by name, type</>} />;
};

export default ServicesListPage;
