import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import ServiceTable from '@app/features/services/components/ServicesTable/ServicesTable';
import { getServiceTableColumns } from '@app/features/services/components/ServicesTable/servicesTableColumns.config';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useEffect, useMemo } from 'react';

const initialFilters = {};

export const ServiceList = () => {
  const serviceTypes = useSchedulingStore((state) => state.serviceTypes);
  const setServiceTypes = useSchedulingStore((state) => state.setServiceTypes);
  const setServices = useSchedulingStore((state) => state.setServices);
  const setLoading = useSchedulingStore((state) => state.setLoadingServices);

  // Fetch service types once
  useEffect(() => {
    const fetchServiceTypes = async () => {
      try {
        setLoading(true);
        if (!serviceTypes.length) {
          const res = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet();
          setServiceTypes(res.data ?? []);
        }
      } catch (error) {
        console.error('Failed to fetch service types', error);
      } finally {
        setLoading(false);
      }
    };

    fetchServiceTypes();
  }, [serviceTypes.length, setServiceTypes, setLoading]);

  // Generate columns when serviceTypes are available
  const columns: ExtendedTableColumnType<Service>[] = useMemo(
    () => getServiceTableColumns(serviceTypes),
    [serviceTypes]
  );

  return (
    <TableProvider
      storageKey="servicesTable"
      storageKeyColumns="servicesTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={[]}
      fetchData={async () => {
        try {
          setLoading(true);
          const response = await ApiClient.serviceApi.services.getServicesServicesGet();
          setServices(response.data);
        } catch (error) {
          console.error('Failed to fetch services', error);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns}
    >
      <ServiceTable />
    </TableProvider>
  );
};
