import { ApiClient } from '@api/api-configuration';
import { Client } from '@app/types/client';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import ClientTable from '@feat-clients/components/ClientTable/ClientTable';
import { getClientTableColumns } from '@feat-clients/components/ClientTable/clientTableColumns.config';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: 'StartDate:desc',
  personId: '',
  status: '0,1,2,3',
  orgId: 0,
};
const filters = [
  { Id: 0, label: 'Submitted', value: false },
  { Id: 1, label: 'Approved', value: false },
  { Id: 2, label: 'Rejected', value: false },
];
export const ClientsList = () => {
  const columns: () => ExtendedTableColumnType<Client>[] = useCallback(() => getClientTableColumns(), []);
  const setClients = useSchedulingStore((state) => state.setClients);
  const setLoading = useSchedulingStore((state) => state.setLoadingClients);
  console.log('COLUMNS', columns);
  return (
    <TableProvider
      storageKey="clientTable"
      storageKeyColumns="clientTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={filters}
      fetchData={async () => {
        setLoading(true);
        try {
          const response = await ApiClient.clientApi.clients.getClientsClientsGet();
          setClients(response.data);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns()}
    >
      <ClientTable />
    </TableProvider>
  );
};
