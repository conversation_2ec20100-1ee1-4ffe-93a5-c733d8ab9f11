import getClientsQueryParams from '@app/api/clients/clients';
import { ClientForm } from '@feat-clients/components/ClientForm/ClientForm';
import { useQuery } from '@tanstack/react-query';
import { useLocation, useParams } from 'react-router-dom';

export const EditClient = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit

  const { isPending, data } = useQuery(getClientsQueryParams(state, id ? id : ''));
  const clientData = state || data;

  if (!clientData && isPending) return <div>Loading...</div>;
  if (!clientData) return <div>Error loading client</div>;
  return <ClientForm data={clientData} />;
};
