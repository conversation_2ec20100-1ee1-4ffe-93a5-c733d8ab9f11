import { ApiClient } from '@api/api-configuration';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, CLIENT_PAGE, EDIT_CLIENT } from '@app/routes/urls';
import { ClientRow, getClientTableColumns } from '@feat-clients/components/ClientTable/clientTableColumns.config';
import QuickCreateClientModal from '@feat-clients/components/ClientTable/QuickCreateClientModal';
import { App as AntdApp } from 'antd';
import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

// URL param configuration for clients
const CLIENT_URL_PARAM_CONFIG = {
  external: {
    searchTerm: 'search',
  },
} as const;

const ClientListPage: React.FC = () => {
  const navigate = useNavigate();
  const [reloadKey, setRelo<PERSON><PERSON>ey] = React.useState(0);
  const [isCreateOpen, setIsCreateOpen] = React.useState(false);
  const { modal, message } = AntdApp.useApp();

  const transformClientToRow = (client: Client): ClientRow => ({
    ...client,
    id: client.clientId,
  });

  const fetchClients = async (params: { limit: number; offset: number; query?: string }) => {
    const searchQuery = { ...(params.query ? { query: params.query } : {}) };
    const response = await ApiClient.clientApi.clients.getClientsClientsGet({
      ...searchQuery,
      limit: params.limit,
      offset: params.offset,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const editNavigation = useCallback(
    (clientId: number) => {
      const path = `/${APP_PREFIX}/${CLIENT_PAGE}/${EDIT_CLIENT.replace(':id', String(clientId))}`;
      navigate(path);
    },
    [navigate]
  );

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'clients',
      urlParamConfig: CLIENT_URL_PARAM_CONFIG,
      fetchData: fetchClients,
      transformData: transformClientToRow,
      columns: getClientTableColumns(editNavigation),
      searchPlaceholder: 'Search clients...',
      addButton: {
        text: 'Add Client',
        onClick: () => setIsCreateOpen(true),
      },
      actions: {
        onEdit: (row: ClientRow) => {
          editNavigation(row.clientId);
        },
        onDelete: async (row: ClientRow) => {
          modal.warning({
            title: 'Delete client',
            content: `Are you sure you want to delete ${row.firstName || ''} ${row.lastName || ''}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            okCancel: true,
            cancelText: 'Cancel',
            onOk: async () => {
              try {
                await ApiClient.clientApi.clients.deleteClientClientsClientIdDelete(String(row.clientId));
                message.success('Client deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete client');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [editNavigation, message, modal]
  );

  return (
    <>
      <GenericDataTable
        key={reloadKey}
        config={tableConfig}
        suffixHoverInfo={<>Search by firstname, lastname, phone</>}
      />
      <QuickCreateClientModal
        open={isCreateOpen}
        onCancel={() => setIsCreateOpen(false)}
        onCreated={() => {
          setIsCreateOpen(false);
          setReloadKey((k) => k + 1);
        }}
      />
    </>
  );
};

export default ClientListPage;
