import { ApiClient } from '@api/api-configuration';
import { ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, SERVICE_REQUESTS_PAGE } from '@app/routes/urls';
import {
  getServiceRequestTableColumns,
  ServiceRequestRow,
} from '@feat-service-requests/components/ServiceRequestTable/serviceRequestTableColumns.config';
import { App as AntdApp } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const ServiceRequestsListPage: React.FC = () => {
  const navigate = useNavigate();
  const { modal, message } = AntdApp.useApp();
  const [reloadKey, setReloadKey] = React.useState(0);
  const fetchServiceRequests = async ({ limit, offset, query }: { limit: number; offset: number; query?: string }) => {
    const response = await ApiClient.serviceRequestsApi.serviceRequests.getRequestsServiceRequestsGet({
      limit,
      offset,
      query,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'serviceRequests',
      urlParamConfig: { external: { searchTerm: 'search' } },
      fetchData: fetchServiceRequests,
      transformData: (request: ServiceRequestResponse) => ({ ...request, id: request.serviceRequestId }),
      columns: getServiceRequestTableColumns(),
      searchPlaceholder: 'Search service requests...',
      addButton: {
        text: 'Add Service Request',
        onClick: () => navigate(`/${APP_PREFIX}/${SERVICE_REQUESTS_PAGE}`),
      },
      actions: {
        onDelete: async (row: ServiceRequestRow) => {
          modal.confirm({
            title: 'Delete service request',
            content: `Are you sure you want to delete request #${row.serviceRequestId}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            onOk: async () => {
              try {
                await ApiClient.serviceRequestsApi.serviceRequests.deleteRequestServiceRequestsRequestIdDelete(
                  row.serviceRequestId
                );
                message.success('Service request deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete service request');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [navigate, modal, message]
  );

  return <GenericDataTable key={reloadKey} config={tableConfig} suffixHoverInfo={<>Search by client, service</>} />;
};

export default ServiceRequestsListPage;
