import { ApiClient } from '@api/api-configuration';
import ServiceTypeTable from '@app/features/serviceTypes/components/ServiceTypesTable/ServiceTypesTable';
import { getServiceTypesTableColumns } from '@app/features/serviceTypes/components/ServiceTypesTable/serviceTypesTableColumns.config';
import { ServiceType } from '@app/types/service.types';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: '',
};

export const ServiceTypesList = () => {
  const columns: () => ExtendedTableColumnType<ServiceType>[] = useCallback(() => getServiceTypesTableColumns(), []);
  const setServiceTypes = useSchedulingStore((state) => state.setServiceTypes);
  const setLoading = useSchedulingStore((state) => state.setLoadingServiceTypes);

  return (
    <TableProvider
      storageKey="serviceTypesTable"
      storageKeyColumns="serviceTypesTableColumns"
      initialFilters={initialFilters}
      fetchData={async () => {
        try {
          setLoading(true);
          const response = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet();
          setServiceTypes(response.data);
        } catch (error) {
          console.error('Failed to fetch service types', error);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns()}
    >
      <ServiceTypeTable />
    </TableProvider>
  );
};
