import { ApiClient } from '@api/api-configuration';
import { ServiceType } from '@api/READ_ONLY/services_api/Api';
import { ServiceTypeForm } from '@app/features/serviceTypes/components/ServiceTypeForm/ServiceTypeForm';
import {
  getServiceTypesTableColumns,
  ServiceTypeRow,
} from '@app/features/serviceTypes/components/ServiceTypesTable/serviceTypesTableColumns.config';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { App as AntdApp, Modal } from 'antd';
import React, { useCallback } from 'react';

const ServiceTypesPage: React.FC = () => {
  const { modal, message } = AntdApp.useApp();
  const [reloadKey, setReloadKey] = React.useState(0);
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [editing, setEditing] = React.useState<ServiceTypeRow | undefined>(undefined);
  const fetchServiceTypes = async ({ limit, offset, query }: { limit: number; offset: number; query?: string }) => {
    const response = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet({
      limit,
      offset,
      query,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };
  const editNavigation = useCallback((serviceType: ServiceTypeRow) => {
    setEditing(serviceType);
    setIsFormOpen(true);
  }, []);

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'serviceTypes',
      urlParamConfig: { external: { searchTerm: 'search' } },
      fetchData: fetchServiceTypes,
      transformData: (serviceType: ServiceType) => ({ ...serviceType, id: serviceType.serviceTypeId }),
      columns: getServiceTypesTableColumns(editNavigation),
      searchPlaceholder: 'Search service types...',
      addButton: {
        text: 'Add Service Type',
        onClick: (param: unknown) => {
          editNavigation(param as ServiceTypeRow);
        },
      },
      actions: {
        onEdit: editNavigation,
        onDelete: async (row: ServiceTypeRow) => {
          modal.confirm({
            title: 'Delete service type',
            content: `Are you sure you want to delete ${row.name || 'this service type'}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            onOk: async () => {
              try {
                await ApiClient.serviceApi.serviceTypes.deleteServiceTypeServiceTypesServiceTypeIdDelete(
                  row.serviceTypeId
                );
                message.success('Service type deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete service type');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [editNavigation, modal, message]
  );

  return (
    <>
      <GenericDataTable key={reloadKey} config={tableConfig} suffixHoverInfo={<>Search by name</>} />
      <Modal
        title={editing ? 'Edit Service Type' : 'Create Service Type'}
        open={isFormOpen}
        onCancel={() => setIsFormOpen(false)}
        footer={null}
        destroyOnClose
      >
        <ServiceTypeForm
          data={editing}
          onSubmitted={() => {
            setIsFormOpen(false);
            setReloadKey((k) => k + 1);
          }}
        />
      </Modal>
    </>
  );
};

export default ServiceTypesPage;
