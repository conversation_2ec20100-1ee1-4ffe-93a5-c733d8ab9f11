import { ApiClient } from '@api/api-configuration';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { getVisitTableColumns, VisitRow } from '@feat-visits/components/VisitTable/visitTableColumns.config';
import { App as AntdApp } from 'antd';
import React from 'react';

const VisitsPage: React.FC = () => {
  const { modal, message } = AntdApp.useApp();
  const [reloadKey, setReloadKey] = React.useState(0);
  const fetchVisits = async ({ limit, offset, query }: { limit: number; offset: number; query?: string }) => {
    const response = await ApiClient.visitsApi.visits.searchVisitsVisitsSearchGet({
      limit,
      offset,
      query,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'visits',
      urlParamConfig: { external: { searchTerm: 'search' } },
      fetchData: fetchVisits,
      transformData: (visit: VisitPopulated) => ({ ...visit, id: visit.id }),
      columns: getVisitTableColumns(),
      searchPlaceholder: 'Search visits...',
      actions: {
        onDelete: async (row: VisitRow) => {
          modal.confirm({
            title: 'Delete visit',
            content: `Are you sure you want to delete visit #${row.id}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            onOk: async () => {
              try {
                await ApiClient.visitsApi.visits.deleteVisitVisitsVisitIdDelete(row.id as number);
                message.success('Visit deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete visit');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [modal, message]
  );

  return <GenericDataTable key={reloadKey} config={tableConfig} suffixHoverInfo={<>Search by client, address... </>} />;
};

export default VisitsPage;
