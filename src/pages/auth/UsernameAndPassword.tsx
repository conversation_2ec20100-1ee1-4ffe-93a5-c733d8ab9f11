import { EyeInvisibleOutlined, EyeOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { useAuthContext } from '@context/auth/useAuthContext';
import useNotifications from '@context/notifications/useNotificationContext';
import { Button, Input, Layout } from 'antd';
import { AxiosError } from 'axios';
import { Formik } from 'formik';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
// import { login } from '../../../../api-requests/Auth';

const UsernameAndPassword = () => {
  const [showPassword, setShowPassword] = useState(false);
  const { openNotification } = useNotifications();
  const { setAuthToken } = useAuthContext();
  const toggleShowPassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };
  const navigate = useNavigate();
  const handleLogin = async (values: { username: string; password: string }) => {
    console.log(values);

    try {
      // const response = await login(values);
      //! will hve to replace this with the actual API call
      // setAuthToken(response.Token);
      setAuthToken(
        'eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNyc2Etc2hhMjU2Iiwia2lkIjoiTG9vbS0xNzA2MDkxMDY3IiwidHlwIjoiSldUIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MNzAoSdfj03Jlmj8SJwhxZjelzph17BIOxjaI-GVuMb7HfRiq2pKpFwWMkmF0iOJjRAA5uGsnYjCrkZfUmqQNzvfUO2GwycJftoPWRed2e3NDshrdvShFoi57nXgHHbD-olmdAxBpgNFf6C8RrQ-esAEiR7tU1sjY658GQsAiv_kKprwWQUOT6nuC-DKBxL5TyZt_K83eY6201YfxTy5G4FnAWGxriUWRoR0I8sXePJyRjQTNEdH21y3KypYJVepE1f4xYnwzP2-vdNVq-8vrkYpkd7Ee-eCzV3Kj8CNtQeiGi_842iZ12zLFrl7oLjtt9_1IWjNkRtTSJn80o8Rug'
      );
      //For users logging in with username and password
      setTimeout(() => {
        navigate('/dashboard/availability');
      }, 100);
    } catch (err) {
      if (err instanceof AxiosError)
        openNotification('topRight', {
          title: `Sign in`,
          description: err?.response?.data.error,
          type: 'Danger',
        });
    }
  };
  return (
    <Layout style={{ background: 'transparent' }} className="w-full">
      <Formik
        initialValues={{ username: '', password: '' }}
        enableReinitialize
        onSubmit={async (values) => {
          handleLogin(values);
        }}
      >
        {({ values, setFieldValue, handleSubmit }) => (
          <form onSubmit={handleSubmit} className="">
            <div className={`flex flex-col justify-center gap-4`}>
              <div className="flex justify-center flex-col w-full gap-4">
                <div data-testid="leaveTypeDropdown" className="w-full flex gap-2 flex-col">
                  <Input
                    name="Name"
                    placeholder="Username"
                    prefix={<UserOutlined />}
                    value={values.username}
                    onChange={(e) => setFieldValue('username', e.target.value)}
                    // label={'Username'}
                    disabled={false}
                  />
                </div>
                <div data-testid="leaveTypeDropdown" className="w-full flex gap-2 flex-col">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter password"
                    prefix={<LockOutlined />}
                    value={values.password}
                    onChange={(e) => setFieldValue('password', e.target.value)}
                    // label={'Password'}
                    disabled={false}
                    suffix={
                      <Button
                        size="small"
                        type="text"
                        icon={showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                        onClick={toggleShowPassword}
                        style={{ border: 'none', background: 'transparent' }}
                      />
                    }
                  />
                </div>
              </div>
              <Button type="primary" size={'large'} onClick={() => handleLogin(values)}>
                Sign in
              </Button>
            </div>
          </form>
        )}
      </Formik>
    </Layout>
  );
};

export default UsernameAndPassword;
