/* eslint-disable @typescript-eslint/no-explicit-any */
import { PatientSearch } from '@feat-service-requests/components/PatientSearch/PatientSearch';
import ServiceRequestForm from '@feat-service-requests/ServiceRequestForm';
import { Button } from 'antd';
import { useState } from 'react';
import HeaderLayoutCopy from './layoutsCopy/HeaderLayoutCopy';

// const submitFormById = (formId: string) => {
//   const form = document.getElementById(formId);

//   form?.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
// };

const SERVICE_REQUEST_FORM_ID = 'serviceRequestForm';

const onSubmitServiceRequestForm = (patientId: string, data: any) => {
  // When i submit i get the results below.

  // Question ? why therapies are not an array with the therapy id ? - do not see this information gets displayed after the submission.

  // Does BE needs this info to be provided? Just the ids should be enough. so we wont pass so much data into the network

  // {
  //     "isRecurring": false,
  //     "therapies": [
  //         {
  //             "serviceId": 28,
  //             "name": "Bathing Assistance",
  //             "description": "Support with hygiene and bathing",
  //             "serviceTypeId": 2,
  //             "estimatedTimeMinutes": 45,
  //             "costInEuros": 20,
  //             "createdAt": "2025-08-04T13:38:25.306727Z",
  //             "updatedAt": "2025-08-04T13:38:25.306727Z"
  //         }
  //     ],
  //     "caregivers": [],
  //     "visits": []
  // }

  console.log('data', data);
};

const ServiceRequest = () => {
  const [selectedPatientId, setSelectedPatientId] = useState<string>('');

  console.log('selectedPatientId', selectedPatientId);

  // const setCaregivers = useSchedulingStore((state) => state.setCaregivers);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet({ offset: 0, limit: 100 });
  //     setCaregivers(response.data);
  //   };
  //   fetchData();
  // }, []);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any

  return (
    <HeaderLayoutCopy title={'Service Request'}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          // overflowY: 'scroll',
          // border: '3px solid red',
        }}
      >
        {/* Basically the page content The Service form on the left and the patient History on the right */}
        <div
          style={{
            // border: '1px solid red',
            height: '100%',
            width: '100%',
            // border: '1px solid red',
            overflowY: 'scroll',
            padding: '24px',
            // display: 'flex',
            // gap: '24px',
            display: 'grid',
            gridTemplateColumns: '1fr 480px',
            gridGap: '24px',
          }}
        >
          {/* I want to give the "Illusion" that this is the same form to the user thats why i wrap it. Each parent to be valid must always container at least 2 children - like 99% of the time */}
          <div
            style={{
              // maxWidth: '900px',
              width: '100%',
              // border: '1px solid red',
              // margin: '0 auto',
            }}
          >
            {/* As a dropdown i would expect to always return the same thing */}
            {/* I cannot clear the value from the drop down :s */}
            <PatientSearch
              // disabled={false}
              value={selectedPatientId}
              /* What is the setValue? Is the onChange from the select?  Also i was expecting the patient Id to be returned. But i got a whole patient object. */
              setValue={(e) => {
                // Typescript complaints that e might be undefined and typescript is not sure what it gets returned i have to typecheck.

                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                if ((e as any)?.clientId) {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  setSelectedPatientId((e as any).clientId);
                }
              }}
            />

            {/* To avoid mistakes i want the form to get cleared when the patient changes - I enforce the user to be careful with his actions. Otherwise i can keep the state so i will tell react to re-render */}
            {/* I prefer this to be honest cause the user is responsible - If we believe we add extra time that might confuse or dissatisfy the user experience can easily be removed */}
            <ServiceRequestForm
              key={selectedPatientId}
              isDisabled={true}
              formId={SERVICE_REQUEST_FORM_ID}
              onSubmit={(values) => onSubmitServiceRequestForm(selectedPatientId, values)}
            />
          </div>

          {/* Right: Service History - A simply component that we will pass the patient Id and it will fetch the data - Completely isolated as ui and everything. - You can add skeleton make it beautiful - even if its slow the rest of the ui will be responsive */}
          {/* The reason i do this is because we separate the concerns - In any case for some reason we cannot see the history - we can still create a service request. And maybe the patients history will be visible from the management */}
          <div
            // className="w-[460px] ml-8 hidden md:block"
            style={
              {
                // border: '1px solid red',
              }
            }
          >
            <div className="bg-white shadow rounded-lg p-4 mb-4">
              <h3 className="mb-4  font-semibold text-lg border-b pb-2">Service History</h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {[...Array(7)].map((_) => (
                  <div key={_} className="flex items-center gap-3">
                    <span className="w-3 h-3 rounded-full bg-blue-400 inline-block flex-shrink-0" />
                    <div className="flex flex-col">
                      <span className="text-sm font-medium ">Jan 12, 2024 21:20</span>
                      <span className="text-xs">Wound Care</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Will be used to add a new service request - Its responsible to collect the data to make a request to the backend. The patient id will be passed from Here the Parent component and the rest of the info from the form */}
        {/* <AddServiceRequestPanel /> */}

        {/* Footer fixed at bottom */}
        {/* Submit the Form by id - No need to belong to the form to submit it */}
        {/* We should not have parent to parent html - most likely if 2 parents are existing the one can be removed and still achieve the same design - Less html on the page :p */}
        {/* <div className="max-w-[1100px] mx-auto flex justify-end gap-4 border border-gray-200"> */}
        {/* </div> */}

        <div
          // className="w-full bg-white shadow-inner sticky bottom-0 p-4 flex justify-end"

          // For something to be sticky it needs to have a parent with a height and overflow - does not cover or case
          className="w-full bg-white shadow-inner"
          style={{
            minHeight: 64,
            maxHeight: 64,
            padding: '0 64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          {/* 1st solution add a tool tip above to explain why the button is disabled - We force the user to select a patient id and full full the form */}
          {/* 2nd solution we leave the button enabled for submission but we do proper form error handling - Meaning no jumps cause errors appeared - Have a form Element that is responsible maybe for the input errors and label*/}
          <Button
            type="primary"
            htmlType="submit"
            form={SERVICE_REQUEST_FORM_ID}
            disabled={!selectedPatientId}
            // onClick={() => {
            //   console.log('Submit form by id');
            // }}
          >
            Create
          </Button>
        </div>
      </div>
    </HeaderLayoutCopy>
  );
};

export default ServiceRequest;
