import { LockOutlined } from '@ant-design/icons';
import AppAvatar from '@app/components/ui/Avatar';
import { useAuthContext } from '@context/auth/useAuthContext';
import { Button, Divider, Dropdown, MenuProps, Space, theme, Typography } from 'antd';
import React, { memo } from 'react';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  path?: string,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    path,
  } as MenuItem;
}

const UserDropdownCopy = () => {
  const { user } = useAuthContext();
  // const navigate = useNavigate();
  const {
    token: { colorBgElevated, borderRadiusLG, boxShadowSecondary },
  } = theme.useToken();
  const contentStyle: React.CSSProperties = {
    backgroundColor: colorBgElevated,
    borderRadius: borderRadiusLG,
    boxShadow: boxShadowSecondary,
  };

  const items: MenuProps['items'] = [
    getItem(
      <div
        className="flex gap-2 items-center justify-start py-1"
        // onClick={() => navigate(`/${APP_PREFIX}/${EDIT_PROFILE_URL}/` + user.personId)}
      >
        <LockOutlined />
        <Typography>{'Change Password'}</Typography>
      </div>,
      '1',
      ''
    ),
  ];

  const menuStyle: React.CSSProperties = {
    boxShadow: 'none',
  };
  return (
    <Dropdown
      menu={{ items }}
      trigger={['click']}
      popupRender={(menu) => (
        <div style={contentStyle}>
          <div className="px-4 py-3 text-left">
            <Typography className="cursor-default font-semibold">{user.fullname}</Typography>
            <div className=" text-left  ">
              <Typography className="cursor-default font-semibold text-gray-500">{user.name}</Typography>
            </div>
          </div>
          <Divider style={{ margin: 0 }} />
          {React.cloneElement(menu as React.ReactElement, {
            style: menuStyle,
          })}
          <Divider style={{ margin: 0 }} />
          <div className="mt-2 text-center">{/* <AppVersion theme={'dark'} /> */}</div>
          <Space
            style={{
              padding: 8,
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Button
              type="default"
              onClick={() => {
                //TODO: imlement logout functionality
              }}
            >
              Sign out
            </Button>
          </Space>
        </div>
      )}
    >
      <a onClick={(e) => e.preventDefault()}>
        <Space>
          <AppAvatar
            isSelected={true}
            personId={user.userId}
            fullName={`${user.fullname}`}
            doubleLetters={[user.fullname[0], user.fullname[1]]}
          />
        </Space>
      </a>
    </Dropdown>
  );
};

export default memo(UserDropdownCopy);
