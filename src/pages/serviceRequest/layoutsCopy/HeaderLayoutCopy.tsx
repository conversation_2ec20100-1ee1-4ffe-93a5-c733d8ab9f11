import useUiContext from '@context/ui/useUiContext';
import { Button } from 'antd';
import React, { memo, ReactNode } from 'react';
import { Helmet } from 'react-helmet';
import { FiSidebar } from 'react-icons/fi';
import HeaderDropdownCopy from './HeaderDropdownCopy';
import UserDropdownCopy from './UserDropdownCopy';

type Props = {
  children: ReactNode;
  title: ReactNode;
  // heightDifference?: string;
};
const isString = (node: ReactNode): node is string => {
  return typeof node === 'string';
};

const isReactElement = (node: ReactNode): node is React.ReactElement => {
  return React.isValidElement(node);
};

// I was expecting the HeaderLayout to handle just the header for the app layout. But it accepts the children as well for the page. Is that the main Layout?

// The whole layout Rerenders when i change route. I can see it in the browser cause it flashes most likely every single page component gets wrapper with the HeaderLayout again and again.

const HeaderLayoutCopy = ({ children, title }: Props) => {
  const { toggleSidebarCollapse } = useUiContext();

  // This should not be the scroll - we should use a good component from the mui
  const customStyle: React.CSSProperties = {
    // This is not correct but for some reason it works in the design :)
    height: '100vh',
    // border: '3px solid blue',
    overflow: 'hidden',
    // overflowY: 'scroll',
  };

  // if (heightDifference) {
  //   customStyle['height'] = `calc(100vh - ${heightDifference || '4rem'})`;
  // }

  return (
    <>
      {/* Do we really need to set the title? */}
      <Helmet>
        <title> {isString(title) ? title : isReactElement(title) ? 'LOOM' : null}</title>
      </Helmet>

      <div className="flex justify-between items-center px-0 py-2 bg-white border-b border-slate-100">
        <div className="flex gap-4 items-center">
          <div className="xs:hidden block" onClick={() => toggleSidebarCollapse()}>
            <Button
              className="text-xl bg-white leading-none"
              type="link"
              icon={<FiSidebar className="text-lg" />}
            ></Button>
          </div>
          <div className="text-lg text-slate-600">{title}</div>
        </div>

        <div className="flex gap-4">
          <HeaderDropdownCopy />

          <UserDropdownCopy />
        </div>
      </div>

      <div style={customStyle}>{children}</div>
    </>
  );
};

export default memo(HeaderLayoutCopy);
