import { APP_PREFIX, AVAILABILITY_PAGE, CLIENT_PAGE, NEW_CLIENT, SCHEDULING_PAGE, SHIFTS_PAGE } from '@app/routes/urls';
import { useAuthContext } from '@context/auth/useAuthContext';
import { Button, Dropdown, theme, Typography } from 'antd';

import { FC, useState } from 'react';
import { CgMenuGridO } from 'react-icons/cg';
import { FaPlus } from 'react-icons/fa';
import { Fc<PERSON>usinessman, FcConferenceCall, FcDataSheet, FcGlobe } from 'react-icons/fc';
import { Link } from 'react-router-dom';

const HeaderDropdownCopy: FC = () => {
  // const [isCreateModalOpen, setCreateModalOpen] = useState(false);
  const [open, setOpen] = useState(false); // new state
  const { allowIf } = useAuthContext();
  // const { showFeature } = useFeatureContext();

  const {
    token: { colorBgElevated, borderRadiusLG, boxShadowSecondary },
  } = theme.useToken();

  const contentStyle: React.CSSProperties = {
    backgroundColor: colorBgElevated,
    borderRadius: borderRadiusLG,
    boxShadow: boxShadowSecondary,
  };

  // const handleLeaveClick = () => {
  //   setCreateModalOpen(true);
  //   setOpen(false);
  // };

  const menuItems = [
    allowIf(['Timesheets_ReadWrite']) && {
      key: 'availability',
      label: 'Availability',
      link: `/${APP_PREFIX}/${AVAILABILITY_PAGE}`,
      icon: <FcDataSheet />,
    },
    // showFeature('SHEDULING') &&
    //   (allowIf(['LeaveHistory_View']) || allowIf(['Meetings_Read'])) && {
    //     key: 'scheduling',
    //     label: 'scheduling',
    //     onClick: handleLeaveClick,
    //     icon: <FcLeave />,
    //   },
    {
      key: 'client',
      label: 'Client',
      link: `/${APP_PREFIX}/${CLIENT_PAGE}/${NEW_CLIENT}`,
      icon: <FcBusinessman />,
    },
    // {
    //   key: 'doctor',
    //   label: 'Doctor',
    //   link: `/${APP_PREFIX}/${DOCTOR_PAGE}/${NEW_DOCTOR}`,
    //   icon: <FcBusinessman />,
    // },

    (allowIf(['Meetings_Read']) || allowIf(['Trips_EditAll'])) && {
      key: 'scheduling',
      label: 'Scheduling',
      link: `/${APP_PREFIX}/${SCHEDULING_PAGE}`,
      icon: <FcConferenceCall />,
    },

    (allowIf(['Meetings_Read']) || allowIf(['Trips_EditAll'])) && {
      key: 'add_trip',
      icon: <FcGlobe />,
      label: 'Trip',
      link: `/${APP_PREFIX}/${SHIFTS_PAGE}`,
    },
  ].filter(Boolean);

  return (
    <>
      <Dropdown
        open={open}
        onOpenChange={setOpen}
        popupRender={() => (
          <div style={contentStyle} className="grid grid-cols-2 p-2 gap-2 text-center">
            {menuItems.map(
              (item) =>
                item && (
                  <div
                    key={item.key}
                    className="flex flex-col items-center justify-center hover:bg-neutral-50 rounded-lg p-2 group cursor-pointer duration-300"
                  >
                    {item.link ? (
                      <Link
                        to={item.link}
                        onClick={() => setOpen(false)}
                        className="overflow-hidden relative border border-solid border-neutral-100 text-2xl bg-white w-12 h-12 aspect-square group-hover:text-primary text-appGrey flex flex-col items-center justify-center text-center p-2 rounded-lg duration-300"
                      >
                        <div className="top-0 absolute right-0 p-1.5 bg-slate-200 aspect-square rounded-bl-lg flex justify-center items-center group-hover:bg-primary duration-300">
                          <FaPlus className="text-slate-600 text-[0.5rem] group-hover:text-white duration-300" />
                        </div>
                        {item.icon}
                      </Link>
                    ) : (
                      <div
                        //! onClick={item.onClick}

                        className="overflow-hidden relative border border-solid border-neutral-100 text-2xl bg-white w-12 h-12 aspect-square group-hover:text-primary text-appGrey flex flex-col items-center justify-center text-center p-2 rounded-lg duration-300"
                      >
                        <div className="top-0 absolute right-0 p-1.5 bg-slate-200 aspect-square rounded-bl-lg flex justify-center items-center group-hover:bg-primary duration-300">
                          <FaPlus className="text-slate-600 text-[0.5rem] group-hover:text-white duration-300" />
                        </div>
                        {item.icon}
                      </div>
                    )}
                    <Typography.Text className="text-center mt-1">{item.label}</Typography.Text>
                  </div>
                )
            )}
          </div>
        )}
        trigger={['click']}
        placement="bottomRight"
        className="group"
      >
        <Button
          type="text"
          icon={<CgMenuGridO className="text-2xl !text-appGrey group-hover:text-primary duration-300" />}
        />
      </Dropdown>
    </>
  );
};

export default HeaderDropdownCopy;
