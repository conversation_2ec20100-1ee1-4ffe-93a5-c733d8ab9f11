import { ApiClient } from '@api/api-configuration';
import { Doctor, DoctorCreate } from '@api/READ_ONLY/doctors_api/Api';
import { DoctorForm } from '@app/features/doctors/components/DoctorForm/DoctorForm';
import { APP_PREFIX, DOCTOR_PAGE, EDIT_DOCTOR } from '@app/routes/urls';
import useNotifications from '@context/notifications/useNotificationContext';
import { useNavigate } from 'react-router-dom';

export const CreateDoctor = () => {
  const navigate = useNavigate();
  const { openNotification } = useNotifications();

  const onSubmit = async (formData: Doctor | DoctorCreate) => {
    try {
      const createdDoc = await ApiClient.doctorsApi.doctors.addNewDoctorDoctorsPost(formData as DoctorCreate);

      openNotification('topRight', {
        title: `Doctor`,
        description: 'Doctor created successfully.',
        type: 'Success',
      });
      const path = `/${APP_PREFIX}/${DOCTOR_PAGE}/${EDIT_DOCTOR.replace(':id', String(createdDoc.data.doctorId))}`;
      navigate(path);
    } catch (error: unknown) {
      openNotification('topRight', {
        title: `Doctor`,
        description: 'Doctor creation failed. ',
        type: 'Warning',
      });
    }
  };
  return <DoctorForm onSubmit={onSubmit} />;
};
