import { ApiClient } from '@api/api-configuration';
import { Doctor, DoctorCreate } from '@api/READ_ONLY/doctors_api/Api';
import { <PERSON>Form } from '@app/features/doctors/components/DoctorForm/DoctorForm';
import { APP_PREFIX, DOCTOR_PAGE, EDIT_DOCTOR } from '@app/routes/urls';
import useNotifications from '@context/notifications/useNotificationContext';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

export const EditDoctor = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /doctors/:id/edit
  const [data, setData] = useState<Doctor>();
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDoctor = async () => {
      try {
        if (id) {
          const response = await ApiClient.doctorsApi.doctors.getSingleDoctorDoctorsDoctorIdGet(Number(id));
          setData(response.data);
        }
      } catch (err) {
        openNotification('topRight', {
          title: `Doctor`,
          description: 'Failed to fetch doctor',
          type: 'Warning',
        });
      }
    };
    console.log('state', state);

    fetchDoctor();
  }, [id, openNotification, state]);

  const onSubmit = async (formData: Doctor | DoctorCreate) => {
    try {
      await ApiClient.doctorsApi.doctors.updateExistingDoctorDoctorsDoctorIdPut(Number(id), formData as Doctor);

      openNotification('topRight', {
        title: `Doctor`,
        description: 'Doctor updated successfully.',
        type: 'Success',
      });
      const path = `/${APP_PREFIX}/${DOCTOR_PAGE}/${EDIT_DOCTOR.replace(':id', String(id))}`;
      navigate(path);
    } catch (error: unknown) {
      openNotification('topRight', {
        title: `Doctor`,
        description: 'Doctor updated failed. ',
        type: 'Warning',
      });
    }
  };

  const doctorData = state || data;

  if (!doctorData) return <div>Loading...</div>;
  return <DoctorForm data={data} onSubmit={onSubmit} />;
};
