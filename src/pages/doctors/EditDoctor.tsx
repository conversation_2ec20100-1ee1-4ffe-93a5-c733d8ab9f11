import getDoctorsQueryParams from '@app/api/doctor/doctor';
import { useQuery } from '@tanstack/react-query';
import { useLocation, useParams } from 'react-router-dom';
import DoctorForm from './CreateDoctor';

export const EditDoctor = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit

  const { isPending, data } = useQuery(getDoctorsQueryParams(state, id ? id : ''));
  const clientData = state || data;

  if (!clientData && isPending) return <div>Loading...</div>;
  if (!clientData) return <div>Error loading client</div>;
  return <DoctorForm data={clientData} />;
};
