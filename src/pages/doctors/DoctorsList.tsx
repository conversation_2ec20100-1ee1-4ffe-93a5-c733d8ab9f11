import { ApiClient } from '@api/api-configuration';
import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import {
  DoctorRow,
  getDoctorTableColumns,
} from '@app/features/doctors/components/DoctorTable/doctorTableColumns.config';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, DOCTOR_PAGE, EDIT_DOCTOR, NEW_DOCTOR } from '@app/routes/urls';
import { App as AntdApp } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const DoctorsList: React.FC = () => {
  const navigate = useNavigate();
  const { modal, message } = AntdApp.useApp();
  const [reloadKey, setReloadKey] = React.useState(0);

  const editNavigation = React.useCallback(
    (doctorId: number) => {
      const path = `/${APP_PREFIX}/${DOCTOR_PAGE}/${EDIT_DOCTOR.replace(':id', String(doctorId))}`;
      navigate(path);
    },
    [navigate]
  );

  const fetchDoctors = async ({ limit, offset, query }: { limit: number; offset: number; query?: string }) => {
    const response = await ApiClient.doctorsApi.doctors.getAllDoctorsListDoctorsGet({
      limit,
      offset,
      query,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'doctors',
      urlParamConfig: { external: { searchTerm: 'search' } },
      fetchData: fetchDoctors,
      transformData: (doctor: Doctor) => ({ ...doctor, id: doctor.doctorId }),
      columns: getDoctorTableColumns(editNavigation),
      searchPlaceholder: 'Search doctors...',
      addButton: {
        text: 'Add Doctor',
        onClick: () => {
          const path = `/${APP_PREFIX}/${DOCTOR_PAGE}/${NEW_DOCTOR}`;
          navigate(path);
        },
      },
      actions: {
        onEdit: (row: DoctorRow) => {
          editNavigation(row.doctorId);
        },
        onDelete: async (row: DoctorRow) => {
          modal.confirm({
            title: 'Delete doctor',
            content: `Are you sure you want to delete ${row.firstName || ''} ${row.lastName || ''}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            onOk: async () => {
              try {
                await ApiClient.doctorsApi.doctors.removeDoctorDoctorsDoctorIdDelete(row.doctorId);
                message.success('Doctor deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete doctor');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [editNavigation, navigate, modal, message]
  );

  return <GenericDataTable key={reloadKey} config={tableConfig} suffixHoverInfo={<>Search by name, email, phone</>} />;
};

export default DoctorsList;
