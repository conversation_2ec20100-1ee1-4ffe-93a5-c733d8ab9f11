import DoctorTable from '@app/features/doctors/components/DoctorTable/DoctorTable';
import { getDoctorTableColumns } from '@app/features/doctors/components/DoctorTable/doctorTableColumns.config';
import { Caregiver } from '@app/types/caregiver.types';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: '',
};
export const DoctorsList = () => {
  const columns: () => ExtendedTableColumnType<Caregiver>[] = useCallback(() => getDoctorTableColumns(), []);
  console.log('COLUMNS', columns);
  return (
    <TableProvider
      storageKey="doctorTable"
      storageKeyColumns="doctorTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={[]}
      fetchData={() => {}}
      cols={columns()}
    >
      <DoctorTable />
    </TableProvider>
  );
};
