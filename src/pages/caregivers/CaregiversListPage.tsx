import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { GenericDataTable } from '@app/features/table/components/GenericDataTable';
import { APP_PREFIX, CAREGIVERS_PREFIX, EDIT_CAREGIVER, NEW_CAREGIVER } from '@app/routes/urls';
import {
  CaregiverRow,
  getCaregiverTableColumns,
} from '@feat-caregivers/components/CaregiverTable/caregiverTableColumns.config';
import { App as AntdApp } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const CAREGIVER_URL_PARAM_CONFIG = {
  external: {
    searchTerm: 'search',
  },
} as const;

const CaregiversListPage: React.FC = () => {
  const navigate = useNavigate();
  const { modal, message } = AntdApp.useApp();
  const [reloadKey, setReloadKey] = React.useState(0);

  const editNavigation = React.useCallback(
    (caregiverId: number) => {
      const path = `/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${EDIT_CAREGIVER.replace(':id', String(caregiverId))}`;
      navigate(path);
    },
    [navigate]
  );

  const transformCaregiverToRow = (caregiver: Caregiver): CaregiverRow => ({
    ...caregiver,
    id: caregiver.caregiverId,
  });

  const fetchCaregivers = async (params: { limit: number; offset: number; query?: string }) => {
    const searchQuery = { ...(params.query ? { query: params.query } : {}) };
    const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet({
      ...searchQuery,
      limit: params.limit,
      offset: params.offset,
    });
    return {
      data: response.data.data,
      total: response.data.total || response.data.data.length,
    };
  };

  const tableConfig = React.useMemo(
    () => ({
      tableId: 'caregivers',
      urlParamConfig: CAREGIVER_URL_PARAM_CONFIG,
      fetchData: fetchCaregivers,
      transformData: transformCaregiverToRow,
      columns: getCaregiverTableColumns(editNavigation),
      searchPlaceholder: 'Search caregivers...',
      addButton: {
        text: 'Add Caregiver',
        onClick: () => navigate(`/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${NEW_CAREGIVER}`),
      },
      actions: {
        onEdit: (row: CaregiverRow) => {
          editNavigation(row.caregiverId);
        },
        onDelete: async (row: CaregiverRow) => {
          modal.confirm({
            title: 'Delete caregiver',
            content: `Are you sure you want to delete ${row.firstName || ''} ${row.lastName || ''}?`,
            okText: 'Delete',
            okButtonProps: { danger: true },
            onOk: async () => {
              try {
                await ApiClient.caregiverApi.caregivers.deleteCaregiverCaregiversCaregiverIdDelete(row.caregiverId);
                message.success('Caregiver deleted');
                setReloadKey((k) => k + 1);
              } catch (e) {
                message.error('Failed to delete caregiver');
              }
            },
          });
        },
        column: { header: 'Actions', sticky: 'right' as const, align: 'center' as const, width: 56 },
      },
      searchConfig: {
        minSearchLength: 3,
        debounceMs: 300,
      },
    }),
    [navigate, modal, message, editNavigation]
  );

  return (
    <GenericDataTable
      key={reloadKey}
      config={tableConfig}
      suffixHoverInfo={<>Search by firstname, lastname, phone, email, service</>}
    />
  );
};

export default CaregiversListPage;
