import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { APP_PREFIX, CAREGIVERS_LIST, CAREGIVERS_PREFIX } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import { omit } from '@app/utils/omitKeys';
import { transformList } from '@app/utils/transformToList';
import useNotifications from '@context/notifications/useNotificationContext';
import { CaregiverFormInner } from '@feat-caregivers/components/CaregiverForm/CaregiverForm';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

export const EditCaregiver = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit
  const { openNotification } = useNotifications();
  const [data, setData] = useState<Caregiver>();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCaregiver = async () => {
      try {
        const response = await ApiClient.caregiverApi.caregivers.getCaregiverCaregiversCaregiverIdGet(Number(id));
        setData(response.data);
      } catch (err) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Failed to fetch caregiver',
          type: 'Warning',
        });
      }
    };
    console.log('state', state);
    if (!state) {
      fetchCaregiver();
    }
  }, []);
  const clientData = state || data;

  if (!clientData) return <div>Loading...</div>;
  if (!clientData) return <div>Error loading client</div>;
  const onSubmit = async (formData: Caregiver) => {
    try {
      console.log('Form data:', formData);
      const allowedData = omit(formData, ['coverageAreas', 'caregiverId', 'createdAt', 'updatedAt']);

      const transformedData = {
        ...allowedData,
        certifications: transformList(formData.certifications),
        skills: transformList(formData.skills),
        specialties: transformList(formData.specialties),
        languagesSpoken: transformList(formData.languagesSpoken),
      };

      await ApiClient.caregiverApi.caregivers.updateCaregiverCaregiversCaregiverIdPut(Number(id), transformedData);
      openNotification('topRight', {
        title: `Caregiver`,
        description: 'Caregiver updated successfully.',
        type: 'Success',
      });
      navigate(`/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${CAREGIVERS_LIST}`);
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver update failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };
  return <CaregiverFormInner data={clientData} externalOnSubmit={onSubmit} />;
};
