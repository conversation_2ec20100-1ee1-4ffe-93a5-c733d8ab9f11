import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import CaregiverTable from '@feat-caregivers/components/CaregiverTable/CaregiverTable';
import { getCaregiverTableColumns } from '@feat-caregivers/components/CaregiverTable/caregiverTableColumns.config';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: '',
};

export const CaregiversList = () => {
  const columns: () => ExtendedTableColumnType<Caregiver>[] = useCallback(() => getCaregiverTableColumns(), []);

  const setCaregivers = useSchedulingStore((state) => state.setCaregivers);
  const setLoading = useSchedulingStore((state) => state.setLoadingCaregivers);

  return (
    <TableProvider
      storageKey="caregiverTable"
      storageKeyColumns="caregiverTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={[]}
      fetchData={async () => {
        try {
          setLoading(true);
          const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet();
          setCaregivers(response.data);
        } catch (error) {
          console.error('Error fetching caregivers:', error);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns()}
    >
      <CaregiverTable />
    </TableProvider>
  );
};
