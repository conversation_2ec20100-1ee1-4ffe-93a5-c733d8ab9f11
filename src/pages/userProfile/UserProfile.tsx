import { CheckCircleOutlined } from '@ant-design/icons';
import useNotifications from '@context/notifications/useNotificationContext';
import { Button, Modal, Typography } from 'antd';
import { AxiosError } from 'axios';
import { Formik, FormikProps } from 'formik';
import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ChangePasswordForm from './ChangePasswordForm';
import { yupValidationPassword } from './passwordValidation';

const { Text } = Typography;

export type TChangePwd = {
  old_password: string;
  new_password: string;
  confirm_password: string;
};

const UserProfile = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const formRef = useRef<FormikProps<TChangePwd> | null | undefined>();
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const submitStep = async (values: TChangePwd) => {
    console.log('values change pwd', values);
    try {
      setIsLoading(true);
      // await changePwd({
      //   old_password: values.old_password,
      //   new_password: values.new_password,
      // });
      setShowModal(true);
      formRef.current?.resetForm();
    } catch (err) {
      if (err instanceof AxiosError) {
        openNotification('topRight', {
          title: `Change Password`,
          description: err?.response?.data.error,
          type: 'Danger',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="px-0 py-2 md:px-8 md:py-10 2xl:py-10 2xl:px-44"
      style={{
        width: '100%',
        flex: 'flex',
        justifyContent: 'center',
      }}
    >
      <Modal
        width={600}
        open={showModal}
        onCancel={() => {
          formRef.current?.resetForm();
          setShowModal(false);
          navigate('/');
        }}
        title={'Password Changed'}
        footer={[
          <Button
            key="confirm_action"
            type="primary"
            onClick={() => {
              formRef.current?.resetForm();
              setShowModal(false);
              navigate('/');
            }}
          >
            <span className="indicator-label">OK</span>
          </Button>,
        ]}
      >
        <div className="flex flex-col items-center py-8 px-4">
          <CheckCircleOutlined className="text-green-500 text-6xl mb-6" />
          <Text className="text-gray-900 text-2xl font-medium mb-4">Password Changed!</Text>
          <Text className="text-gray-800 font-light">Your password has been successfully updated.</Text>
          <Text className="text-gray-800 font-light">Please log in again to continue.</Text>
        </div>
      </Modal>
      <Formik
        innerRef={(formikProps) => {
          if (formikProps) {
            formRef.current = formikProps;
          }
        }}
        validationSchema={yupValidationPassword}
        initialValues={{
          old_password: '',
          new_password: '',
          confirm_password: '',
        }}
        enableReinitialize
        onSubmit={async (values) => {
          await submitStep(values);
        }}
        validateOnBlur={true}
      >
        {({ values, setFieldValue, handleSubmit }) => {
          return (
            <form onSubmit={handleSubmit} id="changePwdForm">
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  flexDirection: 'column',
                }}
                className="mt-4 h-full gap-4"
              >
                {/* <FormCard title="Change Password" icon={<LockOutlined />} collapsable={false}> */}
                <ChangePasswordForm values={values} setFieldValue={setFieldValue} />
                {/* </FormCard> */}
              </div>
              <div className="flex items-center justify-end mt-2">
                {isLoading ? (
                  // <Loader spin={true} />
                  <>Loading</>
                ) : (
                  <Button
                    data-testid="changePwd"
                    size={'large'}
                    style={{
                      // background: AppColors.primary,
                      color: 'white',
                    }}
                    // onClick={() => {
                    //   console.log('Submit');
                    // }}
                    htmlType="submit"
                    form={'changePwdForm'}
                  >
                    <span className="indicator-label">Change Password</span>
                  </Button>
                )}
              </div>
            </form>
          );
        }}
      </Formik>
    </div>
  );
};

export default UserProfile;
