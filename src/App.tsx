import ErrorBoundary from '@app/components/ui/ErrorBoundary';
import FeatureProvider from '@context/features/FeaturesProvider';
import { MessageProvider } from '@context/message';
import NotificationProvider from '@context/notifications/NotificationProvider';
import UiProvider from '@context/ui/UiProvider';
import UseAxiosInitSetup from '@lib/useAxiosInitSetup';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, App as AntdApp } from 'antd';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { appTheme } from './styles/app-theme';

const queryClient = new QueryClient();

const App = () => {
  UseAxiosInitSetup();
  // const { styles } = useStyle();

  return (
    <Suspense>
      <ErrorBoundary>
        <ConfigProvider
          theme={appTheme}

          // button={{ className: styles.linearGradientButton }}
        >
          <AntdApp>
            <QueryClientProvider client={queryClient}>
              <MessageProvider>
                {/* <UrlParamProvider> */}
                <NotificationProvider>
                  <FeatureProvider>
                    <UiProvider>
                      <Outlet />
                    </UiProvider>
                  </FeatureProvider>
                </NotificationProvider>
                {/* </UrlParamProvider> */}
              </MessageProvider>
            </QueryClientProvider>
          </AntdApp>
        </ConfigProvider>
      </ErrorBoundary>
    </Suspense>
  );
};

export { App };
