import FeatureProvider from '@context/features/FeaturesProvider';
import NotificationProvider from '@context/notifications/NotificationProvider';
import UiProvider from '@context/ui/UiProvider';
import { UrlParamProvider } from '@context/urlParams/UrlParamProvider';
import UseAxiosInitSetup from '@lib/useAxiosInitSetup';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from 'antd';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { appTheme } from './styles/app-theme';

const queryClient = new QueryClient();

const App = () => {
  UseAxiosInitSetup();
  // const { styles } = useStyle();

  return (
    <Suspense>
      <ConfigProvider
        theme={appTheme}

        // button={{ className: styles.linearGradientButton }}
      >
        <QueryClientProvider client={queryClient}>
          <UrlParamProvider>
            <NotificationProvider>
              <FeatureProvider>
                <NotificationProvider>
                  <UiProvider>
                    <Outlet />
                  </UiProvider>
                </NotificationProvider>
              </FeatureProvider>
            </NotificationProvider>
          </UrlParamProvider>
        </QueryClientProvider>
      </ConfigProvider>
    </Suspense>
  );
};

export { App };
