import { Caregiver } from '@app/types/caregiver.types';
import { Gender } from '@app/types/common.types';

export const doctors: Caregiver[] = [
  {
    id: 'doc-001',
    type: 'Doctor',
    firstName: '<PERSON>',
    lastName: 'Doe',
    fullName: '<PERSON>',
    dateOfBirth: new Date('1980-05-12'),
    username: 'johndo<PERSON>',
    passwordHash: 'hashedpassword1',
    gender: 'Male' as Gender,
    nationalId: 'A123456789',
    phone: '+**********',
    email: '<EMAIL>',
    baseAddress: {
      street: '123 Main St',
      city: 'New York',
    },
    coverageAreas: ['Manhattan', 'Brooklyn'],
    travelRadius: 15,
    certifications: ['Board Certified in Internal Medicine'],
    skills: ['Diagnosing', 'Patient Communication'],
    specialties: ['Cardiology'],
    availability: {
      Monday: { start: '09:00', end: '17:00' },
      Tuesday: { start: '09:00', end: '17:00' },
      Wednesday: { start: '09:00', end: '17:00' },
      Thursday: null,
      Friday: { start: '10:00', end: '16:00' },
      Saturday: null,
      Sunday: null,
    },
    languagesSpoken: ['English', 'Spanish'],
    rating: 4.8,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    services: [],
  },
  {
    id: 'doc-002',
    type: 'Doctor',
    firstName: 'Emily',
    lastName: 'Nguyen',
    fullName: 'Emily Nguyen',
    dateOfBirth: new Date('1975-08-22'),
    username: 'emilynguyen',
    passwordHash: 'hashedpassword2',
    gender: 'Female' as Gender,
    nationalId: 'B987654321',
    phone: '+**********',
    email: '<EMAIL>',
    baseAddress: {
      street: '456 Oak St',
      city: 'San Francisco',
    },
    coverageAreas: ['San Francisco', 'Oakland'],
    travelRadius: 20,
    certifications: ['Pediatrics Residency', 'Neonatal Care'],
    skills: ['Pediatric Care', 'Emergency Response'],
    specialties: ['Pediatrics'],
    availability: {
      Monday: null,
      Tuesday: { start: '08:00', end: '14:00' },
      Wednesday: { start: '10:00', end: '16:00' },
      Thursday: { start: '08:00', end: '14:00' },
      Friday: { start: '09:00', end: '13:00' },
      Saturday: null,
      Sunday: null,
    },
    languagesSpoken: ['English', 'Vietnamese'],
    rating: 4.6,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    services: [],
  },
];
