import { Caregiver, CareGiverTypes } from '@app/types/caregiver.types';
import { services } from './services';

export const caregivers: Caregiver[] = Array.from({ length: 20 }, (_, i) => {
  const id = `caregiver-${i + 1}`;
  const type = CareGiverTypes[i % CareGiverTypes.length];
  const firstName = `First${i + 1}`;
  const lastName = `Last${i + 1}`;
  const dateOfBirth = new Date(i, i, i);
  const fullName = `${firstName} ${lastName}`;
  const username = `caregiver${i + 1}`;
  const email = `caregiver${i + 1}@healthapp.com`;
  const gender = i % 2 === 0 ? 'Male' : 'Female';
  const phone = `+**********${i}`;
  const address = {
    street: `Street ${i + 1}`,
    city: `City ${i % 5}`,
    zip: `1000${i}`,
    geoLocation: { lat: 35.0 + i, lng: -120.0 - i },
  };

  return {
    id,
    type,
    firstName,
    lastName,
    fullName,
    username,
    dateOfBirth,
    passwordHash: `hashedpassword${i + 1}`,
    email,
    phone,
    gender,
    nationalId: `ID${1000 + i}`,
    baseAddress: address,
    coverageAreas: [`Area ${i % 3}`, `Zone ${i % 4}`],
    travelRadius: 10 + (i % 5) * 5,
    certifications: [`Cert ${i + 1}`],
    skills: [`Skill ${i + 1}`, `Skill ${i + 2}`],
    specialties: type === 'Doctor' ? ['Cardiology', 'Neurology'] : [],
    availability: {
      Monday: { start: '09:00', end: '17:00' },
      Tuesday: { start: '10:00', end: '16:00' },
      Wednesday: null,
      Thursday: { start: '09:00', end: '15:00' },
      Friday: { start: '12:00', end: '18:00' },
    },
    languagesSpoken: ['English', i % 2 === 0 ? 'Spanish' : 'French'],
    rating: +(Math.random() * 5).toFixed(1),
    active: i % 3 !== 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    services: [services[i], services[i + 1]],
  };
});
