import { Service } from '@app/types/service.types';

export const services: Service[] = [
  {
    id: 1,
    name: 'Physical Therapy',
    estimatedTimeMinutes: 60,
    type: 1,
  },
  {
    id: 2,
    name: 'Wound Care',
    estimatedTimeMinutes: 45,
    type: 2,
  },
  {
    id: 3,
    name: 'Medication Management',
    estimatedTimeMinutes: 30,
    type: 4,
  },
  {
    id: 4,
    name: 'Speech Therapy',
    estimatedTimeMinutes: 50,
    type: 1,
  },
  {
    id: 5,
    name: 'Occupational Therapy',
    estimatedTimeMinutes: 55,
    type: 1,
  },
  {
    id: 6,
    name: 'Dementia Care',
    estimatedTimeMinutes: 90,
    type: 3,
  },
  {
    id: 7,
    name: 'Palliative Care',
    estimatedTimeMinutes: 60,
    type: 3,
  },
  {
    id: 8,
    name: 'Mobility Support',
    estimatedTimeMinutes: 40,
    type: 4,
  },
  {
    id: 9,
    name: 'IV Therapy',
    estimatedTimeMinutes: 35,
    type: 1,
  },
  {
    id: 10,
    name: 'Respiratory Therapy',
    estimatedTimeMinutes: 45,
    type: 1,
  },
  {
    id: 11,
    name: 'Post-Surgical Care',
    estimatedTimeMinutes: 50,
    type: 3,
  },
  {
    id: 12,
    name: 'Pain Management',
    estimatedTimeMinutes: 40,
    type: 2,
  },
  {
    id: 13,
    name: 'Chronic Disease Monitoring',
    estimatedTimeMinutes: 60,
    type: 4,
  },
  {
    id: 14,
    name: 'Nutrition Support',
    estimatedTimeMinutes: 30,
    type: 4,
  },
  {
    id: 15,
    name: 'Wound Dressing',
    estimatedTimeMinutes: 20,
    type: 2,
  },
  {
    id: 16,
    name: 'Fall Prevention',
    estimatedTimeMinutes: 25,
    type: 4,
  },
  {
    id: 17,
    name: 'Diabetes Care',
    estimatedTimeMinutes: 40,
    type: 1,
  },
  {
    id: 18,
    name: 'Medication Reminders',
    estimatedTimeMinutes: 10,
    type: 4,
  },
  {
    id: 19,
    name: 'Rehabilitation Assistance',
    estimatedTimeMinutes: 60,
    type: 4,
  },
  {
    id: 20,
    name: 'Personal Hygiene Support',
    estimatedTimeMinutes: 35,
    type: 4,
  },
  {
    id: 21,
    name: 'Meal Preparation',
    estimatedTimeMinutes: 30,
    type: 4,
  },
  {
    id: 22,
    name: 'Companionship',
    estimatedTimeMinutes: 60,
    type: 4,
  },
  {
    id: 23,
    name: 'Mobility Assistance',
    estimatedTimeMinutes: 45,
    type: 4,
  },
  {
    id: 24,
    name: 'Monitoring Vital Signs',
    estimatedTimeMinutes: 15,
    type: 3,
  },
  {
    id: 25,
    name: 'Postpartum Support',
    estimatedTimeMinutes: 70,
    type: 4,
  },
  {
    id: 26,
    name: 'Infant Care',
    estimatedTimeMinutes: 50,
    type: 3,
  },
  {
    id: 27,
    name: 'Catheter Care',
    estimatedTimeMinutes: 20,
    type: 3,
  },
  {
    id: 28,
    name: 'Colostomy Management',
    estimatedTimeMinutes: 25,
    type: 1,
  },
  {
    id: 29,
    name: "Alzheimer's Care",
    estimatedTimeMinutes: 90,
    type: 3,
  },
  {
    id: 30,
    name: 'Daily Living Assistance',
    estimatedTimeMinutes: 60,
    type: 4,
  },
];
