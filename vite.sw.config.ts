import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    lib: {
      entry: 'src/service-workers/firebase-messaging-sw.ts',
      name: 'ServiceWorker',
      fileName: 'firebase-messaging-sw',
      formats: ['iife'],
    },
    outDir: 'public',
    emptyOutDir: false,
    rollupOptions: {
      output: {
        entryFileNames: 'firebase-messaging-sw.js',
      },
    },
  },
});
