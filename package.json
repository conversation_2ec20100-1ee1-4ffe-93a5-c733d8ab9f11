{"name": "homecare", "private": true, "version": "0.2.0", "type": "module", "scripts": {"dev": "npm run build:sw:dev && vite", "dev:watch": "concurrently \"npm run build:sw:watch\" \"vite\"", "build": "npm run build:sw && tsc && vite build --mode production", "build:sw": "vite build --config vite.sw.config.ts", "build:sw:dev": "esbuild src/service-workers/firebase-messaging-sw.ts --bundle --outfile=public/firebase-messaging-sw.js --target=es2020 --format=iife", "build:sw:watch": "esbuild src/service-workers/firebase-messaging-sw.ts --bundle --outfile=public/firebase-messaging-sw.js --target=es2020 --format=iife --watch", "preview": "vite preview", "prepare": "husky", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix --max-warnings 50", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "api:generate": "orval", "api:auto-generate": "npx orval --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "generate-api": "tsx generateOpenApi.ts && npm run type-check", "type-check": "tsc --noEmit"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.17", "@googlemaps/js-api-loader": "^1.16.10", "@sentry/react": "^9.36.0", "@sentry/vite-plugin": "^2.22.4", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.81.2", "antd": "^5.24.1", "antd-style": "^3.7.1", "axios": "^1.4.0", "dayjs": "^1.11.10", "eslint-plugin-prettier": "^5.2.6", "firebase": "^11.3.0", "firebase-admin": "^13.1.0", "formik": "^2.4.5", "fullcalendar": "^6.1.17", "idb-keyval": "^6.2.1", "oidc-client-ts": "^3.3.0", "picomatch": "^4.0.2", "qs": "^6.14.0", "react": "19.1.1", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.1.1", "react-draggable": "^4.5.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.58.1", "react-icons": "^5.3.0", "react-oidc-context": "^3.3.0", "react-query": "3.38.0", "react-router-dom": "^6.9.0", "rrule": "^2.8.1", "simplebar-react": "3.3.0", "yup": "^1.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@storybook/react-vite": "^9.0.13", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.19", "@types/node": "20.3.1", "@types/react": "19.1.12", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "19.1.9", "@types/react-helmet": "^6.1.11", "@types/simplebar": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.19", "babel-jest": "^29.7.0", "concurrently": "^9.2.1", "esbuild": "^0.25.9", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-storybook": "^9.0.13", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.1", "postcss": "^8.4.38", "prettier": "^3.5.3", "sass-embedded": "^1.87.0", "storybook": "^9.0.13", "swagger-typescript-api": "^13.2.7", "tailwindcss": "^4.1.10", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.0.2", "vite": "^6.2.1", "vite-plugin-checker": "^0.9.1", "vite-tsconfig-paths": "^5.1.4"}, "overrides": {"meow": "13.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}}