import type { Config } from 'tailwindcss';

export default {
  // Helps the VS Code Tailwind CSS IntelliSense extension know your source files
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx,html}'],
  theme: {
    // IntelliSense-only: mirror your CSS @theme tokens for autocomplete
    extend: {
      colors: {
        'app-white': 'var(--color-app-white)',
        'app-gray-light': 'var(--color-app-gray-light)',
        'app-gray': 'var(--color-app-gray)',
        'app-gray-dark': 'var(--color-app-gray-dark)',
        'app-primary': 'var(--color-app-primary)',
        'app-secondary': 'var(--color-app-secondary)',
        'app-secondary-light': 'var(--color-app-secondary-light)',
        'app-secondary-extra-light': 'var(--color-app-secondary-extra-light)',
        'app-secondary-extra-lightest': 'var(--color-app-secondary-extra-lightest)',
        'app-text-dark': 'var(--color-app-text-dark)',
        'app-text': 'var(--color-app-text)',
        'app-text-light': 'var(--color-app-text-light)',
        'app-danger': 'var(--color-app-danger)',
        'app-warning': 'var(--color-app-warning)',
        'app-info': 'var(--color-app-info)',
        'app-success': 'var(--color-app-success)',
      },
    },
  },
  plugins: [],
} satisfies Config;
