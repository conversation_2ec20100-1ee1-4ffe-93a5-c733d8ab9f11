### STAGE 1: Build ###
FROM node:20.17.0-alpine AS builder
RUN echo "Node version:" && node --version && echo "NPM version:" && npm --version


ARG APP_VERSION
ARG SENTRY_AUTH_TOKEN

ENV APP_VERSION=$APP_VERSION
ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN

RUN npm -v

RUN npm config set registry https://registry.npmjs.org/

ENV IS_DOCKER_BUILD=true

WORKDIR /app

COPY package.json package-lock.json ./

RUN npm install --legacy-peer-deps

COPY . .

RUN npm run build

### STAGE 2: Production Environment ###
FROM nginx:1.28-alpine AS production

RUN echo "APP_VERSION mode 2: $APP_VERSION"

COPY --from=builder /app/dist /usr/share/nginx/html

COPY ./nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

COPY ./replaceEnvVars.sh /
RUN chmod +x replaceEnvVars.sh

RUN ["chmod", "+x", "/docker-entrypoint.sh"]
CMD /replaceEnvVars.sh && nginx -g "daemon off;"